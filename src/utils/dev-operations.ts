import {WebContainer} from "@webcontainer/api";
import {Terminal} from "@xterm/xterm";

export async function installDependencies(instance: WebContainer, terminal: Terminal) {
  const installProcess = await instance.spawn('npm', ['install']);
  terminal.write("依赖安装中...");
  installProcess.output.pipeTo(new WritableStream({
    write(data) {
      terminal.write(data);
    }
  }));
  const exitCode = await installProcess.exit;
  if (exitCode == 0) {
    terminal.write("依赖安装完毕，准备运行\n");
    return true;
  } else {
    terminal.write(`依赖安装失败，报错信息如上`);
    return false;
  }
}

export async function startDevServer(instance: WebContainer, terminal: Terminal, callback: (url: string) => void) {
  instance.on('server-ready', (_port, url) => {
    terminal.write("服务启动完毕，即将进行加载");
    setTimeout(() => {
      callback(url);
    }, 500)
  });

  terminal.write("启动服务中...");
  const serverProcess = await instance.spawn('npm', ['run', 'start']);
  serverProcess.output.pipeTo(
    new WritableStream({
      write(data) {
        terminal.write(data);
      },
    })
  )
  const exitCode = await serverProcess.exit;
  if (exitCode != 0) {
    terminal.write(`服务启动失败，报错信息如上`);
    return false;
  }
}

export async function writeIndexJS(instance: WebContainer, content: string) {
  await instance.fs.writeFile('/index.js', content);
}

