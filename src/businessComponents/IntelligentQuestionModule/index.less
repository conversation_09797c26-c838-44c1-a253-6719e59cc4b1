.intelligent-question-module {
  width: 100%;
  height: 100%;
  // background: #f5f5f5;
  padding: 50px 20px 20px;
  
  .interview-container {
    gap: 30px;
    display: flex;
    height: 100%;
    // min-height: 600px;
    
    .left-section {
      // flex: 1;
      width: 40%;
      background: white;
      border-radius: 8px;
      // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      
      .left-tabs {
        height: 100%;
        
        .ant-tabs-content-holder {
          height: calc(100% - 46px);
          overflow-y: auto;
        }
        
        .ant-tabs-tabpane {
          height: 100%;
          // padding: 20px;
        }
      }
    }
    
    .right-section {
      flex: 1;
      background: white;
      border-radius: 8px;
      // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      display: flex;
      flex-direction: column;
      
      .right-tabs {
        flex: 1; // Changed from height: 100%
      }

      
      .question-content {
        padding: 10px;
        height: 100%;
        overflow-y: auto;
        
        .component-wrapper {
          height: 100%;
        }
      }
      .question-content::-webkit-scrollbar {
        display: none;
      }
      
      // 题目内容样式
      .question-content {
        .question-generation {
          h3 {
            color: #1890ff;
            margin-bottom: 16px;
            font-size: 16px;
          }
          
          .question-item {
            margin-bottom: 24px;
            padding: 16px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 3px solid #1890ff;
            
            h4 {
              color: #262626;
              margin-bottom: 12px;
              font-size: 14px;
              line-height: 1.6;
            }
            
            .question-details {
              p {
                margin-bottom: 8px;
                color: #666;
                font-size: 13px;
                
                &:last-child {
                  margin-bottom: 0;
                }
              }
            }
          }
        }
      }
    }
  }
}

