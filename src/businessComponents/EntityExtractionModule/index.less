.entity-extraction{
  .split-preview-card{
    margin-bottom: var(--ant-margin-sm);
    padding: 10px 0px 20px;
    .ant-card-body{
      padding: var(--ant-margin);
    }
    .ant-card-head{
      border-bottom:0px;
      min-height: 42px;
      padding:0px 20px 0px 0px;
      .title-tips{
       >span:nth-child(1){
        width:4px;
        height: 16px;
        border-radius: 2px;
        margin-left: -1px;
        background: var(--ant-color-primary);
        margin-right: var(--ant-margin);
       }
      }
    }
    .ant-card-body{
      margin-bottom:0px;
      padding:0px 20px;
      margin-top:var(--ant-margin-xs);
      >div:last-child{
        margin-bottom:0px !important;
      }
    }
  }
  .entity-file-list{
    background: var(--ant-color-bg-container);
    padding: 0px 35px 0px 50px;
    height: 65px;
    .ant-tabs-nav{
      height: 55px;
      margin-bottom: 18px;
      &::before{
        border-bottom:0px;
      }
    }
    .ant-tabs-tab-btn{
      font-size: var(--ant-font-size-lg);
      font-weight: bold;
      color:#333;
    }
  }
  .entity-file-view{
    background: var(--ant-color-bg-container);
    height: 65px;
    .ant-tabs-nav{
      height: 55px;
      &::before{
        border-bottom:0px;
      }
    }
    .ant-tabs-tab-btn{
      font-size: var(--ant-font-size-lg);
      font-weight: bold;
      color:#333;
    }
  }
  .entity-right{
    padding:0px 30px;
    background: var(--ant-color-bg-container);
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.02),0px 1px 6px -1px rgba(0, 0, 0, 0.02),0px 1px 2px 0px rgba(0, 0, 0, 0.03);
    .contract-type{
      height: 55px;
      color:#333;
      margin-left: var(--ant-margin);
      // position: absolute;
      // right: 30px;
      // top: 0px;
      span{
        font-weight:bold;
      }
    }
    // .ant-tabs-ink-bar{
    //   width:0px !important;
    // }
  }
}
.rule-modal-style{
  .ant-tabs-nav{
    &::before {
      border-bottom:0px;
    }
  }
  .ant-tabs-tab{
    font-size: var(--ant-font-size-lg);
    color: #333;
  }
  .ant-modal-content{
    padding: 10px 30px 30px;
    .ant-form-item-label{
      font-size: var(--ant-font-size-lg);
      font-weight: 500;
      color: #333;
      padding:0px;
    }
    .title{
      font-size: var(--ant-font-size-lg);
      font-weight: 500;
      line-height: var(--ant-line-height-lg);
      letter-spacing: 0.59px;
      color: #333333;
    }
  }
}

.my-split-docx-preview-wrapper{
  background: var(--ant-color-bg-container) !important;
  height: calc(100vh - 215px);
  padding: 20px !important;
  box-shadow: 0 0 5px 2px rgba(0, 0, 0, 0.06);
  border-radius: 6px;
  margin: 5px;
  .docx-wrapper>section.docx{
    padding: 10px 20px;
  }
  .my-split-docx-preview{
    width: 100% !important;
    margin-bottom:0px !important;
    padding: 0px !important;
    height: calc(100vh - 215px);
    overflow: auto;
    box-shadow: none !important;
    min-height:0px !important;
  }
}
 