import React, { useState, useRef, useImperativeHandle, forwardRef, Suspense, useMemo, useCallback, memo } from 'react';
import { Tabs } from 'antd';
// import type { TabsProps } from 'antd';
import './index.less';

// 顶层懒加载，避免每次渲染都重新创建组件，导致重复挂载
const AnalysisModuleLazy = React.lazy(() => import('../ResumeAnalysisModule/component/AnalysisModule'));
const JobMatchingModuleLazy = React.lazy(() => import('../ResumeAnalysisModule/component/JobMatchingModule'));
const QuestionGenerationLazy = React.lazy(() => import('../IntelligentQuestionModule/component/QuestionModule'));

// 独立的简历预览组件，保持引用稳定
const ResumePreview: React.FC<any> = (props) => {
  const fileData = props?.viewData?.[0];
  const fileUrl = fileData?.url || fileData?.fileStr || '';
  return (
    <div className="resume-preview">
      {fileUrl ? (
        <embed
          style={{ width: '100%', height: '100%', minHeight: 'calc(100vh - 160px)' }}
          type='application/pdf'
          src={fileUrl + '#toolbar=0&navpanes=0&scrollbar=0'}
        />
      ) : (
        <div className="no-file">
          <h3>简历预览</h3>
          <p>请先上传简历文件</p>
        </div>
      )}
    </div>
  );
};

export interface TabItem {
  title: string;
  content: string;
  key: string;
  props?: any;
}

export interface TabContainerRef {
  // getActiveTab: () => string;
  // setActiveTab: (key: string) => void;
  // getTabData: () => any;
  // refreshTab: (key: string) => void;
}

interface TabContainerProps {
  tabs: TabItem[];
  className?: string;
  defaultActiveKey?: string;
  stepIndex?: number; // 新增属性，用于传递步骤索引
}

const TabContainer = forwardRef<TabContainerRef, TabContainerProps>(({ 
  tabs, 
  className = '',
  defaultActiveKey,
  stepIndex = 0, // 默认步骤索引为0
}, ref) => {
  const [activeTab, setActiveTab] = useState(defaultActiveKey || tabs[0]?.key);

  // 组件映射表 - 仅创建一次，避免反复创建导致子组件重复挂载
  const componentMap: Record<string, React.ComponentType<any>> = useMemo(() => ({
    'ResumePreview': ResumePreview,
    'ResumeAnalysis': (props: any) => {
      console.log('ResumeAnalysis props:我是第二步的tab', props);
      return (
        <Suspense fallback={<div>加载中...</div>}>
          <AnalysisModuleLazy {...props} />
        </Suspense>
      );
    },
    'JobMatching': (props: any) => {
      console.log('JobMatching props:我是第三步的tab', props);
      return (
        <Suspense fallback={<div>加载中...</div>}>
        <JobMatchingModuleLazy {...props} />
      </Suspense>
      )
      
  },
    'QuestionGeneration': (props: any) => (
      <Suspense fallback={<div>加载中...</div>}>
        <QuestionGenerationLazy {...props} />
      </Suspense>
    )
  }), []);

  useImperativeHandle(ref, () => ({
  }));

  const handleTabChange = useCallback((key: string) => {
    setActiveTab(key);
  }, []);

  // 根据 stepIndex 过滤要显示的标签页
  const visibleTabs = useMemo(() => {
    if (stepIndex === 0) {
      return tabs.filter(tab => tab.content === 'ResumePreview');
    } else if (stepIndex === 1) {
      return tabs.filter(tab => ['ResumePreview', 'ResumeAnalysis', 'JobMatching'].includes(tab.content));
    } else if (stepIndex >= 2) {
      return tabs.filter(tab => ['ResumePreview', 'ResumeAnalysis', 'JobMatching', 'QuestionGeneration'].includes(tab.content));
    }
    // else if (stepIndex === 5) {
    //   return tabs.filter(tab => ['ResumePreview', 'ResumeAnalysis', 'JobMatching', 'QuestionGeneration'].includes(tab.content));
    // }
    return tabs;
  }, [stepIndex, tabs]);

  // 确保 activeTab 始终指向第一个可见标签页
  const currentActiveTab = visibleTabs.find(tab => tab.key === activeTab) ? activeTab : visibleTabs[0]?.key;

  // 预生成 items，保持引用稳定
  const items = useMemo(() => visibleTabs.map(tab => ({
    key: tab.key,
    label: tab.title,
    children: (
      <div className="tab-content">
        {componentMap[tab.content] ? (
          <div className="tab-component-wrapper">
            {React.createElement(componentMap[tab.content], {
              ...tab.props,
            })}
          </div>
        ) : (
          <div className="tab-not-found">
            组件 {tab.content} 未找到
            <p>请在componentMap中添加对应的组件映射</p>
          </div>
        )}
      </div>
    )
  })), [visibleTabs, componentMap]);
  
  return (
    <div className={`tab-container ${className}`}>
      <Tabs 
        activeKey={currentActiveTab} 
        onChange={handleTabChange}
        items={items}
        destroyInactiveTabPane={false}
        className="dynamic-tabs"
        type="card"
        size="small"
      />
    </div>
  );
});

TabContainer.displayName = 'TabContainer';

export default memo(TabContainer);
