import React from "react";
import "./index.less";
import { Flex } from "antd";

interface HeaderProps {
  mainTitle?: string;
  subTitle?: string;
}

const ContractHeader: React.FC<HeaderProps> = ({
  mainTitle = "合同起草",
  subTitle = "AI根据模版及资料快速起草合同",
}) => {
  const hasMainTitle = !!mainTitle?.trim();
  const hasSubTitle = !!subTitle?.trim();

  return (
    <Flex className="copilot-header" justify="center" vertical align="center">
      {hasMainTitle && (
        <h1 className="main-title" data-testid="main-title">
          {mainTitle}
        </h1>
      )}
      {hasSubTitle && (
        <p className="sub-title" data-testid="sub-title">
          {subTitle}
        </p>
      )}
    </Flex>
  );
};

export default React.memo(ContractHeader); // 使用 memo 优化性能
