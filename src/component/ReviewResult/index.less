.review-result-container {
  width: 100%;
  // max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  background: #f5f5f5;
  height: calc(100vh - 125px);
  overflow-y: auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;

  // 评审结果组件样式
  .review-result {
    background: white;
    border-radius: 8px;
    padding: 24px;
    // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    width: 50%;
    margin: 0 auto;

    .review-header {
      text-align: center;
      margin-bottom: 24px;

      .review-title {
        font-size: 22px;
        font-weight: 600;
        color: #333;
        margin: 0;
        text-align: left;
      }
    }

    .review-steps {
      // margin-bottom: 24px;

      .ant-steps-vertical {
        .ant-steps-item {
          .ant-steps-item-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;

            .step-icon {
              font-size: 16px;
              
              &.finish {
                color: #52c41a;
              }
            }
          }

          .ant-steps-item-content {
            .ant-steps-item-title {
              font-size: 14px;
              color: #333;
              font-weight: 500;
            }

            .ant-steps-item-description {
              font-size: 12px;
              color: #666;
              margin-top: 4px;
            }
          }
        }
      }
    }

    .result-card {
      margin-bottom: 24px;
      // border: 1px solid #e8e8e8;
      border-radius: 6px;
      margin-left: 30px;
      margin-right: 20px;
      background-color: #FAFAFA;

      .ant-card-body {
        padding: 20px;
      }

      .result-content {
        .result-row {
          display: flex;
          align-items: flex-start;
          margin-bottom: 16px;

          &:last-child {
            margin-bottom: 0;
          }

          .result-label {
            font-size: 14px;
            color: #333;
            font-weight: 500;
            margin-right: 12px;
            min-width: 80px;
          }

          .result-tag {
            font-size: 12px;
            padding: 4px 12px;
            border-radius: 12px;
            font-weight: 500;

            &.ant-tag-success {
              background: #f6ffed;
              border-color: #b7eb8f;
              color: #52c41a;
            }

            &.ant-tag-error {
              background: #fff2f0;
              border-color: #ffccc7;
              color: #ff4d4f;
            }
          }

          .result-description {
            font-size: 14px;
            color: #666;
            line-height: 1.5;
          }
        }
      }
    }

    .review-actions {
      display: flex;
      gap: 12px;
      justify-content: center;

      .action-button {
        min-width: 120px;
        height: 40px;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;

        &.ant-btn-primary {
          background: #1890ff;
          border-color: #1890ff;
        }

        &.ant-btn-default {
          border-color: #d9d9d9;
          color: #333;
        }
      }
    }
  }

  // 场景规划组件样式
  .scenario-planning {
    width: 50%;
    margin: 0 auto;
    background: white;
    border-radius: 8px;
    padding: 24px;
    margin-top: 20px;
    height: 100%;
    // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .scenario-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
      padding-bottom: 16px;
      // border-bottom: 1px solid #f0f0f0;

      .scenario-title {
        font-size: 22px;
        font-weight: 600;
        color: #333;
        margin: 0;
      }

      .pass-button {
        background: #52c41a;
        border-color: #52c41a;
        border-radius: 6px;
        font-size: 12px;
        height: 28px;
        padding: 0 12px;
      }
    }

    .scenario-content {
      margin-bottom: 24px;

      .scenario-step {
        margin-bottom: 20px;
        border: 1px solid #eee;
        border-radius: 8px;
        padding: 20px;
        position: relative;

        &:last-child {
          margin-bottom: 0;
        }

          .step-header {
           display: flex;
           align-items: center;
           margin-bottom: 12px;
           flex-wrap: wrap;
           gap: 8px;
           border-bottom:none;

           .step-title-section {
             display: flex;
             align-items: center;
             justify-content: space-between;
             gap: 12px;
             flex: 1;

             .step-title {
               font-size: 16px;
               font-weight: 600;
               color: #333;
               margin: 0;
             }

             .unit-scene-button {
               background: #fff7e6;
               border-color: #ffd591;
               color: #fa8c16;
               font-size: 11px;
               height: 24px;
               padding: 0 8px;
               border-radius: 4px;
             }
           }

           .step-role {
             font-size: 12px;
             color: #1890ff;
             background: #f0f8ff;
             padding: 2px 8px;
             border-radius: 4px;
             border: 1px solid #d6e4ff;
           }
         }

        .step-content {
          //  padding-left: 16px;
          //  position: relative;

           .step-input,
           .step-output {
             font-size: 13px;
             color: #666;
             margin-bottom: 8px;
             line-height: 1.4;
             display: flex;
             align-items: flex-start;
             gap: 8px;
             padding-bottom: 10px;

             strong {
               color: #333;
               font-weight: 500;
               min-width: 40px;
             }

             .editing-title,
             .editing-input,
             .editing-output {
               flex: 1;
               font-size: 13px;
               border: 1px solid #d9d9d9;
               border-radius: 4px;
               padding: 4px 8px;
               background: white;

               &:focus {
                 border-color: #1890ff;
                 box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
               }

               &::placeholder {
                 color: #bfbfbf;
                 font-size: 12px;
               }
             }

             .editing-title {
               font-weight: 600;
               color: #333;
             }
           }

           .step-edit-section {
             position: absolute;
             bottom: 10px;
             right: 20px;
             display: flex;
             gap: 4px;

             .edit-button {
               font-size: 12px;
               height: 24px;
               padding: 0 4px;
               border: none;
               background: transparent;
               color: #666;
             }

             .edit-actions {
               display: flex;
               gap: 4px;

               .save-button {
                //  background: #52c41a;
                //  border-color: #52c41a;
                 color: white;
                 font-size: 11px;
                 height: 24px;
                 padding: 0 8px;
                 border-radius: 4px;
               }

               .cancel-button {
                 background: #f5f5f5;
                 border-color: #d9d9d9;
                 color: #666;
                 font-size: 11px;
                 height: 24px;
                 padding: 0 8px;
                 border-radius: 4px;
               }
             }
           }
         }

        .step-divider {
          height: 1px;
          background: #f0f0f0;
          margin: 16px 0;
        }
      }
    }

    .scenario-footer {
      .confirm-button {
        background: #1890ff;
        border-color: #1890ff;
        height: 48px;
        font-size: 16px;
        font-weight: 500;
        border-radius: 6px;
      }
    }
    .scenario-stream {
      height: calc(100vh - 550px);
      overflow: auto;
    }
  }
}

.review-result-container::-webkit-scrollbar {
  display: none;
}

// 弹框信息
.review-result-modal{
  .review-steps {
    .ant-steps-vertical {
      .ant-steps-item {
        .ant-steps-item-icon {
          width: 24px;
          height: 24px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 12px;

          .step-icon {
            font-size: 16px;
            
            &.finish {
              color: #52c41a;
            }
          }
        }

        .ant-steps-item-content {
          .ant-steps-item-title {
            font-size: 14px;
            color: #333;
            font-weight: 500;
          }

          .ant-steps-item-description {
            font-size: 12px;
            color: #666;
            margin-top: 4px;
          }
        }
      }
    }
  }
}