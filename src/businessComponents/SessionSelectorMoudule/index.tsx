import React, { useState, useEffect } from 'react'
import { Select, Button, Space, Typography, message, Spin, Empty } from 'antd'
import { PlusOutlined, ReloadOutlined, UserOutlined, CalendarOutlined } from '@ant-design/icons'
import { intelligentInterview } from '@/api/intelligentInterview'
import dayjs from 'dayjs'

const { Text } = Typography
const { Option } = Select

// 会话状态类型定义
export type SessionStatus = 'in_progress' | 'completed' | 'cancelled' | 'pending'

// 会话数据接口
export interface SessionData {
  id: string
  candidate_name: string
  status: SessionStatus
  created_at: string
  position?: string
  company?: string
  [key: string]: any
}

// 组件配置接口
export interface SessionSelectorConfig {
  showCreateNew?: boolean
  showRefresh?: boolean
  showStatus?: boolean
  showDate?: boolean
  showPosition?: boolean
  showCompany?: boolean
  maxHeight?: string | number
  theme?: 'default' | 'compact' | 'detailed'
  emptyText?: string
  loadingText?: string
}

// 组件属性接口
export interface SessionSelectorProps {
  onSessionSelect: (sessionId: string | null, sessionData?: SessionData) => void
  currentSessionId?: string
  config?: SessionSelectorConfig
  className?: string
  style?: React.CSSProperties
  disabled?: boolean
  placeholder?: string
  size?: 'small' | 'middle' | 'large'
}

const SessionSelector: React.FC<SessionSelectorProps> = ({
  onSessionSelect,
  currentSessionId,
  config = {},
  className = '',
  style = {},
  disabled = false,
  placeholder = '选择面试会话',
  size = 'middle'
}) => {
  const [sessions, setSessions] = useState<SessionData[]>([])
  const [loading, setLoading] = useState(false)
  const [selectedSessionId, setSelectedSessionId] = useState<string | undefined>(currentSessionId)

  // 默认配置
  const defaultConfig: Required<SessionSelectorConfig> = {
    showCreateNew: true,
    showRefresh: true,
    showStatus: true,
    showDate: true,
    showPosition: true,
    showCompany: true,
    maxHeight: '400px',
    theme: 'default',
    emptyText: '暂无面试会话',
    loadingText: '加载中...'
  }

  const finalConfig = { ...defaultConfig, ...config }

  // 加载会话列表
  const loadSessions = async () => {
    setLoading(true)
    try {
      const sessionList = await intelligentInterview.getSessionList()
      setSessions(sessionList)
    } catch (error) {
      message.error('加载面试会话列表失败')
      console.error('加载会话列表失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 组件挂载时加载会话列表
  useEffect(() => {
    loadSessions()
  }, [])

  // 当currentSessionId变化时更新选中状态
  useEffect(() => {
    setSelectedSessionId(currentSessionId)
  }, [currentSessionId])

  // 处理会话选择
  const handleSessionChange = async (value: string) => {
    if (value === 'new') {
      // 创建新会话
      setSelectedSessionId(undefined)
      onSessionSelect(null)
      return
    }

    setSelectedSessionId(value)
    
    try {
      // 获取完整的会话数据
      const sessionData = await intelligentInterview.getSessionData(value)
      console.log('加载的会话数据:', sessionData)
      onSessionSelect(value, sessionData)
      message.success('会话数据加载成功')
    } catch (error) {
      message.error('加载会话数据失败')
      console.error('加载会话数据失败:', error)
    }
  }

  // 获取会话状态显示文本
  const getStatusInfo = (status: SessionStatus) => {
    const statusMap: Record<SessionStatus, { text: string; color: string; bgColor: string; icon?: React.ReactNode }> = {
      'in_progress': { text: '进行中', color: '#1890ff', bgColor: '#e6f7ff' },
      'completed': { text: '已完成', color: '#52c41a', bgColor: '#f6ffed' },
      'cancelled': { text: '已取消', color: '#ff4d4f', bgColor: '#fff2f0' },
      'pending': { text: '待开始', color: '#faad14', bgColor: '#fffbe6' }
    }
    return statusMap[status] || { text: status, color: '#666', bgColor: '#f5f5f5' }
  }

  // 渲染会话选项
  const renderSessionOption = (session: SessionData) => {
    const statusInfo = getStatusInfo(session.status)
    
    return (
      <Option
        key={session.id}
        value={session.id}
        label={session.candidate_name}
      >
        <div style={{
          padding: '8px 0',
          width: '100%'
        }}>
          <div style={{
            fontWeight: 500,
            fontSize: 13,
            color: '#262626',
            marginBottom: 4,
            display: 'flex',
            alignItems: 'center'
          }}>
            <UserOutlined style={{ marginRight: 6, fontSize: 12, color: '#1890ff' }} />
            {session.candidate_name}
          </div>
          
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            flexWrap: 'wrap',
            gap: 8
          }}>
            {finalConfig.showDate && (
              <div style={{
                fontSize: 11,
                color: '#8c8c8c',
                display: 'flex',
                alignItems: 'center'
              }}>
                <CalendarOutlined style={{ marginRight: 4, fontSize: 10 }} />
                {dayjs(session.created_at).format('MM-DD HH:mm')}
              </div>
            )}
            
            {finalConfig.showPosition && session.position && (
              <div style={{
                fontSize: 11,
                color: '#666',
                maxWidth: '120px',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap'
              }}>
                {session.position}
              </div>
            )}
            
            {finalConfig.showStatus && (
              <div style={{
                fontSize: 10,
                color: statusInfo.color,
                padding: '2px 6px',
                background: statusInfo.bgColor,
                borderRadius: 8,
                border: `1px solid ${statusInfo.color}`,
                fontWeight: 500,
                whiteSpace: 'nowrap'
              }}>
                {statusInfo.text}
              </div>
            )}
          </div>
        </div>
      </Option>
    )
  }

  // 渲染空状态
  const renderEmpty = () => (
    <Empty
      image={Empty.PRESENTED_IMAGE_SIMPLE}
      description={finalConfig.emptyText}
      style={{ padding: '20px 0' }}
    />
  )

  return (
    <div 
      className={`session-selector ${className}`}
      style={{
        padding: finalConfig.theme === 'compact' ? 12 : 16,
        background: 'white',
        borderRadius: 8,
        border: '1px solid #e8e8e8',
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
        height: 'fit-content',
        ...style
      }}
    >
      <div style={{ marginBottom: finalConfig.theme === 'compact' ? 12 : 16 }}>
        <Text strong style={{ 
          fontSize: finalConfig.theme === 'compact' ? 13 : 14, 
          color: '#262626', 
          display: 'block', 
          marginBottom: 4 
        }}>
          选择面试会话
        </Text>
        <Text type="secondary" style={{
          fontSize: finalConfig.theme === 'compact' ? 11 : 12,
          color: '#8c8c8c',
          lineHeight: 1.4
        }}>
          选择已有会话继续面试流程，或创建新的面试会话
        </Text>
      </div>
      
      <Space direction="vertical" size="small" style={{ width: '100%' }}>
        <Select
          style={{
            width: '100%',
            fontSize: finalConfig.theme === 'compact' ? 12 : 14
          }}
          placeholder={placeholder}
          value={selectedSessionId}
          onChange={handleSessionChange}
          loading={loading}
          showSearch
          optionLabelProp="label"
          size={size}
          disabled={disabled}
          filterOption={(input, option) => {
            const label = option?.label as string
            return label?.toLowerCase().includes(input.toLowerCase())
          }}
          notFoundContent={loading ? <Spin size="small" /> : renderEmpty()}
        >
          {finalConfig.showCreateNew && (
            <Option value="new" label="创建新的面试会话">
              <div style={{
                display: 'flex',
                alignItems: 'center',
                padding: '4px 0',
                color: '#1890ff',
                fontWeight: 500
              }}>
                <PlusOutlined style={{ marginRight: 6, fontSize: 12 }} />
                <span style={{ fontSize: 13 }}>创建新的面试会话</span>
              </div>
            </Option>
          )}
          
          {sessions.map(renderSessionOption)}
        </Select>

        {finalConfig.showRefresh && (
          <Button
            icon={<ReloadOutlined />}
            onClick={loadSessions}
            loading={loading}
            size={finalConfig.theme === 'compact' ? 'small' : 'middle'}
            type="default"
            style={{
              width: '100%',
              borderRadius: 4,
              height: finalConfig.theme === 'compact' ? 28 : 32
            }}
          >
            刷新会话列表
          </Button>
        )}
      </Space>

      {selectedSessionId && selectedSessionId !== 'new' && (
        <div style={{
          marginTop: 12,
          padding: 8,
          background: '#f8f9fa',
          borderRadius: 4,
          border: '1px solid #e9ecef'
        }}>
          <Text type="secondary" style={{ fontSize: 11 }}>
            当前会话：{sessions.find(s => s.id === selectedSessionId)?.candidate_name}
          </Text>
        </div>
      )}
    </div>
  )
}

export default SessionSelector