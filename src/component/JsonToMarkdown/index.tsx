interface JsonToMarkdownProps {
  data: any
  depth?: number
}

const JsonToMarkdown = ({ data, depth = 0 }: JsonToMarkdownProps): string => {
  const convertToMarkdown = (obj: any, currentDepth: number = 0): string => {
    if (typeof obj === 'string') {
      return obj
    }

    if (typeof obj === 'object' && obj !== null) {
      let markdown = ''
      
      Object.keys(obj).forEach((key, index) => {
        const value = obj[key]
        const indent = '  '.repeat(currentDepth)
        
        if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
          // 嵌套对象
          markdown += `${indent}## ${key}\n\n`
          markdown += convertToMarkdown(value, currentDepth + 1)
        } else if (Array.isArray(value)) {
          // 数组
          markdown += `${indent}## ${key}\n\n`
          value.forEach((item, itemIndex) => {
            if (typeof item === 'object' && item !== null) {
              markdown += `${indent}### 项目 ${itemIndex + 1}\n\n`
              markdown += convertToMarkdown(item, currentDepth + 1)
            } else {
              markdown += `${indent}- ${item}\n`
            }
          })
          markdown += '\n'
        } else {
          // 简单值
          if (currentDepth === 0) {
            markdown += `${indent}## ${key}\n\n`
            markdown += `${indent}${value}\n\n`
          } else {
            markdown += `${indent}### ${key}\n\n`
            markdown += `${indent}${value}\n\n`
          }
        }
      })
      
      return markdown
    }
    
    return String(obj)
  }

  const markdownContent = convertToMarkdown(data, depth)
  
  return markdownContent
}

export { JsonToMarkdown }
export default JsonToMarkdown
