<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>代码执行结果</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Microsoft YaHei", sans-serif;
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #1e1e1e;
      color: #fff;
      overflow: hidden;
    }

    .loading-container {
      position: relative;
      text-align: center;
      padding: 24px;
      background: rgba(255, 255, 255, 0.03);
      border-radius: 8px;
      border: 1px solid rgba(24, 136, 255, 0.1);
    }

    /* 主加载动画 */
    .loading-ring {
      display: inline-block;
      position: relative;
      width: 40px;
      height: 40px;
      margin-bottom: 16px;
    }

    .loading-ring:before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border-radius: 50%;
      border: 3px solid transparent;
      border-top-color: #1888ff;
      border-right-color: #1888ff;
      animation: spin 1s linear infinite;
    }

    .loading-text {
      font-size: 14px;
      color: rgba(255, 255, 255, 0.9);
      letter-spacing: 1px;
      margin-top: 8px;
      background: linear-gradient(90deg, #1888ff, #3498ff);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      animation: pulse 1.5s ease-in-out infinite;
    }

    /* 背景装饰 */
    .background-line {
      position: absolute;
      height: 1px;
      width: 100px;
      background: linear-gradient(90deg,
      transparent,
      rgba(24, 136, 255, 0.2),
      transparent
      );
    }

    .line-top {
      top: 0;
      left: 50%;
      transform: translateX(-50%);
    }

    .line-bottom {
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
    }

    /* 动画定义 */
    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }
      100% {
        transform: rotate(360deg);
      }
    }

    @keyframes pulse {
      0%, 100% {
        opacity: 0.6;
      }
      50% {
        opacity: 1;
      }
    }

    /* 发光效果 */
    .glow {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 100px;
      height: 100px;
      background: radial-gradient(
        circle,
        rgba(24, 136, 255, 0.1) 0%,
        transparent 70%
      );
      z-index: -1;
      animation: glow 2s ease-in-out infinite;
    }

    @keyframes glow {
      0%, 100% {
        opacity: 0.3;
      }
      50% {
        opacity: 0.5;
      }
    }
  </style>
</head>
<body>
<div class="loading-container">
  <div class="background-line line-top"></div>
  <div class="background-line line-bottom"></div>
  <div class="glow"></div>
  <div class="loading-ring"></div>
  <div class="loading-text">正在执行</div>
</div>
</body>
</html>
