import {
  Col,
  Row,
  Tabs,
  Card,
  Space,
  Tag,
  theme,
  Flex,
  Typography,
  Result,
} from "antd";
import {
  CheckCircleFilled,
  DownOutlined,
  ExclamationCircleFilled,
  InfoCircleFilled,
  UpOutlined,
  WarningFilled,
} from "@ant-design/icons";
import {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import { uploadChatFile } from "@/api/public";
import { getToken, getUserInfo } from "@/utils/auth";
import useSSEChat from "@/hooks/useSSEChat";
import { fileToBase64 } from "@/utils/common";
import DocxPreview from "@/component/DocxViewer";
import StreamTypewriter from "@/component/StreamTypewriter";
import "./index.less";
import TabPane from "antd/es/tabs/TabPane";
interface MentionsComponentProps {
  agentId?: string;
  setGlobalLoading?: (loading: boolean) => void;
  onCallParent: (type: string, data?: any) => void; // 调用输入输出评审
  onGetReviewInfo: (type: string, data?: any) => void; // 调用场景输出评审
  onIncrement?: () => void; // 子组件调用下一步存储数据
  unitOutPutData?: any; // 父组件传过来的数据，用于回显跟判断有无输入输出通过
  pageInfo?: any; // 页面信息
  unitInputData: {
    targentData: []; // 传过来文件拆分块后选中的数据
    rulesData: []; // 所有的规则
    fileList: []; // 文件信息
    ruleFrom: any; // 之前选择的视角等数据
    originalFile: any; // 文件信息
    selectedRules: []; // 选择的规则
  };
}
export interface MentionsComponentRef {
  triggerSplit: (data: any) => void;
}

const riskLevelColors: Record<string, string> = {
  高风险: "red",
  中风险: "orange",
  低风险: "blue",
};
const { Text } = Typography;
const { useToken } = theme;
const ContractLawReview = forwardRef<
  MentionsComponentRef,
  MentionsComponentProps
>(
  (
    {
      unitInputData = {
        targentData: [],
        rulesData: [],
        ruleFrom: {},
        fileList: [],
        originalFile: null,
        selectedRules: [],
      },
      setGlobalLoading,
      agentId,
      pageInfo,
      onGetReviewInfo, // 调用场景输出评审
      onCallParent,
      onIncrement,
      unitOutPutData,
    },
    ref
  ) => {
    const sseChat = useSSEChat();
    const { token } = useToken();
    const [currentActiveKey, setCurrentActiveKey] = useState("0");
    const [showType, setShowType] = useState("全部");
    const scrollRef = useRef<HTMLDivElement>(null);
    const [messageStr, setMessageStr] = useState<any>(""); // 返回的数据类型
    const [allData, setAllData] = useState<any>([]); // 所有的数据集合
    const [expandedMap, setExpandedMap] = useState<Record<number, boolean>>({}); // 是否展开
    const items = [
      {
        key: "全部",
        label: <span className="tabs-btn"> 全部（{allData.length}） </span>,
        children: null,
        // 可以在这里添加具体的高风险项目列表
        style: { backgroundColor: "#fff0f0" }, // 浅粉色背景
      },
      {
        key: "高风险",
        label: (
          <span className="tabs-btn">
            高风险（
            {allData.filter((x) => x.rule_level === "高风险").length}）
          </span>
        ),
        children: null,
        // 可以在这里添加具体的高风险项目列表
        style: { backgroundColor: "#fff0f0" }, // 浅粉色背景
      },
      {
        key: "中风险",
        label: (
          <span className="tabs-btn">
            中风险（
            {allData.filter((x) => x.rule_level === "中风险").length}）
          </span>
        ),
        children: null,
        style: { backgroundColor: "#f5f5f5" }, // 浅灰色背景
      },
      {
        key: "低风险",
        label: (
          <span className="tabs-btn">
            低风险（
            {allData.filter((x) => x.rule_level === "低风险").length}）
          </span>
        ),
        children: null,
        style: { backgroundColor: "#f5f5f5" }, // 浅灰色背景
      },
      {
        key: "已通过",
        label: (
          <span className="tabs-btn">
            通过（
            {allData.filter((x) => x.rule_level === "已通过").length}）
          </span>
        ),
        children: null,
        style: { backgroundColor: "#f5f5f5" }, // 浅灰色背景
      },
    ];

    // 当 allData 改变时，初始化 expandedMap（默认都展开或都收起）
    useEffect(() => {
      if (allData?.length) {
        const initial: Record<number, boolean> = {};
        allData.forEach((_, idx) => {
          initial[idx] = true; // 默认展开
        });
        setExpandedMap(initial);
      }
    }, [allData]);
    useEffect(() => {
      setGlobalLoading?.(false);
      if (!unitOutPutData?.inputReview) {
        getContractData();
        return;
      } else {
        console.log(unitOutPutData?.allData, 3434444);
        setAllData(unitOutPutData?.allData || []);
        // setMessageStr(
        //   `<listing>${JSON.stringify(unitOutPutData?.allData)}</listing>`
        // );
      }
    }, []);
    const getContractData = async () => {
      setGlobalLoading?.(true);

      // 拼接 str
      let str = "";
      unitInputData?.targentData.forEach((item: any) => {
        str += `${item.title},${item.text}`;
      });

      const results: any[] = []; // 用来收集每次 getLawData 的结果
      const data: any = []; // 选中的规则数据
      unitInputData?.rulesData?.forEach((item) => {
        if (unitInputData.selectedRules.includes(item.id)) {
          data.push(item);
        }
      });

      for (let i = 0; i < data?.length; i++) {
        if (i === 0 && !unitOutPutData?.inputReview) {
          onCallParent?.("输入", `${str},${data[0]?.title}`);
          return;
        } else {
          const res: any = await getLawData(data[i]?.title);
          const cleanRes = res
            .replace(/```json\s*|```$/g, "")
            .trim()
            .replace(/```/g, "")
            .trim();
          const jsonObject = JSON.parse(cleanRes)?.contract_review;
          results.push(jsonObject); // 👉 先收集起来
        }
      }

      // 👉 循环完统一更新
      if (results.length > 0) {
        const flatResults = results.flat();
        // setAllData((prev: any) => ({ ...prev, ...flatResults }));
        // setAllData(flatResults);
        console.log(flatResults, 9999111);
        // 如果要拼接到 messageStr
        if (!unitOutPutData?.outputReview) {
          onCallParent?.("输出", JSON.stringify(flatResults));
          onGetReviewInfo?.("输出", JSON.stringify(flatResults));
        } else {
          setGlobalLoading?.(false);
        }
        onIncrement?.(); // 调用下一步存储数据
      }
    };

    // 调取dify
    const getLawData = async (text: any) => {
      let str: any = "";
      unitInputData?.targentData.forEach((item: any) => {
        str += `${item.title},${item.text}`; // 用反引号拼接
      });
      const tokenInfo = await getToken();
      const userInfo = await getUserInfo();
      setMessageStr("");
      return new Promise((resolve) => {
        sseChat.start({
          url: "/dify/broker/agent/stream",
          headers: {
            "Content-Type": "application/json",
            Token: tokenInfo || "",
          },
          body: {
            insId: "1",
            bizType: "app:agent",
            bizId: agentId || "",
            agentId: agentId || "",
            path: "/chat-messages",
            difyJson: {
              inputs: {
                type: "规则审核",
              },
              pageinfo: pageInfo,
              response_mode: "streaming",
              user: userInfo?.id || "anonymous",
              conversation_id: "",
              query: `${str},${text}`,
            },
          },
          query: {},
          onMessage: (res: any) => {
            const cleanRes = res
              .replace(/```json\s*|```$/g, "")
              .trim()
              .replace(/```/g, "")
              .trim();
            setMessageStr(cleanRes);
          },
          onFinished: (resultData: any) => {
            setMessageStr("");
            const cleanRes = resultData
              .replace(/```json\s*|```$/g, "")
              .trim()
              .replace(/```/g, "")
              .trim();
            const jsonObject = JSON.parse(cleanRes)?.contract_review;
            setAllData((prevData) => [...prevData, ...jsonObject]);
            resolve(resultData); // 将结果传出去
          },
        });
      });
    };

    // 导出文件
    const exportContract = async () => {
      setGlobalLoading?.(true);
      const tokenInfo = await getToken();
      const userInfo = await getUserInfo();
      const fileData = {
        fileName: unitInputData?.originalFile?.name,
        fileStr: await fileToBase64(unitInputData?.originalFile),
        path: "/files/upload",
        agentId: agentId,
        user: userInfo?.id,
        libName: unitInputData?.originalFile?.name,
        libDesc: "",
        flag: "file",
      };
      uploadChatFile(fileData).then(async (response: any) => {
        const fileData: any = [];
        fileData.push({
          type: "document",
          transfer_method: "local_file",
          upload_file_id: response?.data.id,
        });
        if (response.code == 200) {
          return new Promise((resolve) => {
            sseChat.start({
              url: "/dify/broker/agent/stream",
              headers: {
                "Content-Type": "application/json",
                Token: tokenInfo || "",
              },
              body: {
                insId: "1",
                bizType: "app:agent",
                bizId: agentId || "",
                agentId: agentId || "",
                path: "/chat-messages",
                difyJson: {
                  inputs: {
                    type: "文件下载",
                  },
                  pageinfo: pageInfo,
                  response_mode: "streaming",
                  user: userInfo?.id || "anonymous",
                  conversation_id: "",
                  query: JSON.stringify(allData),
                  files: fileData,
                },
              },
              query: {},
              onMessage: () => {},
              onFinished: (res: any) => {
                const parenthesesContent = res.match(/\((.*?)\)/);
                const parenthesesResult = parenthesesContent
                  ? parenthesesContent[1]
                  : null;

                // 提取[]中的内容
                const squareBracketsContent = res.match(/\[(.*?)\]/);
                const squareBracketsResult = squareBracketsContent
                  ? squareBracketsContent[1]
                  : null;

                if (parenthesesResult && squareBracketsResult) {
                  const link = document.createElement("a");
                  link.href = parenthesesResult;
                  link.download = `${squareBracketsResult}`;
                  document.body.appendChild(link);
                  link.click();
                  link.remove();
                }
                setGlobalLoading?.(false);
              },
            });
          });
        }
      });
    };
    useImperativeHandle(ref, () => ({
      triggerSplit: async () => {
        return {
          allData: allData,
        };
      },
      getContractData: () => {
        console.log("到第四步了");
        getContractData();
      },
      exportData: () => {
        console.log("到第四步了,导出了");
        exportContract();
      },
    }));
    return (
      <div style={{ height: "100%" }} className="law-review">
        <Row style={{ height: "100%" }}>
          <Col xs={24} md={10}>
            <Tabs
              defaultActiveKey="0"
              activeKey={currentActiveKey}
              onChange={setCurrentActiveKey}
              className="law-file-list"
              items={unitInputData?.fileList?.map((x, index) => ({
                key: index + "",
                label: x.name,
                children: (
                  <div style={{ minHeight: "calc(100vh - 160px)" }}>
                    <DocxPreview
                      file={unitInputData?.originalFile}
                      className="my-split-docx-preview"
                    />
                  </div>
                ),
              }))}
            />
          </Col>

          <Col
            xs={24}
            md={14}
            className="law-right"
            style={{ height: "calc(100vh - 140px)" }}
          >
            <Flex className="law-right-header" gap={token.marginSM} vertical>
              <Flex justify="space-between" align="center">
                <Flex
                  style={{
                    fontSize: token.fontSizeXL,
                    fontWeight: "bold",
                    color: "#333",
                  }}
                  align="center"
                >
                  <Result status="success" />
                  审查结果
                </Flex>
                <Flex
                  style={{
                    fontSize: token.fontSize,
                    color: "#333333",
                    lineHeight: token.lineHeight,
                  }}
                  gap={token.marginMD}
                >
                  <span>
                    当前立场：
                    <span style={{ color: token.colorPrimary }}>
                      {unitInputData?.ruleFrom?.stance || ""}
                    </span>
                  </span>
                  <span>
                    审查视角：
                    <span style={{ color: token.colorPrimary }}>
                      {unitInputData?.ruleFrom?.angleSelectValue || ""}
                    </span>
                  </span>
                  <span>
                    审核模式：
                    <span style={{ color: token.colorPrimary }}>
                      {unitInputData?.ruleFrom?.type_schema || ""}
                    </span>
                  </span>
                </Flex>
              </Flex>
              <Flex justify="space-between" align="center">
                <Flex
                  style={{
                    fontSize: token.fontSize,
                    color: "#333333",
                    lineHeight: token.lineHeight,
                    letterSpacing: "0.6px",
                    marginLeft: token.marginLG,
                  }}
                  gap={token.marginXS}
                >
                  共{allData.length}个风险点：高风险
                  {allData.filter((x) => x.rule_level === "高风险").length}
                  个，中风险
                  {allData.filter((x) => x.rule_level === "中风险").length}
                  个，低风险
                  {allData.filter((x) => x.rule_level === "低风险").length} 个
                </Flex>
              </Flex>
            </Flex>
            <Tabs
              defaultActiveKey="全部"
              activeKey={showType}
              onChange={(activeKey: string) => {
                setShowType(activeKey);
              }}
              items={items}
              className="law-file-view"
              tabBarStyle={{ marginBottom: 0 }}
              renderTabBar={(props, DefaultTabBar) => (
                <DefaultTabBar
                  {...props}
                  style={{ backgroundColor: "transparent" }}
                />
              )}
            />
            <div
              style={{ height: "calc(100vh - 320px)", overflow: "auto" }}
              ref={scrollRef}
            >
              {/* <StreamTypewriter
                text={messageStr}
                onchange={() => {
                  scrollRef.current?.scrollTo({
                    top: scrollRef.current.scrollHeight,
                    behavior: "smooth",
                  });
                }}
                end={true}
                charsPerUpdate={5}
                components={{
                  listing({ children, className, ...props }: any) {
                    let isJSON = false;
                    let array: any = [];
                    console.log(children, 912);
                    try {
                      array = JSON.parse(children); // ✅ children 现在是合法 JSON
                      isJSON = true;
                    } catch (error) {
                      isJSON = false;
                    }
                    const [expandedMap, setExpandedMap] = useState<
                      Record<number, boolean>
                    >({});

                    return isJSON ? (
                      <>
                        {array?.map((obj: any, index: number) => {
                          const expanded = expandedMap[index] ?? true;
                          return (
                            <>
                              {showType === obj?.rule_level ||
                              showType === "全部" ? (
                                <Card
                                  style={{ marginBottom: token.margin }}
                                  className="law-contract-card"
                                  styles={{
                                    body: expanded
                                      ? { display: "none", padding: 0 }
                                      : {},
                                  }}
                                  title={
                                    <Flex
                                      justify="space-between"
                                      align="center"
                                    >
                                      <div
                                        style={{
                                          maxWidth: "calc(100% - 50px)",
                                          overflow: "hidden",
                                          textOverflow: "ellipsis",
                                          whiteSpace: "nowrap",
                                        }}
                                      >
                                        <Tag
                                          color={
                                            riskLevelColors[obj?.rule_level]
                                          }
                                        >
                                          {obj?.rule_level}
                                        </Tag>
                                        <Text>{obj?.rule_desc}</Text>
                                      </div>
                                      {expanded ? (
                                        <UpOutlined
                                          onClick={() =>
                                            setExpandedMap((prev) => ({
                                              ...prev,
                                              [index]: false,
                                            }))
                                          }
                                        />
                                      ) : (
                                        <DownOutlined
                                          onClick={() =>
                                            setExpandedMap((prev) => ({
                                              ...prev,
                                              [index]: true,
                                            }))
                                          }
                                        />
                                      )}
                                    </Flex>
                                  }
                                >
                                  {!expanded && (
                                    <>
                                      <Tabs defaultActiveKey="1">
                                        {obj?.risk_points?.map(
                                          (item: any, index: any) => (
                                            <TabPane
                                              tab={`风险点${index + 1}`}
                                              key={index + 1}
                                            >
                                              <Text
                                                style={{
                                                  whiteSpace: "pre-line",
                                                }}
                                              >
                                                {item?.original_text || "--"}
                                              </Text>
                                              <p
                                                style={{
                                                  color: "#333",
                                                  fontWeight: "bold",
                                                  margin: "16px 0px",
                                                  fontSize: "16px",
                                                  lineHeight: "22px",
                                                }}
                                              >
                                                校验结果
                                              </p>
                                              <Space
                                                direction="vertical"
                                                size={16}
                                                style={{ width: "100%" }}
                                              >
                                                <Card
                                                  size="small"
                                                  className="tips-card"
                                                  style={{
                                                    background:
                                                      token.colorErrorBg,
                                                    border: "none",
                                                  }}
                                                >
                                                  <Text strong>风险提示：</Text>
                                                  <Text>
                                                    {item?.risk_warning || "--"}
                                                  </Text>
                                                </Card>

                                                <Card
                                                  size="small"
                                                  className="tips-card"
                                                  style={{
                                                    background:
                                                      token.colorPrimaryBg,
                                                    border: "none",
                                                  }}
                                                >
                                                  <Text strong>修改意见：</Text>
                                                  <Text>
                                                    {item?.suggestion || "--"}
                                                  </Text>
                                                </Card>
                                              </Space>
                                            </TabPane>
                                          )
                                        )}
                                      </Tabs>
                                    </>
                                  )}
                                </Card>
                              ) : null}
                            </>
                          );
                        })}
                      </>
                    ) : (
                      <code
                        {...props}
                        className={className}
                        style={{
                          wordWrap: "break-word",
                          wordBreak: "break-all",
                          overflowWrap: "break-word",
                          whiteSpace: "pre-wrap",
                        }}
                      >
                        {children}
                      </code>
                    );
                  },
                }}
              /> */}
              {messageStr ? (
                <>
                  <>
                    {allData &&
                      allData.map((obj: any, index: number) => {
                        const expanded = expandedMap[index] ?? true;

                        return (
                          <>
                            {showType === obj?.rule_level ||
                            showType === "全部" ? (
                              <Card
                                style={{ marginBottom: token.margin }}
                                className="law-contract-card"
                                styles={{
                                  body: expanded
                                    ? { display: "none", padding: 0 }
                                    : {},
                                }}
                                title={
                                  <Flex justify="space-between" align="center">
                                    <div
                                      style={{
                                        maxWidth: "calc(100% - 50px)",
                                        overflow: "hidden",
                                        textOverflow: "ellipsis",
                                        whiteSpace: "nowrap",
                                      }}
                                    >
                                      <Tag
                                        color={riskLevelColors[obj?.rule_level]}
                                      >
                                        {obj?.rule_level}
                                      </Tag>
                                      <Text>{obj?.rule_desc}</Text>
                                    </div>
                                    {expanded ? (
                                      <DownOutlined
                                        onClick={() =>
                                          setExpandedMap((prev) => ({
                                            ...prev,
                                            [index]: false,
                                          }))
                                        }
                                      />
                                    ) : (
                                      <UpOutlined
                                        onClick={() =>
                                          setExpandedMap((prev) => ({
                                            ...prev,
                                            [index]: true,
                                          }))
                                        }
                                      />
                                    )}
                                  </Flex>
                                }
                              >
                                {!expanded && (
                                  <>
                                    <Tabs defaultActiveKey="1">
                                      {obj?.risk_points?.map(
                                        (item: any, index: any) => (
                                          <TabPane
                                            tab={`风险点${index + 1}`}
                                            key={index + 1}
                                          >
                                            <Text
                                              style={{
                                                whiteSpace: "pre-line",
                                              }}
                                            >
                                              {item?.original_text || "--"}
                                            </Text>
                                            <p
                                              style={{
                                                color: "#333",
                                                fontWeight: "bold",
                                                margin: "16px 0px",
                                                fontSize: "16px",
                                                lineHeight: "22px",
                                              }}
                                            >
                                              校验结果
                                            </p>
                                            <Space
                                              direction="vertical"
                                              size={16}
                                              style={{ width: "100%" }}
                                            >
                                              <Card
                                                size="small"
                                                className="tips-card"
                                                style={{
                                                  background:
                                                    token.colorErrorBg,
                                                  border: "none",
                                                }}
                                              >
                                                <Text strong>风险提示：</Text>
                                                <Text>
                                                  {item?.risk_warning || "--"}
                                                </Text>
                                              </Card>

                                              <Card
                                                size="small"
                                                className="tips-card"
                                                style={{
                                                  background:
                                                    token.colorPrimaryBg,
                                                  border: "none",
                                                }}
                                              >
                                                <Text strong>修改意见：</Text>
                                                <Text>
                                                  {item?.suggestion || "--"}
                                                </Text>
                                              </Card>
                                            </Space>
                                          </TabPane>
                                        )
                                      )}
                                    </Tabs>
                                  </>
                                )}
                              </Card>
                            ) : null}
                          </>
                        );
                      })}
                  </>
                  <StreamTypewriter
                    text={messageStr}
                    onchange={() => {
                      scrollRef.current?.scrollTo({
                        top: scrollRef.current.scrollHeight,
                        behavior: "smooth",
                      });
                    }}
                    end={true}
                    charsPerUpdate={5}
                  />
                </>
              ) : (
                // messageStr 没有值时，直接渲染 listing 里的内容
                <>
                  {allData &&
                    allData.map((obj: any, index: number) => {
                      const expanded = expandedMap[index] ?? true;

                      return (
                        <>
                          {showType === obj?.rule_level ||
                          showType === "全部" ? (
                            <Card
                              style={{ marginBottom: token.margin }}
                              className="law-contract-card"
                              styles={{
                                body: expanded
                                  ? { display: "none", padding: 0 }
                                  : {},
                              }}
                              title={
                                <Flex justify="space-between" align="center">
                                  <div
                                    style={{
                                      maxWidth: "calc(100% - 50px)",
                                      overflow: "hidden",
                                      textOverflow: "ellipsis",
                                      whiteSpace: "nowrap",
                                    }}
                                  >
                                    <Tag
                                      color={riskLevelColors[obj?.rule_level]}
                                    >
                                      {obj?.rule_level}
                                    </Tag>
                                    <Text>{obj?.rule_desc}</Text>
                                  </div>
                                  {expanded ? (
                                    <DownOutlined
                                      onClick={() =>
                                        setExpandedMap((prev) => ({
                                          ...prev,
                                          [index]: false,
                                        }))
                                      }
                                    />
                                  ) : (
                                    <UpOutlined
                                      onClick={() =>
                                        setExpandedMap((prev) => ({
                                          ...prev,
                                          [index]: true,
                                        }))
                                      }
                                    />
                                  )}
                                </Flex>
                              }
                            >
                              {!expanded && (
                                <>
                                  <Tabs defaultActiveKey="1">
                                    {obj?.risk_points?.map(
                                      (item: any, index: any) => (
                                        <TabPane
                                          tab={`风险点${index + 1}`}
                                          key={index + 1}
                                        >
                                          <Text
                                            style={{
                                              whiteSpace: "pre-line",
                                            }}
                                          >
                                            {item?.original_text || "--"}
                                          </Text>
                                          <p
                                            style={{
                                              color: "#333",
                                              fontWeight: "bold",
                                              margin: "16px 0px",
                                              fontSize: "16px",
                                              lineHeight: "22px",
                                            }}
                                          >
                                            校验结果
                                          </p>
                                          <Space
                                            direction="vertical"
                                            size={16}
                                            style={{ width: "100%" }}
                                          >
                                            <Card
                                              size="small"
                                              className="tips-card"
                                              style={{
                                                background: token.colorErrorBg,
                                                border: "none",
                                              }}
                                            >
                                              <Text strong>风险提示：</Text>
                                              <Text>
                                                {item?.risk_warning || "--"}
                                              </Text>
                                            </Card>

                                            <Card
                                              size="small"
                                              className="tips-card"
                                              style={{
                                                background:
                                                  token.colorPrimaryBg,
                                                border: "none",
                                              }}
                                            >
                                              <Text strong>修改意见：</Text>
                                              <Text>
                                                {item?.suggestion || "--"}
                                              </Text>
                                            </Card>
                                          </Space>
                                        </TabPane>
                                      )
                                    )}
                                  </Tabs>
                                </>
                              )}
                            </Card>
                          ) : null}
                        </>
                      );
                    })}
                </>
              )}
            </div>
          </Col>
        </Row>
      </div>
    );
  }
);
// 添加 displayName
ContractLawReview.displayName = "SplitPreviewModule";
export default ContractLawReview;
