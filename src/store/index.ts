import { configureStore } from '@reduxjs/toolkit'

import { commonReducer } from '@/store/Reducer/commonReducer'
import { configReducer } from '@/store/Reducer/configReducer'

const store = configureStore({
  reducer: {
    commonReducer,
    configReducer
  }
})

// Infer the `RootState` and `AppDispatch` types from the store itself
type RootState = ReturnType<typeof store.getState>

// Inferred type: {posts: PostsState, comments: CommentsState, users: UsersState}
type AppDispatch = typeof store.dispatch

export type { AppDispatch, RootState }
export { store }
export { useAppDispatch, useAppSelector } from './hooks'
