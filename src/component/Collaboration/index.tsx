import React, { useEffect, useMemo, useState } from "react";
import { message, Modal } from "antd";
import { scenarioSubmit } from "@/api/public";
import { MentionsInput, Mention } from "react-mentions";
import { useLocation } from "react-router-dom";
import { listNoteRela } from "@/api/public";
import "./index.less";

const mentionTagStyle = {
  color: "#1677ff",
  fontWeight: 600,
  marginLeft: "-2px",
  position: "absolute",
  background: "none",
  lineHeight: "18px",
  zIndex: 1,
};

const inputStyles = {
  control: {
    fontSize: 14,
    lineHeight: "22px",
    minHeight: 120,
    border: "1px solid #d9d9d9",
    borderRadius: 8,
    background: "#fff",
  },
  highlighter: {
    padding: 12,
    overflow: "hidden",
    whiteSpace: "pre-wrap" as const,
    wordWrap: "break-word" as const,
  },
  input: {
    padding: 12,
    border: 0,
    outline: 0,
    boxShadow: "none",
    fontSize: 14,
    lineHeight: "22px",
  },
  suggestions: {
    list: {
      backgroundColor: "#fff",
      border: "1px solid rgba(0,0,0,0.15)",
      borderRadius: 8,
      maxHeight: 240,
      overflowY: "auto" as const,
    },
    item: {
      padding: "8px 12px",
      borderBottom: "1px solid #f5f5f5",
      "&focused": {
        backgroundColor: "#E6F4FF",
      },
    },
  },
};

interface MentionModalProps {
  open: boolean;
  pageInfo?: any; // 页面的数据
  currentStep?: number | string; // 当前步骤
  onOk?: (value: any) => void; // 返回纯文本
  onCancel?: () => void;
}

const MentionModal: React.FC<MentionModalProps> = ({
  open,
  onOk,
  currentStep,
  pageInfo,
  onCancel,
}) => {
  const location = useLocation();
  const [value, setValue] = useState("");
  const [loading, setLoading] = useState(false);
  const [users, setUsers] = useState([]); // @的人员列表
  const [selectUserList, setSelectUserList] = useState<any>([]);

  // 默认的 markup 格式：@[display](id)
  const mentionMarkup = "@[__display__](__id__)";

  // 判断是否已经有 mention
  const hasMention = useMemo(() => /@\[[^\]]+\]\([^)]+\)/.test(value), [value]);
  // 提取 mentions 的 id
  const extractMentionIds = (v: string) => {
    const ids: string[] = [];
    const regex = /@\[[^\]]+\]\(([^)]+)\)/g;
    let match;
    while ((match = regex.exec(v)) !== null) {
      ids.push(match[1]); // 这里就是 __id__
    }
    return ids;
  };
  // 提交时转为纯文本
  const toPlainText = (v: string) =>
    v.replace(/@\[(.+?)\]\((.+?)\)/g, (_m, display) => `@${display}`);
  const getUseList = () => {
    listNoteRela({
      type: "note_rel_contact",
      query: "",
    }).then((res) => {
      const mentionData = res.data.map((u) => ({
        display: u.name,
        id: u.objId, // 或者用 objId 作为唯一标识
      }));
      setUsers(mentionData);
    });
  };
  // 提交
  const submit = () => {
    setLoading(true);
    const textValue = toPlainText(value);
    const ids = extractMentionIds(value); // 选中的 id 列表
    setSelectUserList(ids || []);
    const pathname = window.location.pathname; // "/copilot/legalReview"
    const lastSegment = pathname.split("/").filter(Boolean).pop();
    const url = `${
      import.meta.env.VITE_BASE_API
    }/copilot/login?pathName=${lastSegment}`;
    scenarioSubmit({
      url: url,
      currentStep: currentStep,
      pageInfo: pageInfo,
      userId: ids?.[0],
      msgTitle: pageInfo?.pageName,
      msgContent: textValue,
    })
      .then((res: any) => {
        if (res.code == 200) {
          message.success("协同成功");
          setValue("");
        } else {
          message.success(res.msg);
        }
        setLoading(false);
        onOk?.(res);
      })
      .catch((err) => {
        message.success(err.msg);
        setLoading(false);
        onOk?.(err);
      });
  };
  // 取消
  const handleCancel = () => {
    setLoading(false);
    setValue("");
    onCancel?.();
  };
  useEffect(() => {
    getUseList();
  }, []);
  return (
    <Modal
      title="描述"
      open={open}
      confirmLoading={loading} // 👈 绑定 loading
      onOk={submit}
      onCancel={handleCancel}
      destroyOnClose
    >
      <div className="modal-content-collaboration">
        <MentionsInput
          value={value}
          onChange={(e) => {
            setValue(e.target.value);
          }}
          style={inputStyles}
          placeholder="请输入内容，使用 @ 选择人员（仅限一人）"
          allowSuggestionsAboveCursor
          className="custom-mentions-dropdown"
        >
          <Mention
            trigger="@"
            data={hasMention ? [] : users} // 👈 已有@时禁止再选
            style={mentionTagStyle}
            markup={mentionMarkup}
            displayTransform={(_id, display) => `@${display}`}
          />
        </MentionsInput>
      </div>
    </Modal>
  );
};

export default MentionModal;
