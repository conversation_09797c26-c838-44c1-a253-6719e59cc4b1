import type { InitialState, RegistrantActions } from '@/store/Reducer/types'

import { createSlice } from '@reduxjs/toolkit'
import { getLocalStorage, setLocalStorage } from '@/utils/storage'

const initialState: InitialState = {
  isFromApp: getLocalStorage('setIsFromApp', true) ?? false,
  isSelect: getLocalStorage('setIsSelect', true) ?? 'false',
  hideHeader: getLocalStorage('setHideHeader', true) ?? 'false'
}

const registrantSlice = createSlice({
  name: 'counter',
  initialState,
  reducers: {
    setIsFromApp: (state, action: { payload: boolean }) => {
      state.isFromApp = action.payload
      setLocalStorage('setIsFromApp', state.isFromApp)
    },
    setIsSelect: (state, action: { payload: boolean }) => {
      state.isSelect = action.payload
      setLocalStorage('setIsSelect', state.isSelect)
    },
    setHideHeader: (state, action: { payload: boolean }) => {
      state.hideHeader = action.payload
      setLocalStorage('setHideHeader', state.hideHeader)
    }
  }
})

// Action creators are generated for each case reducer function
const { setIsFromApp, setIsSelect, setHideHeader }: RegistrantActions = registrantSlice.actions

const commonReducer = registrantSlice.reducer

export { commonReducer, setIsFromApp, setIsSelect, setHideHeader }
export type { InitialState }
