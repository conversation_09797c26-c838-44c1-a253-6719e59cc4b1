<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TechInnovate Solutions</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }
        .bg-pattern {
            background-color: #f4f4f4;
            /* background-image: 
                linear-gradient(45deg, #e6e6e6 25%, transparent 25%), 
                linear-gradient(-45deg, #e6e6e6 25%, transparent 25%); */
            background-size: 20px 20px;
        }
    </style>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/echarts/5.4.3/echarts.min.js"></script>
</head>
<body class="bg-pattern min-h-screen flex items-center justify-center p-4">
    <div class="container mx-auto max-w-4xl">
        <div class="bg-white shadow-xl rounded-xl overflow-hidden grid md:grid-cols-2 grid-cols-1">

            <!-- 右侧详细信息 -->
            <div class="md:col-span-2 p-8">
                <div class="space-y-6">
                   正在查询，请稍等...
               
                </div>
            </div>
        </div>
    </div>
</body>
<script>
    async function callChatMessagesAPI(apikey, query, fileId) {
        const companyInfoDiv = document.querySelector('.space-y-6');
        // Add full-page loading overlay
        const loadingOverlay = document.createElement('div');
        loadingOverlay.className = 'fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50';
        loadingOverlay.innerHTML = `
            <div class="text-center">
                <div class="animate-spin rounded-full h-16 w-16 border-4 border-gray-300 border-t-blue-500"></div>
                <span class="block mt-4 text-xl text-white">正在加载...</span>
            </div>
        `;
        document.body.appendChild(loadingOverlay);

        try {
            const response = await fetch('https://copilot.sino-bridge.com:90/v1/chat-messages', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${apikey}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    "inputs": {},
                    "query": query,
                    "response_mode": "streaming",
                    "conversation_id": "",
                    "user": "web-user-" + Math.random().toString(36).substr(2, 9),
                    "files": [{
                        type: "document",
                        transfer_method: "local_file",
                        upload_file_id: fileId
                    }],
                })
            });

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`HTTP错误 ${response.status}: ${errorText}`);
            }

            const reader = response.body.getReader();
            const decoder = new TextDecoder("utf-8");
            let contentHtml = '';
            let fullContent = '';

            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                const chunk = decoder.decode(value, { stream: true });
                const lines = chunk.split("\n");

                for (const line of lines) {
                    if (line.startsWith("data: ")) {
                        const jsonString = line.substring(6).trim();
                        if (jsonString) {
                            try {
                                const data = JSON.parse(jsonString);
                                if (data.event === "message") {
                                    fullContent += data.answer;
                                    companyInfoDiv.innerHTML = fullContent;
                                    await new Promise(resolve => setTimeout(resolve, 50));
                                }
                            } catch (e) {
                                console.error('JSON解析错误:', e);
                            }
                        }
                    }
                }
            }

            // 流式输出完成后，按段落分割并美化
            const sections = fullContent.split('\n\n');
            contentHtml = sections.map(section => {
                if (section.trim()) {
                    const [title, ...items] = section.split('\n');
                    return `
                        <section class="mb-6">
                            <h2 class="text-xl font-semibold text-gray-800 border-b pb-2 mb-3">
                                ${title}
                            </h2>
                            <div class="text-gray-600">
                                ${items.map(item => `<p class="mb-2">${item.replace(/^- /, '')}</p>`).join('')}
                            </div>
                        </section>
                    `;
                }
                return '';
            }).join('');
   // 在显示内容之前，移除JSON数据
   contentHtml = contentHtml.replace(/\{"parse_result":\s*\{.*?\}\}/g, '');
            companyInfoDiv.innerHTML = contentHtml;

            // 查找收入数据并解析
            const match = fullContent.match(/\{"parse_result":\s*\{.*?\}\}/);
            
            if (match) {
                try {
                    const parsedData = JSON.parse(match[0]);
                    if (parsedData.parse_result) {
                        const { data, value } = parsedData.parse_result;
                        
                        // 创建图表容器
                        const chartDiv = document.createElement('div');
                        chartDiv.style.width = '100%';
                        chartDiv.style.height = '400px';
                        companyInfoDiv.insertBefore(chartDiv, companyInfoDiv.firstChild);
                     

                        // 初始化 ECharts
                        const chart = echarts.init(chartDiv);
                        
                        // 配置项
                        const option = {
                            title: {
                                text: '年度收入统计'
                            },
                            tooltip: {
                                trigger: 'axis'
                            },
                            xAxis: {
                                type: 'category',
                                data: data
                            },
                            yAxis: {
                                type: 'value'
                            },
                            series: [{
                                data: value,
                                type: 'bar',
                                showBackground: true,
                                backgroundStyle: {
                                    color: 'rgba(180, 180, 180, 0.2)'
                                }
                            }]
                        };

                        // 使用配置项显示图表
                        chart.setOption(option);
                    }
                } catch (e) {
                    console.error('解析收入数据失败:', e);
                }
            }

        } catch (error) {
            console.error('查询错误:', error);
            companyInfoDiv.innerHTML = `<div class="text-red-500">加载错误: ${error.message}</div>`;
        } finally {
            // Remove loading overlay when done
            loadingOverlay.remove();
        }
    }

    // 获取 URL 中的 query 参数
    const urlParams = new URLSearchParams(window.location.search);
    const queryParam = urlParams.get('query'); // 获取名为 'query' 的参数

    // 如果 query 参数存在，则调用 API
    if (queryParam) {
        const apikey = 'app-aJ9ioCjhuaoCVMf6GKkKUjMH'; // 固定的 API Key
        const fileId = 'your-file-id'; // 替换为实际的 fileId
        callChatMessagesAPI(apikey, queryParam, fileId);
    } else {
        console.error('未找到 query 参数');
    }
</script>

</html>
