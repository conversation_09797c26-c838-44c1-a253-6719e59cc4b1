import Icon from '@ant-design/icons'
import { useEffect, useMemo, useState } from 'react'

type CSSProperties = React.CSSProperties
/** 自定义Icon组件属性 */
export interface IconFontProps {
  // icon的类型，传入值需与.svg的文件名保持一致
  type: string
  // 以下是AntD Icon组件的component组件接受的属性
  className?: string
  fill?: string
  style?: CSSProperties
  height?: string | number
  width?: string | number
  isGradien?: boolean | undefined
}

// 定义SVG组件类型
type SVGComponentType = React.ComponentType<any>

function IconFont({ type, isGradien, width, height, className, style, fill }: IconFontProps) {
  const [SVGComponent, setSVGComponent] = useState<SVGComponentType | null>(null)

  useEffect(() => {
    let isMounted = true
    // 使用 import.meta.glob 预加载所有 SVG 文件
    const icons = import.meta.glob('../../assets/icons/*.svg')
    const iconPath = `../../assets/icons/${type}.svg`

    if (icons[iconPath]) {
      icons[iconPath]()
        .then((module: any) => {
          if (isMounted) {
            const SVG = module.ReactComponent
            setSVGComponent(() => SVG)
          }
        })
        .catch(error => console.error('加载SVG时发生错误:', error))
    }

    return () => {
      isMounted = false
    }
  }, [type])

  // 使用 useMemo 缓存 gradientStyle
  const gradientId = useMemo(() => `gradient-${type}`, [type])
  const gradientStyle = useMemo(
    () => (
      <defs>
        <linearGradient id={gradientId} gradientTransform='rotate(128)'>
          <stop offset='19%' stopColor='#1888FF' />
          <stop offset='87%' stopColor='#2F54EB' />
        </linearGradient>
      </defs>
    ),
    [gradientId]
  )

  if (!SVGComponent) {
    return null
  }

  const iconProps = {
    ...(style && { style }),
    ...(className && { className }),
    ...(width && { width }),
    ...(height && { height }),
  }

  return isGradien ? (
    <Icon
      component={() => (
        <svg {...iconProps}>
          {gradientStyle}
          <SVGComponent fill={`url(#${gradientId})`} {...iconProps} />
        </svg>
      )}
    />
  ) : (
    <Icon component={() => <SVGComponent {...iconProps} fill={fill} />} />
  )
}

IconFont.displayName = 'IconFont'

export default IconFont
