import { useState, useImperativeHandle, forwardRef,useEffect } from 'react';
import { Card, Tag, Tabs, message } from 'antd';
import './index.less';

export interface QuestionModuleRef {
  getQuestionData: () => any;
  setQuestionData: (data: any) => void;
  generateQuestions: () => Promise<void>;
}

interface QuestionModuleProps {
  viewData?: any
}


interface QuestionItem {
  id: string;
  title: string;
  category: string;
  examinePoints: string[];
  answerPoints: string[];
  scoringCriteria: Array<{
    criterion: string;
    weight: number;
  }>;
}

const QuestionModule = forwardRef<QuestionModuleRef, QuestionModuleProps>(({ 
  viewData

}, ref) => {
  const [questions, setQuestions] = useState<QuestionItem[]>([
    {
      id: '1',
      title: '作为产品经理,你如何平衡市场推广活动的创意设计需求与技术实现可行性?',
      category: '技术',
      examinePoints: ['技术理解能力', '需求分析能力'],
      answerPoints: ['需求优先级排序', '技术可行性评估'],
      scoringCriteria: [
        { criterion: '逻辑清晰性', weight: 30 },
        { criterion: '技术理解', weight: 40 },
        { criterion: '实际案例', weight: 30 }
      ]
    },
    {
      id: '2',
      title: '作为产品经理,你如何平衡市场推广活动的创意设计需求与技术实现可行性?',
      category: '技术',
      examinePoints: ['技术理解能力', '需求分析能力'],
      answerPoints: ['需求优先级排序', '技术可行性评估'],
      scoringCriteria: [
        { criterion: '逻辑清晰性', weight: 30 },
        { criterion: '技术理解', weight: 40 },
        { criterion: '实际案例', weight: 30 }
      ]
    },
    {
      id: '3',
      title: '请分享你使用Photoshop和Illustrator设计推广物料的具体工作流程',
      category: '设计',
      examinePoints: ['设计工具使用能力', '工作流程规范性'],
      answerPoints: ['需求分析', '设计规划', '制作执行', '反馈优化'],
      scoringCriteria: [
        { criterion: '工具熟练度', weight: 40 },
        { criterion: '流程规范性', weight: 35 },
        { criterion: '设计质量', weight: 25 }
      ]
    }
  ]);
  useEffect(() => {
    console.log('智能出题viewData:', viewData)
  }, [viewData])
  
  // 旧生成逻辑的状态，仅保留 setter 以便复用生成函数
  const [, setIsGenerating] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    getQuestionData: () => ({
      questions,
      totalCount: questions.length,
      categories: [...new Set(questions.map(q => q.category))]
    }),
    setQuestionData: (data: any) => {
      if (data.questions) {
        setQuestions(data.questions);
      }
    },
    generateQuestions: async () => {
      await handleGenerateQuestions();
    },
  }), [questions]);

  // 生成题目
  const handleGenerateQuestions = async () => {
    setIsGenerating(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const newQuestions = [
        {
          id: Date.now().toString(),
          title: '请描述你在产品迭代过程中如何处理用户反馈和需求变更？',
          category: '产品管理',
          examinePoints: ['需求管理能力', '用户思维'],
          answerPoints: ['用户反馈收集', '需求优先级评估', '变更管理流程'],
          scoringCriteria: [
            { criterion: '需求理解', weight: 35 },
            { criterion: '流程管理', weight: 35 },
            { criterion: '沟通协调', weight: 30 }
          ]
        }
      ];
      
      setQuestions(prev => [...prev, ...newQuestions]);
      
      message.success('题目生成成功！');
    } catch (error) {
      message.error('题目生成失败，请重试');
    } finally {
      setIsGenerating(false);
    }
  };


  // 过滤题目
  const filteredQuestions = selectedCategory === 'all' 
    ? questions 
    : questions.filter(q => q.category === selectedCategory);

  const categories = [...Array.from(new Set(questions.map(q => q.category)))];

  // ----------------------
  // 渲染 viewData 为 Tabs
  // ----------------------
  const tabLabelMap: Record<string, string> = {
    '技术能力': '技术能力',
    '项目经验': '项目经验',
    '团队协作': '团队协作',
    '问题解决': '问题解决',
    '学习能力': '学习能力',
    '沟通表达': '沟通表达',
    '协同能力': '协同能力'
  };

  const getTabLabel = (raw: string) => tabLabelMap[raw] || raw;

  const renderPointsList = (raw: any) => {
    const points = Array.isArray(raw)
      ? raw
      : typeof raw === 'string'
        ? raw.split(/[、，,；;]/).map(s => s.trim()).filter(Boolean)
        : [];
    if (points.length === 0) return null;
    return (
      <ul className="qr-points-list">
        {points.map((p: string, idx: number) => (
          <li key={idx}>{p}</li>
        ))}
      </ul>
    );
  };

  const renderCategory = (categoryName: string, qObj: any) => (
    <div className="qr-category">
      <h3>{getTabLabel(categoryName)}能力题目</h3>
      <ol className="qr-questions" style={{paddingLeft: '20px'}}>
        {Object.entries(qObj || {}).map(([qKey, data]: any) => {
          const question = data?.['问题'];
          const answer = data?.['参考答案'];
          const points = data?.['考察点'];
          const follow = data?.['追问建议'];
          return (
            <li key={qKey} className="qr-question-item">
              <div className="qr-question">{question}</div>
              <div className="qr-section">
                <strong>参考答案：</strong>
                <div className="qr-text">{answer}</div>
              </div>
              <div className="qr-section">
                <strong>考察点：</strong>
                {renderPointsList(points)}
              </div>
              <div className="qr-section">
                <strong>追问建议：</strong>
                <div className="qr-text">{follow}</div>
              </div>
            </li>
          );
        })}
      </ol>
    </div>
  );

  // 如果提供了 viewData Tabs 呈现
  if (viewData && Object.keys(viewData).length > 0) {
    const items = Object.entries(viewData).map(([category, qObj]: [string, any]) => ({
      key: category,
      label: getTabLabel(category),
      children: renderCategory(category, qObj)
    }));

    return (
      <div className="question-module">
        <Tabs className="question-tabs" items={items} />
      </div>
    );
  }
});

QuestionModule.displayName = 'QuestionModule';

export default QuestionModule;
