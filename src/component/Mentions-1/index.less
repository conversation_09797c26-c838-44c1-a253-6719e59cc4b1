.mentions-components{  
  .top-toolbar {
    /* 聊天工具条 */
    height: 28px;
    .top-toolbar-left {
      flex: 1;
      width: 59% !important;
      all: initial;
      height: 26px;
      .ant-select {
        --ant-select-active-outline-color: none !important;
      }
    }
  }
  .sino-upload-file{
    .icon{
      font-size: 18px;
    }
  }

  .text-input {
    width: 100%;
    border: 1px solid var(--ant-color-border);
    border-radius: var(--ant-border-radius-lg);
    position: relative;
    &:hover {
      border-color: var(--ant-color-primary-hover);
    }
    &:focus {
      border-color: var(--ant-color-primary-active);
    }
    .text-input-content{
      min-width: 108px;
      border: 1px solid var(--ant-color-border-secondary);
      border-radius: var(--ant-border-radius-sm);
      padding: var(--ant-padding-xs);
      background: var(--ant-color-fill-tertiary);
      color: var(--ant-color-text);
      min-height: 46px;
      position: relative;
      .quote-close{
        display: none;
        color: var(--ant-color-text-tertiary);
        font-size: var(--ant-font-size-lg);
        position: absolute;
        right: 3px;
        top: 3px;
        font-size: var(--ant-font-size-lg);
      }
      &:hover{
        .quote-close{
          display: block;
        }
      }
      .quote-tit{
        color: var(--ant-color-text-description);
        font-size: var(--ant-font-size-sm)
      }
      .quote-text{
        color: var(--ant-color-text);
        font-size: var(--ant-font-size-sm);
        overflow: hidden;
        text-overflow:ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
    }
    .prompt-select {
      margin: 0px var(--ant-margin-sm);
      padding: var(--ant-padding-sm) 0px 0px;
      flex: 1;
      .ant-tag-borderless {
        display: flex;
        min-width: 210px;
        max-width: 280px;
        overflow-y: auto;
        justify-content: space-between;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
    .knowledge-base {
      margin-right: var(--ant-margin-sm);
      margin-top: var(--ant-margin-xs);
      box-sizing: border-box;
      flex: 1;
      position: relative;
      .knowledge-base-info {
        gap: var(--ant-margin-xxs);
        overflow-x: auto;
        flex: 1;
      }
      .right-icon,
      .left-icon {
        position: absolute;
        // top: var(--ant-padding-sm);
        width: 25px;
        height: 52px;
      }
      .right-icon {
        right: 0px;
        background: linear-gradient(270deg, #ffffff 36%, rgba(255, 255, 255, 0.18) 67%, rgba(255, 255, 255, 0) 100%);
      }
      .left-icon {
        left: 0px;
        background: linear-gradient(270deg, rgba(255, 255, 255, 0) 36%, rgba(255, 255, 255, 0.18) 67%, #ffffff 100%);
      }
      .left,
      .right {
        min-width: 0px;
        width: 20px;
        height: 20px;
        position: absolute;
        font-size: var(--ant-font-size);
        border: none;
        z-index: 10;
        background: var(--ant-color-bg-base);
        box-shadow:
          0px 2.5px 5px -1px rgba(0, 0, 0, 0.12),
          0px 5px 6.25px 0px rgba(0, 0, 0, 0.08),
          0px 1.25px 12.5px 0px rgba(0, 0, 0, 0.05);
        .anticon {
          font-size: var(--ant-font-size);
          color: var(--ant-color-text);
        }
      }
      .left {
        left: 2px;
        top: 50%;
        transform: translateY(-50%);
      }
      .right {
        right: 2px;
        top: 50%;
        transform: translateY(-50%);
      }
      .knowledge-load {
        width: 24px;
        height: 48px;
        margin-right: var(--ant-margin-xs);
        margin
        > div {
          align-items: center;
        }
      }

      .sino-relation-icon {
        color: #813ce0;
        padding-top: 1.5px;
      }

      .extend-icon {
        width: 24px;
        height: 24px;
      }
      .knowledge-content-base {
        background: var(--ant-color-fill-tertiary);
        border-radius: var(--ant-border-radius);
        padding: 0px var(--ant-padding-xs);
        box-sizing: border-box;
        height: 52px;
        flex: 1;
        max-width: 240px;
        &:hover {
          .close {
            display: block;
          }
        }
      }
      .knowledge-content-base-flex {
        // width: calc(100% - 28px);
        position: relative;
        gap: var(--ant-margin-xxs);
        .close {
          z-index: 10;
          background: #fff;
          color: var(--ant-color-text-tertiary);
          font-size: var(--ant-font-size-lg);
          position: absolute;
          right: -4px;
          top: 4px;
          border-radius: 50%;
          display: none;
        }
      }
      .knowledge-content-base-item {
        white-space: nowrap;
        // width: 94%;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .knowledge-content-base-width {
        width: 108px !important;
        max-width: 108px !important;
      }
      .knowledge-base-item {
        width: 100%;
        white-space: nowrap;
        width: 90%;
        margin-left: var(--ant-margin-xs);
        overflow: hidden;
        text-overflow: ellipsis;
        height: 52px;
      }

      .knowledge-base-title {
        font-size: var(--ant-font-size);
        color: var(--ant-color-text);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .first-title {
        padding-left: 4px;
      }

      .two-title {
        font-size: var(--ant-font-size-sm);
        color: var(--ant-color-text-tertiary);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .knowledge-keyword {
      .knowledge-keyword-ul {
        display: flex;
        flex-wrap: nowrap;
        height: 20px;
        width: 94%;
        padding-left: 10px;

        li {
          padding: 2px 6px;
          background: #f9f0ff;
          border-radius: 2px;
          color: #813ce0;
          font-size: 12px;
          margin-right: 8px;
          cursor: pointer;
        }
      }
    }

    text-align: left;

    .text-input-mentions {
      border-radius: var(--ant-border-radius-lg);
      height: 108px;
      padding: var(--ant-padding-sm);
      border: 0px;
      &:focus,
      &:focus-within {
        box-shadow: none;
      }
      > textarea {
        padding: 0px;
        &:focus {
          box-shadow: none;
          border: none;
        }
      }
    }

    .ant-mentions-dropdown {
      z-index: 9999 !important;
    }

    /* 文本输入区 */
  }
  .chat-textarea {
    padding:2px;
    .chat-cue {
      width: 100%;
      position: absolute;
      left: 0px;
      top: -188px;
      .ant-dropdown {
        width: 100%;
        position: absolute;
        left: 0px !important;
        top: 0px !important;
        max-height: 180px;
        .ant-dropdown-menu {
          height: 100%;
        }
        .ant-dropdown-menu-item-group-title {
          padding: var(--ant-padding-xs);
        }
        .ant-dropdown-menu-item-group-list {
          margin: 0px !important;
          height: 134px !important;
          overflow-y: auto;
        }
      }
    }
    .chat-cue-nodata {
      width: 100%;
      padding: var(--ant-padding-xxs);
      border-radius: var(--ant-border-radius-lg);
      background: var(--ant-color-bg-container);
      position: absolute;
      left: 0px;
      top: -188px;
      height: 180px;
      z-index: 111;
      box-shadow:
        0px 8px 10px -5px rgba(0, 0, 0, 0.08),
        0px 16px 24px 2px rgba(0, 0, 0, 0.04),
        0px 6px 30px 5px rgba(0, 0, 0, 0.05);
      .chat-cue-tit {
        padding: var(--ant-padding-xs) var(--ant-padding-sm);
        font-size: var(--ant-font-size);
        line-height: var(--ant-line-height);
        color: var(--ant-color-text-tertiary);
      }
      .chat-cue-con {
        width: 100%;
        max-height: 120px;
        overflow-y: auto;
      }
      .chat-cue-text {
        padding: var(--ant-padding-xxs) var(--ant-padding-sm);
        font-size: var(--ant-font-size);
        color: var(--ant-color-text);
        cursor: pointer;
        border-radius: var(--ant-border-radius-sm);
        flex: 1 1 100%;
        span {
          width: 100%;
          display: inline-block;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        &:hover {
          background: var(--ant-control-item-bg-hover);
        }
      }
    }
  }
}