const keyBefore = "bridge-";

function setLocalStorage(key: string, value: any): void {
  localStorage.setItem(
    `${keyBefore}${key}`,
    typeof value === "string" ? value : JSON.stringify(value)
  );
}

function getLocalStorage(key: string, isParse = false): any {
  const cache = localStorage.getItem(`${keyBefore}${key}`);

  if (cache == null) return null;

  if (isParse) {
    try {
      return JSON.parse(cache);
    } catch (e) {
      return null;
    }
  }

  return cache;
}

export { getLocalStorage, setLocalStorage };
