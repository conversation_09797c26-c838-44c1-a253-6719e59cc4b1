// import { useEffect, useRef } from "react";
// import { renderAsync } from "docx-preview";

// interface DocxPreviewProps {
//   file: File | ArrayBuffer | Blob | null; // 支持 File / Blob / ArrayBuffer
//   className?: string; // 自定义 className
//   ignoreCss?: boolean; // 是否忽略 docx-preview 自带样式
//   style?: React.CSSProperties; // 容器样式
// }

// const DocxPreview: React.FC<DocxPreviewProps> = ({
//   file,
//   className = "my-docx-preview",
//   ignoreCss = false,
//   style = { width: "100%", height: "400px" },
// }) => {
//   const previewRef = useRef<HTMLDivElement | null>(null);

//   useEffect(() => {
//     if (!file || !previewRef.current) return;

//     // 清空上一次渲染内容，避免多个文件叠加
//     previewRef.current.innerHTML = "";

//     renderAsync(file, previewRef.current, null, {
//       className,
//       ignoreCss,
//     })
//       .then(() => console.log("DOCX 渲染成功"))
//       .catch((err) => console.error("DOCX 渲染失败:", err));
//   }, [file, className, ignoreCss]);

//   return <div ref={previewRef} style={style} />;
// };

// export default DocxPreview;

import { useEffect, useRef } from "react";
import { renderAsync } from "docx-preview";

interface DocxPreviewProps {
  file: File | ArrayBuffer | Blob | null;
  className?: string;
  ignoreCss?: boolean;
  style?: React.CSSProperties;
  highlightWords?: string[];
  scrollToIndex?: number; // 新增：默认滚动到第几个高亮（0 开始）
  onHighlightsReady?: (ids: string[]) => void;
}

const DocxPreview: React.FC<DocxPreviewProps> = ({
  file,
  className = "my-docx-preview",
  ignoreCss = false,
  style = { width: "100%", height: "400px", overflow: "auto" },
  highlightWords = [],
  scrollToIndex = 0,
  onHighlightsReady,
}) => {
  const previewRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    if (!file || !previewRef.current) return;

    previewRef.current.innerHTML = "";

    renderAsync(file, previewRef.current, null, { className, ignoreCss })
      .then(() => {
        console.log("DOCX 渲染成功");

        if (highlightWords.length > 0) {
          const ids = highlightInDoc(previewRef.current!, highlightWords);
          onHighlightsReady?.(ids);

          // 自动滚动到指定索引的高亮
          if (ids.length > 0) {
            const index = scrollToIndex ?? 0; // 默认 0
            const safeIndex = Math.min(Math.max(index, 0), ids.length - 1);
            scrollToMark(previewRef.current!, ids[safeIndex]);
          }
        }
      })
      .catch((err) => console.error("DOCX 渲染失败:", err));
  }, [file, className, ignoreCss, highlightWords, scrollToIndex]);

  return <div ref={previewRef} style={style} />;
};

// --- 高亮工具函数 ---
function escapeRegExp(s: string) {
  return s.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
}

function highlightInDoc(container: HTMLElement, words: string[]): string[] {
  const walker = document.createTreeWalker(
    container,
    NodeFilter.SHOW_TEXT,
    null
  );
  const regex = new RegExp(`(${words.map(escapeRegExp).join("|")})`, "gi");

  let node: Node | null;
  const nodesToProcess: Text[] = [];
  const ids: string[] = [];
  let index = 0;

  while ((node = walker.nextNode())) {
    if (node.nodeType === Node.TEXT_NODE && regex.test(node.nodeValue || "")) {
      nodesToProcess.push(node as Text);
    }
  }

  nodesToProcess.forEach((textNode) => {
    const parent = textNode.parentNode;
    if (!parent) return;

    const frag = document.createDocumentFragment();
    const parts = (textNode.nodeValue || "").split(regex);

    parts.forEach((part) => {
      if (!part) return frag.appendChild(document.createTextNode(""));

      if (words.some((w) => w.toLowerCase() === part.toLowerCase())) {
        const mark = document.createElement("mark");
        mark.textContent = part;
        mark.style.background = "yellow";
        mark.style.cursor = "pointer";

        const id = `highlight-${index++}`;
        mark.id = id;
        ids.push(id);

        frag.appendChild(mark);
      } else {
        frag.appendChild(document.createTextNode(part));
      }
    });

    parent.replaceChild(frag, textNode);
  });

  return ids;
}

// --- 滚动 ---
function scrollToMark(container: HTMLElement, id: string) {
  const el = document.getElementById(id);
  if (!el) return;

  const rect = el.getBoundingClientRect();
  const containerRect = container.getBoundingClientRect();
  const offset = rect.top - containerRect.top;

  container.scrollTo({
    top: container.scrollTop + offset - container.clientHeight / 2,
    behavior: "smooth",
  });
}
function scrollToFirstMatch(container: HTMLElement, words: string[]) {
  const walker = document.createTreeWalker(
    container,
    NodeFilter.SHOW_TEXT,
    null
  );
  let node: Node | null;

  while ((node = walker.nextNode())) {
    const text = node.nodeValue || "";
    for (const word of words) {
      const index = text.toLowerCase().indexOf(word.toLowerCase());
      if (index !== -1) {
        const range = document.createRange();
        range.setStart(node, index);
        range.setEnd(node, index + word.length);

        const rect = range.getBoundingClientRect();
        const containerRect = container.getBoundingClientRect();
        const offset = rect.top - containerRect.top;

        container.scrollTo({
          top: container.scrollTop + offset - container.clientHeight / 2,
          behavior: "smooth",
        });

        return; // 只滚动到第一个匹配
      }
    }
  }
}

export default DocxPreview;
