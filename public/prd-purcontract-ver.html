<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>合同比对系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        #sContractBtn,
        #pContractBtn {
            margin-top: 10px;
        }

        .contract-info {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .contract-info div {
            flex: 1 1 calc(50% - 10px);
            background-color: #f9f9f9;
            padding: 10px;
            border-radius: 5px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .contract-info div div {
            flex: 1 1 calc(50% - 10px);
            background-color: #f9f9f9;
            padding: 10px;
            border-radius: 5px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .contract-info p {
            margin: 0;
        }

        .contract-info .label {
            font-size: 0.875rem;
            color: #6b7280;
        }

        .contract-info .value {
            font-size: 1rem;
            font-weight: 500;
        }

        .modal {
            display: none;
        }

        .modal.active {
            display: flex;
        }
    </style>
</head>

<body class="bg-gray-100 p-6">

    <!-- Tab Navigation -->
    <div class="mb-8">
        <ul class="flex border-b" id="contractTabs">
            <li class="-mb-px mr-1">
                <a class="bg-white inline-block border-l border-t border-r rounded-t py-2 px-4 text-blue-500 text-blue-700 font-semibold"
                    href="#sContractTab">销售合同</a>
            </li>
            <li class="mr-1">
                <a class="bg-white inline-block py-2 px-4 text-blue-500 hover:text-blue-800 font-semibold"
                    href="#pContractTab">采购合同</a>
            </li>
            <li class="mr-1">
                <a class="bg-white inline-block py-2 px-4 text-blue-500 hover:text-blue-800 font-semibold"
                    href="#riskCheckTab">风险校验结果</a>
            </li>
        </ul>
    </div>

    <!-- Tab Content -->
    <div id="sContractTab" class="tab-content">
        <div class="bg-white p-6 rounded-lg shadow">
            <h2 class="text-lg font-semibold mb-8">销售合同上传</h2>
            <input type="file" accept=".pdf" id="sContractfile"
                class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100" />
            <button id="sContractBtn"
                class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">确认上传</button>
            <!-- 销售合同信息 -->
            <div class="bg-white p-6 rounded-lg shadow" style="margin-top: 10px;">
                <h2 class="text-lg font-semibold mb-8">销售合同信息</h2>
                <div class="mb-8 contract-info" id="sContractBox">
                    暂无数据
                </div>
                <div class="overflow-x-auto">
                    <div id="deviceBox" class="overflow-x-auto"></div>
                </div>
            </div>
        </div>
    </div>

    <div id="pContractTab" class="tab-content hidden">
        <div class="bg-white p-6 rounded-lg shadow">
            <h2 class="text-lg font-semibold mb-8">采购合同上传</h2>
            <input type="file" accept=".pdf" multiple id="pContractfile"
                class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100" />
            <button id="pContractBtn"
                class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">确认上传</button>
            <!-- 采购合同信息 -->
            <div class="bg-white p-6 rounded-lg shadow" style="margin-top: 10px;">
                <h2 class="text-lg font-semibold mb-8">采购合同信息</h2>
                <div class="mb-8" id="pContractBox">
                    暂无数据
                </div>
            </div>
        </div>
    </div>

    <div id="riskCheckTab" class="tab-content hidden">
        <div class="bg-white p-6 rounded-lg shadow">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-lg font-semibold">风险校验结果</h2>
                <button id="riskCheckBtn"
                    class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">执行风险校验</button>
            </div>

            <div class="space-y-6">
                <!-- 匹配条目 -->
                <div>
                    <h3 class="font-medium mb-3">匹配条目</h3>
                    <div class="overflow-x-auto" id="matchItemsBox">
                        暂无数据
                    </div>
                </div>

                <!-- 无对应记录条目 -->
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <h3 class="font-medium mb-3">无对应销售记录条目</h3>
                        <div class="bg-yellow-50 p-4 rounded-md">
                            <ul class="list-disc list-inside space-y-1 text-sm" id="noSalesItemsBox">
                                暂无数据
                            </ul>
                        </div>
                    </div>
                    <div>
                        <h3 class="font-medium mb-3">无对应采购记录条目</h3>
                        <div class="bg-yellow-50 p-4 rounded-md">
                            <ul class="list-disc list-inside space-y-1 text-sm" id="noPurchaseItemsBox">
                                暂无数据
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- 总毛利 -->
                <div class="mt-6 p-4 bg-gray-50 rounded-md">
                    <p class="text-lg font-semibold">
                        总毛利：<span class="text-green-600" id="tGross">暂无数据</span>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <div id="errorModal" class="modal fixed inset-0 bg-black bg-opacity-50 items-center justify-center">
        <div style="width: 500px;" class="bg-white rounded-lg p-6 max-w-sm mx-auto relative">
            <p id="errorMessage" class="mb-4"></p>
            <div class="mb-4">
                <label for="keyInput" class="block text-sm font-medium text-gray-700 mb-1">请输入新的Key:</label>
                <input type="text" id="keyInput" class="w-full px-3 py-2 border border-gray-300 rounded-md">
            </div>
            <div class="flex justify-end space-x-2">
                <button id="cancelModal"
                    class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">取消</button>
                <button id="confirmKey" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">确认</button>
            </div>
        </div>
    </div>

    <script>
        // 存储当前URL参数
        const urlParams = new URLSearchParams(window.location.search);

        // 获取弹窗相关元素
        const cancelModalButton = document.getElementById('cancelModal');
        const confirmKeyButton = document.getElementById('confirmKey');
        const modal = document.getElementById('errorModal');
        const errorMessage = document.getElementById('errorMessage');
        const keyInput = document.getElementById('keyInput');

        // 动态显示弹窗并传入错误信息  
        function showErrorModal (message) {
            errorMessage.textContent = message;
            modal.classList.add('active');
        }

        cancelModalButton.addEventListener('click', () => {
            modal.classList.remove('active');
        });

        confirmKeyButton.addEventListener('click', () => {
            const newKey = keyInput.value.trim();
            if (newKey) {
                // 更新URL中的key参数
                urlParams.set('key', newKey);
                window.history.replaceState({}, '', `${window.location.pathname}?${urlParams.toString()}`);

                // 关闭弹窗
                modal.classList.remove('active');

                // 可以在这里添加重新尝试操作的逻辑
                // 例如: retryLastOperation();
            } else {
                alert('请输入有效的Key');
            }
        });

        // Optional: Close modal when clicking outside of it  
        modal.addEventListener('click', (event) => {
            if (event.target === modal) {
                modal.classList.remove('active');
            }
        });

        function extractContent (str, tag) {
            // 创建正则表达式以匹配指定标签内的内容
            const regex = new RegExp(`<${tag}>(.*?)</${tag}>`, 'gs')
            const matches = []
            let match = null

            // 使用正则表达式提取内容
            while ((match = regex.exec(str)) !== null) {
                matches.push(match[1].trim()) // match[1] 是标签内的内容
            }

            return matches[0] || ''
        }

        // 上传接口
        async function uploadFile (apikey, file) {
            const formData = new FormData();
            formData.append("file", file);
            formData.append(
                "user",
                "web-user-" + Math.random().toString(36).substr(2, 9)
            );

            try {
                const response = await fetch(
                    "https://copilot.sino-bridge.com:90/v1/files/upload",
                    {
                        method: "POST",
                        headers: {
                            Authorization: `Bearer ${apikey}`,
                        },
                        body: formData,
                    }
                );

                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`HTTP错误 ${response.status}: ${errorText}`);
                }

                const data = await response.json();
                return data.id; // 假设返回的JSON中包含文件ID
            } catch (error) {
                console.error("文件上传错误", error);
                throw error;
            }
        }

        // 返回结果
        async function callChatMessagesAPI (apikey, query, inputs, fileIds) {
            const key = urlParams.get('key')
            if (!key) {
                showErrorModal('对不起,您的key无效或已过期')
                return
            }
            // const resultContainer = document.getElementById("resultContainer");
            const sContractBox = document.getElementById("sContractBox");
            const deviceBox = document.getElementById("deviceBox");
            const pContractBox = document.getElementById("pContractBox");
            const pDeviceBox = document.getElementById("pDeviceBox");

            // 重置容器
            // resultContainer.innerHTML = "正在加载...";
            sContractBox.innerHTML = "正在加载...";
            deviceBox.innerHTML = "";

            try {
                const files = fileIds.map((fileId) => ({
                    type: "document",
                    transfer_method: "local_file",
                    upload_file_id: fileId,
                }));

                const response = await fetch(
                    "https://copilot.sino-bridge.com:90/v1/chat-messages",
                    {
                        method: "POST",
                        headers: {
                            Authorization: `Bearer ${apikey}`,
                            "Content-Type": "application/json",
                        },
                        body: JSON.stringify({
                            inputs: { type: "1", key },
                            query: query,

                            response_mode: "blocking", // 阻塞模式
                            conversation_id: "",
                            user: "web-user-" + Math.random().toString(36).substr(2, 9),
                            files: files,
                        }),
                    }
                );

                if (!response.ok) {
                    // 尝试解析错误响应体
                    const errorText = await response.text();
                    throw new Error(`HTTP错误 ${response.status}: ${errorText}`);
                }

                // 解析响应
                const data = await response.json();

                const errorStr = extractContent(data.answer, 'error')
                if (errorStr) {
                    showErrorModal(errorStr)
                    return
                }


                // 提取JSON数据并生成表格
                const jsonData = JSON.parse(
                    data.answer.replace(/```json\n|```/g, "")
                );
                console.log("解析的JSON数据:", jsonData);
                // 确保jsonData是数组
                const items = Array.isArray(jsonData) ? jsonData : [jsonData];

                // type=1动态渲染结果
                sContractBox.innerHTML = `
                    <div>
                        <p class="label">合同类型</p>
                        <p class="value">${items[0].合同类型}</p>
                    </div>
                    <div>
                        <p class="label">合同金额</p>
                        <p class="value">${items[0].合同金额}</p>
                    </div>
                    <div>
                        <p class="label">合同日期</p>
                        <p class="value">${items[0].合同日期}</p>
                    </div>
                    <div>
                        <p class="label">客户名</p>
                        <p class="value">${items[0].客户名}</p>
                    </div>
                `;



                if (items.length > 0 && items[0].设备清单) {
                    const table = document.createElement("table");
                    table.classList.add(
                        "min-w-full",
                        "bg-white",
                        "divide-y",
                        "divide-gray-200"
                    );

                    // 动态创建表头
                    const thead = document.createElement("thead");
                    thead.classList.add("bg-gray-50");
                    const headerRow = document.createElement("tr");
                    const headers = Object.keys(items[0].设备清单[0]);
                    headers.forEach((headerText) => {
                        const th = document.createElement("th");
                        th.classList.add(
                            "px-3",
                            "py-2",
                            "text-left",
                            "text-xs",
                            "font-medium",
                            "text-gray-500",
                            "uppercase"
                        );
                        th.textContent = headerText;
                        headerRow.appendChild(th);
                    });
                    thead.appendChild(headerRow);
                    table.appendChild(thead);

                    // 创建表体
                    const tbody = document.createElement("tbody");
                    tbody.classList.add("bg-white", "divide-y", "divide-gray-200");

                    items[0].设备清单.forEach((device) => {
                        const row = document.createElement("tr");
                        headers.forEach((header) => {
                            const td = document.createElement("td");
                            td.classList.add("px-3", "py-2");
                            td.textContent = device[header];
                            row.appendChild(td);
                        });
                        tbody.appendChild(row);
                    });

                    table.appendChild(tbody);
                    deviceBox.appendChild(table);
                } else {
                    console.log("没有设备清单数据");
                }
            } catch (error) {
                // 详细的错误处理
                console.error("查询错误", error);
                resultContainer.innerHTML = `错误: ${error.message}`;
            }
        }

        // 返回结果
        async function pccallChatMessagesAPI (apikey, query, inputs, fileIds) {
            const key = urlParams.get('key')
            if (!key) {
                showErrorModal('对不起,您的key无效或已过期')
                return
            }
            // const resultContainer = document.getElementById("resultContainer");
            const pContractBox = document.getElementById("pContractBox");
            // 重置容器
            pContractBox.innerHTML = "正在加载...";
            try {
                const files = fileIds.flat().map((fileId) => ({
                    type: "document",
                    transfer_method: "local_file",
                    upload_file_id: fileId,
                }));

                const response = await fetch(
                    "https://copilot.sino-bridge.com:90/v1/chat-messages",
                    {
                        method: "POST",
                        headers: {
                            Authorization: `Bearer ${apikey}`,
                            "Content-Type": "application/json",
                        },
                        body: JSON.stringify({
                            inputs: { type: "2", key },
                            query: query,

                            response_mode: "blocking", // 阻塞模式
                            conversation_id: "",
                            user: "web-user-" + Math.random().toString(36).substr(2, 9),
                            files: files,
                        }),
                    }
                );

                if (!response.ok) {
                    // 尝试解析错误响应体
                    const errorText = await response.text();
                    throw new Error(`HTTP错误 ${response.status}: ${errorText}`);
                }

                // 解析响应
                const data = await response.json();

                const errorStr = extractContent(data.answer, 'error')
                if (errorStr) {
                    showErrorModal(errorStr)
                    return
                }

                const items = JSON.parse(data.answer);
                console.log("解析的JSON数据:", items);
                // 重置容器
                pContractBox.innerHTML = "";
                items.forEach((item, index) => {
                    const contractDiv = document.createElement("div");
                    contractDiv.classList.add("contract-info");
                    contractDiv.innerHTML = `
               
                         <div >
                            <p class="label">合同名称</p>
                            <p class="value">${item.合同名称}</p>
                        </div>
            
                        <div >
                            <p class="label">合同金额</p>
                            <p class="value">${item.合同金额}</p>
                        </div>
                        <div >
                            <p class="label">合同日期</p>
                            <p class="value">${item.合同日期}</p>
                        </div>
                        <div >
                            <p class="label">客户名</p>
                            <p class="value">${item.客户名}</p>
                        </div>
                    `;
                    // 重置容器

                    pContractBox.appendChild(contractDiv);

                    if (item["设备清单"]) {
                        const table = document.createElement("table");
                        table.classList.add(
                            "min-w-full",
                            "bg-white",
                            "divide-y",
                            "divide-gray-200"
                        );

                        // 动态创建表头
                        const thead = document.createElement("thead");
                        thead.classList.add("bg-gray-50");
                        const headerRow = document.createElement("tr");
                        const headers = Object.keys(item["设备清单"][0]);
                        headers.forEach((headerText) => {
                            const th = document.createElement("th");
                            th.classList.add(
                                "px-3",
                                "py-2",
                                "text-left",
                                "text-xs",
                                "font-medium",
                                "text-gray-500",
                                "uppercase"
                            );
                            th.textContent = headerText;
                            headerRow.appendChild(th);
                        });
                        thead.appendChild(headerRow);
                        table.appendChild(thead);

                        // 创建表体
                        const tbody = document.createElement("tbody");
                        tbody.classList.add("bg-white", "divide-y", "divide-gray-200");

                        item["设备清单"].forEach((device) => {
                            const row = document.createElement("tr");
                            headers.forEach((header) => {
                                const td = document.createElement("td");
                                td.classList.add("px-3", "py-2");
                                td.textContent = device[header];
                                row.appendChild(td);
                            });
                            tbody.appendChild(row);
                        });

                        table.appendChild(tbody);
                        contractDiv.appendChild(table);
                    } else {
                        console.log("没有设备清单数据");
                    }
                });

            } catch (error) {
                // 详细的错误处理
                console.error("查询错误", error);
            }
        }

        async function exVerAPI (apikey, query, inputs) {
            const key = urlParams.get('key')
            if (!key) {
                showErrorModal('对不起,您的key无效或已过期')
                return
            }
            try {
                const response = await fetch(
                    "https://copilot.sino-bridge.com:90/v1/chat-messages",
                    {
                        method: "POST",
                        headers: {
                            Authorization: `Bearer ${apikey}`,
                            "Content-Type": "application/json",
                        },
                        body: JSON.stringify({
                            inputs: { type: "3", key },
                            query: query,
                            response_mode: "blocking", // 阻塞模式
                            conversation_id: "",
                            user: "web-user-" + Math.random().toString(36).substr(2, 9),
                            files: [],
                        }),
                    }
                );

                if (!response.ok) {
                    // 尝试解析错误响应体
                    const errorText = await response.text();
                    throw new Error(`HTTP错误 ${response.status}: ${errorText}`);
                }

                // 解析响应
                const data = await response.json();

                const errorStr = extractContent(data.answer, 'error')
                if (errorStr) {
                    showErrorModal(errorStr)
                    return
                }

                // 提取JSON数据并生成表格
                const jsonData = JSON.parse(
                    data.answer.replace(/```json\n|```/g, "")
                );
                console.log("解析的JSON数据:", jsonData);
                // 确保jsonData是数组
                const items = Array.isArray(jsonData) ? jsonData : [jsonData];
                console.log("解析的itemJSON数据:", items);
                const matchItemsBox = document.getElementById("matchItemsBox");
                matchItemsBox.innerHTML = ""; // 清空之前的内容

                const table = document.createElement("table");
                table.classList.add("min-w-full", "divide-y", "divide-gray-200");

                // 动态创建表头
                const thead = document.createElement("thead");
                thead.classList.add("bg-gray-50");
                const headerRow = document.createElement("tr");
                const headers = Object.keys(items[0].匹配条目[0]);
                headers.forEach((headerText) => {
                    const th = document.createElement("th");
                    th.classList.add("px-3", "py-2", "text-left", "text-xs", "font-medium", "text-gray-500", "uppercase");
                    th.textContent = headerText;
                    headerRow.appendChild(th);
                });
                thead.appendChild(headerRow);
                table.appendChild(thead);

                // 创建表体
                const tbody = document.createElement("tbody");
                tbody.classList.add("bg-white", "divide-y", "divide-gray-200");

                items[0].匹配条目.forEach((item) => {
                    const row = document.createElement("tr");
                    headers.forEach((header) => {
                        const td = document.createElement("td");
                        td.classList.add("px-3", "py-2");
                        td.textContent = item[header];
                        row.appendChild(td);
                    });
                    tbody.appendChild(row);
                });

                table.appendChild(tbody);
                matchItemsBox.appendChild(table);

                // 动态渲染无对应销售记录条目
                const noSalesItemsBox = document.getElementById("noSalesItemsBox");
                noSalesItemsBox.innerHTML = ""; // 清空之前的内容
                items[0].无对应销售记录条目.forEach((item) => {
                    const li = document.createElement("li");
                    li.textContent = `${item.产品名称} (${item.产品型号})`;
                    noSalesItemsBox.appendChild(li);
                });

                // 动态渲染无对应采购记录条目
                const noPurchaseItemsBox = document.getElementById("noPurchaseItemsBox");
                noPurchaseItemsBox.innerHTML = ""; // 清空之前的内容
                items[0].无对应采购记录条目.forEach((item) => {
                    const li = document.createElement("li");
                    li.textContent = `${item.产品名称} (${item.产品型号}) `;
                    noPurchaseItemsBox.appendChild(li);
                });

                // 动态渲染总毛利
                const tGross = document.getElementById("tGross");
                tGross.textContent = `¥${items[0].总毛利}`;

            } catch (error) {
                // 详细的错误处理
                console.error("查询错误", error);
            }
        }


        // 这里可以添加文件上传和风险校验的交互逻辑
        document.addEventListener("DOMContentLoaded", function () {
            const key = new URLSearchParams(window.location.search).get('key')
            if (!key) {
                showErrorModal('对不起,您的key无效或已过期')
            }

            // 文件上传处理
            const salesFileInput = document.getElementById("sContractfile");
            const salesFileButton = document.getElementById("sContractBtn");
            let uploadedFileId = null;

            salesFileInput.addEventListener("change", async function (e) {
                const file = e.target.files[0];
                if (file) {
                    try {
                        uploadedFileId = await uploadFile(
                            "app-QYzAgeFaRWFTTFO8zbzEdUUN",
                            file
                        );
                        console.log("销售合同文件已上传:", uploadedFileId);
                    } catch (error) {
                        console.error("文件上传失败:", error);
                    }
                }
            });

            salesFileButton.addEventListener("click", function () {
                if (uploadedFileId) {
                    callChatMessagesAPI(
                        "app-QYzAgeFaRWFTTFO8zbzEdUUN",
                        "默认参数1",
                        { type: 1 },
                        [uploadedFileId]
                    );
                } else {
                    alert("请先上传销售合同文件");
                }
            });

            // 采购合同文件上传处理
            const purchaseFileInput = document.getElementById("pContractfile");
            const purchaseFileButton = document.getElementById("pContractBtn");
            let uploadedPurchaseFileId = null;

            purchaseFileInput.addEventListener("change", async function (e) {
                const files = e.target.files;
                if (files.length > 0) {
                    try {
                        const fileIds = [];
                        for (const file of files) {
                            const fileId = await uploadFile(
                                "app-QYzAgeFaRWFTTFO8zbzEdUUN",
                                file
                            );
                            fileIds.push(fileId);
                            console.log("采购合同文件已上传:", fileId);
                        }
                        uploadedPurchaseFileId = fileIds;
                    } catch (error) {
                        console.error("文件上传失败:", error);
                    }
                }
            });

            purchaseFileButton.addEventListener("click", function () {
                if (uploadedPurchaseFileId) {
                    pccallChatMessagesAPI(
                        "app-QYzAgeFaRWFTTFO8zbzEdUUN",
                        "默认参数2",
                        { type: 2 },
                        [uploadedPurchaseFileId]
                    );
                } else {
                    alert("请先上传采购合同文件");
                }
            });

            const riskCheckButton = document.getElementById("riskCheckBtn");
            riskCheckButton.addEventListener("click", function () {
                const sContractBox = document.getElementById("sContractBox").innerText.trim();
                const pContractBox = document.getElementById("pContractBox").innerText.trim();

                if (sContractBox === "暂无数据" || pContractBox === "暂无数据") {
                    alert("请先上传销售合同和采购合同文件");
                    return;
                }

                const query = '1、销售合同：' + sContractBox + '2、采购合同：' + pContractBox;
                exVerAPI("app-QYzAgeFaRWFTTFO8zbzEdUUN", query, { type: 3 });
            });

            const tabs = document.querySelectorAll("#contractTabs a");
            const tabContents = document.querySelectorAll(".tab-content");

            tabs.forEach(tab => {
                tab.addEventListener("click", function (e) {
                    e.preventDefault();
                    const targetTab = this.getAttribute("href");

                    if (targetTab === "#riskCheckTab") {
                        const sContractBox = document.getElementById("sContractBox").innerText.trim();
                        const pContractBox = document.getElementById("pContractBox").innerText.trim();

                        if (sContractBox === "暂无数据" || pContractBox === "暂无数据") {
                            alert("请先上传销售合同和采购合同文件");
                            return;
                        }
                    }

                    tabs.forEach(t => t.classList.remove("border-l", "border-t", "border-r", "rounded-t", "text-blue-700"));
                    tabContents.forEach(tc => tc.classList.add("hidden"));

                    this.classList.add("border-l", "border-t", "border-r", "rounded-t", "text-blue-700");
                    document.querySelector(targetTab).classList.remove("hidden");
                });
            });
        });
    </script>
</body>

</html>