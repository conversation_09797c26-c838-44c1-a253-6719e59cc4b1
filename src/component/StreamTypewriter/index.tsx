import { useEffect, useRef, useState, memo } from 'react'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import rehypeRaw from 'rehype-raw'
import './index.less'

const StreamTypewriter = ({
  text = '',
  speed = 10,
  end = false,
  stop = false, // 新增 stop停止输出 属性
  components = {},
  onchange = () => {},
  onFinished = () => {},
  charsPerUpdate = 1 // 每次更新的字符数量
}) => {
  const [displayText, setDisplayText] = useState('')
  const processingIndex = useRef(0)
  const prevText = useRef('')
  const nodeEnd = useRef(false)
  const [cursorVisible, setCursorVisible] = useState(true)

  // 处理文本差异
  const getNewContent = () => {
    const newPart = text.slice(prevText.current.length)
    prevText.current = text // 更新已处理的基准文本
    return newPart
  }

  // 打字机核心逻辑
  useEffect(() => {
    let animator: NodeJS.Timeout | undefined = undefined

    if (text.length === 0) {
      setDisplayText('')
      return
    }

    const typeNextChar = () => {
      if (processingIndex.current < text.length && !stop) {
        // 如果 stop 为 true，停止流式输出
        setDisplayText(prev => {
          const endSplitIndex = Math.min(processingIndex.current + charsPerUpdate, text.length)
          const charsToAdd = text.slice(processingIndex.current, endSplitIndex)
          processingIndex.current = endSplitIndex
          onchange?.()
          return prev + charsToAdd
        })

        // 如果还有剩余字符，继续调用 typeNextChar
        animator = setTimeout(typeNextChar, speed)
      }
      // 检查是否到达文本末尾
      if (processingIndex.current >= text.length) {
        nodeEnd.current = true
      }
    }

    // 确保动画启动时检查条件
    if (getNewContent().length > 0 && !stop) {
      nodeEnd.current = false
      animator = setTimeout(typeNextChar, speed)
    }

    // 清理定时器
    return () => {
      if (animator && processingIndex.current >= text.length) {
        clearTimeout(animator)
      }
    }
  }, [text, speed, stop])

  const cursorTimer = useRef<NodeJS.Timeout | undefined>(undefined)

  useEffect(() => {
    if (end && nodeEnd.current && processingIndex.current >= text.length) {
      clearInterval(cursorTimer.current)
      setCursorVisible(false)
      onFinished?.()
    }
  }, [end, nodeEnd.current, cursorTimer.current, processingIndex.current, text])

  // 光标闪烁效果
  useEffect(() => {
    cursorTimer.current = setInterval(() => {
      setCursorVisible(v => !v)
    }, 500)
    return () => clearInterval(cursorTimer.current)
  }, [])

  return (
    <>
      <ReactMarkdown
        className='markdown-body'
        components={{
          ...components
        }}
        remarkPlugins={[remarkGfm]}
        rehypePlugins={[rehypeRaw]}
      >
        {displayText}
      </ReactMarkdown>
      <span
        style={{
          visibility: cursorVisible ? 'visible' : 'hidden',
          color: '#222',
          fontWeight: 600,
          marginLeft: 2
        }}
      >
        |
      </span>
    </>
  )
}

export default memo(StreamTypewriter)
