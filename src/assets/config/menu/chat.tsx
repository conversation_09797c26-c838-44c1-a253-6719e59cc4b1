/** 聊天图标 */
import type { JSX } from 'react'

interface SelectType {
  selected: JSX.Element
  unselected: JSX.Element
}

/** 生成 */
export const createChatIcon: SelectType = {
  selected: (
    <svg width='20px' height='20px' viewBox='0 0 20 20' version='1.1' xmlns='http://www.w3.org/2000/svg'>
      <g stroke='none' strokeWidth='1' fill='none' fillRule='evenodd'>
        <g transform='translate(-624.000000, -346.000000)'>
          <g transform='translate(624.000000, 346.000000)'>
            <path fill='#FFFFFF' d='M0,0 L20,0 L20,20 L0,20 L0,0 Z' opacity='0'></path>
            <g transform='translate(2.248509, 3.098043)' fill='#1888ff'>
              <path
                d='M1.46787316,0.0930472563 C0.605027679,-0.290398811 -0.281934747,0.579942568 0.0851114007,1.44988488 L2.35145728,6.82139 C2.37330437,6.87317054 2.37330457,6.93158309 2.35145688,6.98336443 L0.085111053,12.3548706 C-0.281934946,13.2248131 0.605027878,14.0951538 1.46787266,13.7117068 L14.6484884,7.85428127 C15.4733435,7.48771906 15.4733515,6.31703973 14.6484915,5.95047315 L1.46787316,0.0930472563 Z M5.66813151,6.27726198 C5.32295386,6.27726198 5.04313151,6.55708353 5.04313151,6.90226237 C5.04313151,7.24743923 5.32295347,7.52726237 5.66813151,7.52726237 L8.16813151,7.52726237 C8.51330916,7.52726237 8.79313151,7.24744082 8.79313151,6.90226237 C8.79313151,6.55708472 8.51330996,6.27726198 8.16813151,6.27726198 L5.66813151,6.27726198 Z'
                id='Vector'
              ></path>
            </g>
          </g>
        </g>
      </g>
    </svg>
  ),
  unselected: (
    <svg width='20px' height='20px' viewBox='0 0 20 20' version='1.1' xmlns='http://www.w3.org/2000/svg'>
      <g stroke='none' strokeWidth='1' fill='none' fillRule='evenodd'>
        <g transform='translate(-624.000000, -346.000000)'>
          <g transform='translate(624.000000, 346.000000)'>
            <path d='M0,0 L20,0 L20,20 L0,20 L0,0 Z' id='路径' opacity='0' fill='#FFFFFF'></path>
            <g transform='translate(2.248509, 3.098043)' fill='#ABABAB'>
              <path
                d='M1.46787316,0.0930472563 C0.605027679,-0.290398811 -0.281934747,0.579942568 0.0851114007,1.44988488 L2.35145728,6.82139 C2.37330437,6.87317054 2.37330457,6.93158309 2.35145688,6.98336443 L0.085111053,12.3548706 C-0.281934946,13.2248131 0.605027878,14.0951538 1.46787266,13.7117068 L14.6484884,7.85428127 C15.4733435,7.48771906 15.4733515,6.31703973 14.6484915,5.95047315 L1.46787316,0.0930472563 Z M5.66813151,6.27726198 C5.32295386,6.27726198 5.04313151,6.55708353 5.04313151,6.90226237 C5.04313151,7.24743923 5.32295347,7.52726237 5.66813151,7.52726237 L8.16813151,7.52726237 C8.51330916,7.52726237 8.79313151,7.24744082 8.79313151,6.90226237 C8.79313151,6.55708472 8.51330996,6.27726198 8.16813151,6.27726198 L5.66813151,6.27726198 Z'
                id='Vector'
              ></path>
            </g>
          </g>
        </g>
      </g>
    </svg>
  )
}
/** 新聊天 */
export const ChatNewSVGIcon = (
  <svg
    width='13.1333346px'
    height='12.9666634px'
    viewBox='0 0 13.1333346 12.9666634'
    version='1.1'
    xmlns='http://www.w3.org/2000/svg'
  >
    <g stroke='none' strokeWidth='1' fill='none' fillRule='evenodd'>
      <g transform='translate(-1784.433333, -781.600000)'>
        <g transform='translate(1773.000000, 774.000000)'>
          <g transform='translate(10.000000, 6.000000)'>
            <path d='M0,0 L16,0 L16,16 L0,16 L0,0 Z' id='路径'></path>
            <g
              className='stroke-theme'
              transform='translate(1.833333, 2.000000)'
              strokeDasharray='0,0'
              strokeWidth='0.8'
            >
              <path
                d='M6.36466599,9.16613833 L5.47654978,9.16613833 C5.39452171,9.16613833 5.31537406,9.19638443 5.25425084,9.25108846 L2.38896592,11.8154716 C2.17424965,12.0076396 1.83333333,11.8552399 1.83333333,11.5670878 L1.83333333,9.49947166 C1.83333333,9.31537692 1.68409491,9.16613833 1.5,9.16613833 L1.33333333,9.16613833 C0.59695371,9.16613833 0,8.56918462 0,7.832805 L0,1.33333333 C0,0.59695371 0.59695371,0 1.33333333,0 L11,0 C11.7363815,0 12.3333346,0.59695371 12.3333346,1.33333349 L12.3333346,7.832805 C12.3333346,8.10783577 12.250061,8.36341667 12.1073545,8.57571157'
                id='路径'
                strokeLinejoin='round'
                fillRule='nonzero'
              ></path>
              <line
                x1='2.46419271'
                y1='3.63365059'
                x2='7.91085938'
                y2='3.53365059'
                id='vector'
                strokeLinejoin='round'
              ></line>
              <line
                x1='2.46419271'
                y1='5.90029284'
                x2='5.49752604'
                y2='5.80029284'
                id='vector'
                strokeLinejoin='round'
              ></line>
              <path
                d='M12.3331675,9.24584797 C12.3331675,10.8589698 11.0254738,12.1666634 9.412352,12.1666634 C7.79923018,12.1666634 6.49153653,10.8589698 6.49153653,9.24584797 C6.49153653,7.63272614 7.79923018,6.3250325 9.412352,6.3250325 C11.0254738,6.3250325 12.3331675,7.63272614 12.3331675,9.24584797 Z'
                id='路径'
              ></path>
              <line
                x1='8.34244792'
                y1='9.31251615'
                x2='10.4824479'
                y2='9.31251615'
                id='vector'
                strokeLinecap='square'
              ></line>
              <line
                x1='8.34244792'
                y1='9.31251615'
                x2='10.4824479'
                y2='9.31251615'
                id='vector'
                strokeLinecap='square'
                transform='translate(9.412448, 9.312516) rotate(90.000000) translate(-9.412448, -9.312516) '
              ></line>
            </g>
          </g>
        </g>
      </g>
    </g>
  </svg>
)

export const stopChatSVGIcon = (
  <svg width='20px' height='20px' viewBox='0 0 20 20' version='1.1' xmlns='http://www.w3.org/2000/svg'>
    <g stroke='none' strokeWidth='1' fill='none' fillRule='evenodd'>
      <g transform='translate(-592.000000, -314.000000)'>
        <g transform='translate(592.000000, 314.000000)'>
          <rect fill='#D8D8D8' opacity='0' x='0' y='0' width='20' height='20'></rect>
          <path
            className='fill-theme'
            d='M7.99394122,11.9899829 L11.9899829,11.9899829 L11.9899829,7.99394122 L7.99394122,7.99394122 L7.99394122,11.9899829 Z M15.9858544,9.99196206 C15.9858544,6.69590289 13.2888764,3.99789888 9.99196206,3.99789888 C6.69504774,3.99789888 3.99806994,6.69590289 3.99806994,9.99196206 C3.99806994,13.2890473 6.69504774,15.9868805 9.99196206,15.9868805 C13.2888764,15.9868805 15.9858544,13.2888764 15.9858544,9.99196206 Z M10,3 C13.8499914,3 17,6.15000861 17,10 C17,13.8499914 13.8499914,17 10,17 C6.15000861,17 3,13.8498205 3,10 C3,6.15000861 6.15000861,3 10,3 Z'
            id='停止生成icon'
          ></path>
        </g>
      </g>
    </g>
  </svg>
)

export const resetChatSVGIcon = (
  <svg width='20px' height='20px' viewBox='0 0 20 20' version='1.1' xmlns='http://www.w3.org/2000/svg'>
    <g stroke='none' strokeWidth='1' fill='none' fillRule='evenodd'>
      <g className='fill-theme' transform='translate(-653.000000, -282.000000)' fillRule='nonzero'>
        <g transform='translate(653.000000, 282.000000)'>
          <rect opacity='0' x='0' y='0' width='20' height='20'></rect>
          <path
            d='M13.846875,15.2704928 C15.309375,14.0912177 16.25,12.2546417 16.25,10.1925213 C16.25,6.63375257 13.4609375,3.75483916 10.009375,3.7499819 C6.553125,3.74517297 3.75,6.63053051 3.75,10.1925213 C3.75,12.0901898 4.35847789,13.5089304 5.625,14.6875 C5.63396435,14.6958418 5.6455122,14.7075486 5.65964353,14.7226202 C5.85401496,14.9299472 6.17965647,14.9404323 6.38697308,14.7460498 C6.40759063,14.7267185 6.4265805,14.7057215 6.44375,14.6832718 L6.51028506,14.5962749 C6.68601039,14.3665053 6.66701382,14.0427077 6.465625,13.8350637 C6.465625,13.8350637 6.465625,13.8350637 6.465625,13.8350637 C6.00625,13.3614204 5.6453125,12.8104476 5.39375,12.1966446 C5.13125,11.5635092 5,10.8884869 5,10.1925213 C5,9.49655567 5.13125,8.82153344 5.3921875,8.186787 C5.64375,7.57298397 6.0046875,7.02201118 6.4640625,6.5483679 C6.9234375,6.07472462 7.4578125,5.70257632 8.053125,5.44320024 C8.6703125,5.17415797 9.325,5.03883132 10,5.03883132 C10.675,5.03883132 11.3296875,5.17415797 11.9453125,5.44320024 C12.540625,5.70257632 13.075,6.07472462 13.534375,6.5483679 C13.99375,7.02201118 14.3546875,7.57298397 14.60625,8.186787 C14.8671875,8.82153344 14.9984375,9.49655567 14.9984375,10.1925213 C14.9984375,10.8884869 14.8671875,11.5635092 14.60625,12.1982556 C14.3546875,12.8120586 13.99375,13.3630314 13.534375,13.8366747 C13.3890625,13.9865006 13.2359375,14.1266604 13.0765625,14.2555429 L12.440625,13.4161955 C12.3765625,13.3308108 12.2453125,13.3598094 12.2203125,13.4645264 L11.6015625,16.0776196 C11.5828125,16.1581712 11.6421875,16.2371117 11.721875,16.2371117 L14.33125,16.2498761 C14.4359375,16.2498761 14.4953125,16.1259506 14.4296875,16.0421769 L13.846875,15.2704928 Z'
            id='路径'
          ></path>
        </g>
      </g>
    </g>
  </svg>
)

export const picUploadSVGIcon = (
  <svg width='24px' height='24px' viewBox='0 0 24 24' version='1.1' xmlns='http://www.w3.org/2000/svg'>
    <g stroke='none' strokeWidth='1' fill='none' fillRule='evenodd'>
      <g id='OCR' transform='translate(-1692.000000, -339.000000)'>
        <g transform='translate(1553.000000, 297.000000)'>
          <g transform='translate(139.000000, 42.000000)'>
            <path d='M0,0 L24,0 L24,24 L0,24 L0,0 Z' id='路径' fill='#FFFFFF' opacity='0'></path>
            <g transform='translate(3.000000, 4.000000)' stroke='#121212' strokeDasharray='0,0' strokeWidth='1.2'>
              <path
                d='M2.00000027,16 C0.895430713,16 2.67029463e-07,15.1045694 2.67029463e-07,14 L2.67029463e-07,2 C2.67029463e-07,0.895430565 0.89543107,0 2.00000027,0 L16.0000003,0 C17.1045697,0 18.0000003,0.895430803 18.0000003,2 L18.0000003,14 C18.0000003,15.1045694 17.1045697,16 16.0000003,16 L2.00000027,16 Z'
                id='路径'
                fillRule='nonzero'
              ></path>
              <path
                d='M0,12.2060108 L5.87,6.70601076 C5.97,6.60601076 6.08,6.50601076 6.19,6.42601076 C6.59,6.19601076 7.11,6.25601076 7.43,6.59601076 L16.31,15.9960108'
                fillRule='nonzero'
              ></path>
              <path
                d='M12.0000003,11.1279051 L14.7500003,8.83790508 C14.8500003,8.74790508 14.9500003,8.66790508 15.0600003,8.59790508 C15.4500003,8.38790508 15.9300003,8.45790508 16.2500003,8.76790508 L18.0000003,10.5279051'
                fillRule='nonzero'
              ></path>
              <path
                d='M14.0000003,4.625 C14.0000003,5.45342714 13.3284274,6.125 12.5000003,6.125 C11.6715731,6.125 11.0000003,5.45342714 11.0000003,4.625 C11.0000003,3.79657286 11.6715731,3.125 12.5000003,3.125 C13.3284274,3.125 14.0000003,3.79657286 14.0000003,4.625 Z'
                id='路径'
              ></path>
            </g>
          </g>
        </g>
      </g>
    </g>
  </svg>
)

export const HistoryChatSVGIcon = (
  <svg width='20' height='20' viewBox='0 0 20 20' version='1.1' xmlns='http://www.w3.org/2000/svg'>
    <g stroke='none' strokeWidth='1' fill='none' fillRule='evenodd'>
      <g transform='translate(-623.000000, -515.000000)'>
        <g transform='translate(623.000000, 515.000000)'>
          <g transform='translate(2.500000, 2.500000)' stroke='#121212' strokeDasharray='0,0'>
            <path d='M15,7.5 C15,11.6421358 11.6421358,15 7.5,15 C3.35786422,15 0,11.6421358 0,7.5 C0,3.35786422 3.35786422,0 7.5,0 C11.6421358,0 15,3.35786422 15,7.5 Z'></path>
            <path
              d='M12.5,7.5 L8.00833333,7.5 C7.95,7.5 7.89166667,7.5 7.825,7.49166667 C7.64166667,7.45 7.5,7.28333333 7.5,7.08333333 L7.5,2.5'
              fillRule='nonzero'
            ></path>
            <line x1='7.5' y1='12.0833333' x2='7.5' y2='12.5' strokeLinecap='round'></line>
            <line x1='4.46129445' y1='10.740967' x2='4.16666663' y2='11.0355948' strokeLinecap='round'></line>
            <line x1='10.5867515' y1='10.740967' x2='10.8813793' y2='11.0355948' strokeLinecap='round'></line>
            <line x1='2.5' y1='7.6' x2='2.91666667' y2='7.6' strokeLinecap='round' strokeLinejoin='round'></line>
            <line x1='4.46129442' y1='4.25906465' x2='4.16666663' y2='3.96443681' strokeLinecap='round'></line>
            <line x1='10.5867515' y1='4.25906465' x2='10.8813793' y2='3.96443681' strokeLinecap='round'></line>
          </g>
        </g>
      </g>
    </g>
  </svg>
)

export const EditSVGIcon = (
  <svg width='20' height='20' viewBox='0 0 20 20' version='1.1' xmlns='http://www.w3.org/2000/svg'>
    <g stroke='none' strokeWidth='1' fill='none' fillRule='evenodd'>
      <g transform='translate(-560.000000, -544.000000)'>
        <g transform='translate(560.000000, 544.000000)'>
          <g transform='translate(2.314097, 2.313969)' stroke='#121212' strokeDasharray='0,0' strokeLinejoin='round'>
            <path d='M12.5087031,0.244077643 L15.1486174,2.8839916 C15.4740543,3.20942849 15.4740543,3.73706549 15.1486174,4.06250238 L13.1893253,6.02179368 L4.06242688,15.1487255 C3.90614666,15.3050063 3.69418355,15.3928057 3.47316941,15.3928057 L0.417340448,15.3928057 C0.187258422,15.3928057 0.000724454779,15.2063124 0.000673597582,14.9762297 L0,11.9196653 C0,11.698589 0.0877521187,11.4865517 0.244076177,11.3302271 L9.37090079,2.20336914 L11.3301921,0.244077692 C11.655629,-0.0813591977 12.1832662,-0.0813592474 12.5087031,0.244077643 Z M13.1893253,6.02179368 L9.37090079,2.20336914'></path>
            <line x1='3.51857532' y1='15.4928124' x2='15.185242' y2='15.3928124'></line>
          </g>
        </g>
      </g>
    </g>
  </svg>
)
