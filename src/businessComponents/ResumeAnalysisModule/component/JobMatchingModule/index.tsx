import React, { forwardRef, useImperativeHandle, useEffect, useRef } from 'react';
import { Card, Tag, Divider, Row, Col } from 'antd';
import * as echarts from 'echarts';
import './index.less';

export interface JobMatchingModuleRef {
  getMatchingData: () => any;
}

export interface JobMatchingModuleProps {
  viewData?: any;
}

const JobMatchingModule = forwardRef<JobMatchingModuleRef, JobMatchingModuleProps>(({viewData}, ref) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);
  console.log(viewData, '--------viewData岗位匹配组件')

  // 初始化雷达图
  useEffect(() => {
    if (viewData?.dimension_scores_charts && chartRef.current) {
      // 销毁之前的图表实例
      if (chartInstance.current) {
        chartInstance.current.dispose();
      }

      // 创建新的图表实例
      chartInstance.current = echarts.init(chartRef.current);
      
      // 设置图表配置
      const option = {
        backgroundColor: viewData.dimension_scores_charts.backgroundColor || '#fff',
        tooltip: viewData.dimension_scores_charts.tooltip || {},
        radar: {
          center: viewData.dimension_scores_charts.radar.center || ['50%', '50%'],
          radius: viewData.dimension_scores_charts.radar.radius || '70%',
          name: viewData.dimension_scores_charts.radar.name || {},
          indicator: viewData.dimension_scores_charts.radar.indicator || [],
          fontFamily: viewData.dimension_scores_charts.radar.fontFamily || 'Arial'
        },
        series: viewData.dimension_scores_charts.series || []
      };

      chartInstance.current.setOption(option);

      // 响应式处理
      const handleResize = () => {
        chartInstance.current?.resize();
      };
      window.addEventListener('resize', handleResize);

      return () => {
        window.removeEventListener('resize', handleResize);
        if (chartInstance.current) {
          chartInstance.current.dispose();
        }
      };
    }
  }, [viewData]);
  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    getMatchingData: () => {
      return {...viewData}
    }
  }));

  return (
    <div className="job-matching-module">
      <Card className="matching-card">
        {/* 总体匹配度 */}
        <div className="overall-matching">
          <h3>岗位匹配度 {viewData?.score_percent || 0}%</h3>
          {/* <div className="score-display">
            <div className="score-circle">
              <div className="score-number">{viewData?.score_percent || 0}%</div>
              <div className="score-label">总体匹配度</div>
            </div>
          </div> */}
        </div>

        {/* <Divider /> */}

        {/* 匹配理由分析 */}
        <div className="rationale-section">
          {/* <h4>匹配理由分析</h4> */}
          <div className="rationale-list">
            {viewData?.rationale?.map((reason: string, index: number) => (
              <div key={index} className="rationale-item">
                <Tag color="blue" className="rationale-tag">
                  {index + 1}
                </Tag>
                <span className="rationale-text">{reason}</span>
              </div>
            )) || (
              <div className="no-data">
                <p>暂无匹配理由分析</p>
              </div>
            )}
          </div>
        </div>

        {/* <Divider /> */}

        {/* 雷达图 */}
        <div className="radar-chart-section">
          {/* <h4>能力维度分析</h4> */}
          <div className="chart-container">
            <div ref={chartRef} style={{ width: '100%', height: '400px' }} />
          </div>
        </div>
      </Card>
    </div>
  );
});

JobMatchingModule.displayName = 'JobMatchingModule';

export default JobMatchingModule;
