// 添加处理LaTeX命令的方法

export const processLatexCommands = (text: string): string => {
  const latexMap: Record<string, string> = {
    "\\\\": "\n", // 双反斜杠替换为换行
    "\\\\,": " ", // 细空格
    "\\\\:": " ", // 中等空格
    "\\\\;": " ", // 粗空格
    "\\\\!": "", // 负空格
    "\\\\risingdotseq": "一", // 上升点等于
    "\\\\times": "×", // 乘号
    "\\\\%": "%", // 百分号
    "\\\\approx": "≈", // 约等于
    "\\\\sim": "∼", // 相似
    "\\\\equiv": "≡", // 恒等于
    "\\\\neq": "≠", // 不等于
    "\\\\leq": "≤", // 小于等于
    "\\\\geq": "≥", // 大于等于
    "\\\\subset": "⊂", // 子集
    "\\\\supset": "⊃", // 超集
    "\\\\in": "∈", // 属于
    "\\\\notin": "∉", // 不属于
    "\\\\rightarrow": "→", // 右箭头
    "\\\\leftarrow": "←", // 左箭头
    "\\\\Rightarrow": "⇒", // 右双箭头
    "\\\\Leftarrow": "⇐", // 左双箭头
    "\\\\alpha": "α", // 希腊字母
    "\\\\beta": "β",
    "\\\\gamma": "γ",
    "\\\\delta": "δ",
    "\\\\epsilon": "ε",
    "\\\\zeta": "ζ",
    "\\\\eta": "η",
    "\\\\theta": "θ",
    "\\\\iota": "ι",
    "\\\\kappa": "κ",
    "\\\\lambda": "λ",
    "\\\\mu": "μ",
    "\\\\nu": "ν",
    "\\\\xi": "ξ",
    "\\\\pi": "π",
    "\\\\rho": "ρ",
    "\\\\sigma": "σ",
    "\\\\tau": "τ",
    "\\\\upsilon": "υ",
    "\\\\phi": "φ",
    "\\\\chi": "χ",
    "\\\\psi": "ψ",
    "\\\\omega": "ω",
    "\\\\Gamma": "Γ",
    "\\\\Delta": "Δ",
    "\\\\Theta": "Θ",
    "\\\\Lambda": "Λ",
    "\\\\Xi": "Ξ",
    "\\\\Pi": "Π",
    "\\\\Sigma": "Σ",
    "\\\\Upsilon": "Υ",
    "\\\\Phi": "Φ",
    "\\\\Psi": "Ψ",
    "\\\\Omega": "Ω",
    "\\\\pm": "±", // 正负号
    "\\\\mp": "∓", // 负正号
    "\\\\infty": "∞", // 无穷大
    "\\\\partial": "∂", // 偏微分
    "\\\\nabla": "∇", // 梯度
    "\\\\forall": "∀", // 全称量词
    "\\\\exists": "∃", // 存在量词
    "\\\\emptyset": "∅", // 空集
    "\\\\int": "∫", // 积分
    "\\\\oint": "∮", // 环路积分
    "\\\\sum": "∑", // 求和
    "\\\\prod": "∏", // 乘积
    "\\\\sqrt": "√", // 平方根
    "\\\\cdot": "·", // 点乘
    "\\\\circ": "∘", // 圆圈
    "\\\\angle": "∠", // 角度
    "\\\\parallel": "∥", // 平行
    "\\\\perp": "⊥", // 垂直
    "\\\\prime": "′", // 撇号
    "\\\\hbar": "ℏ", // 约化普朗克常数
    "\\\\imath": "ı", // 无点i
    "\\\\jmath": "ȷ", // 无点j
    "\\\\ell": "ℓ", // 手写体l
    "\\\\Re": "ℜ", // 实部
    "\\\\Im": "ℑ", // 虚部
    "\\\\aleph": "ℵ", // 阿列夫数
    "\\\\wp": "℘", // Weierstrass椭圆函数
    "\\\\otimes": "⊗", // 张量积
    "\\\\oplus": "⊕", // 直和
    "\\\\ominus": "⊖", // 直差
    "\\\\oslash": "⊘", // 除法
    "\\\\odot": "⊙", // 点积
    "\\\\bigcirc": "○", // 大圆圈
    "\\\\diamond": "⋄", // 菱形
    "\\\\triangle": "△", // 三角形
    "\\\\bigtriangledown": "▽", // 倒三角形
    "\\\\star": "⋆", // 星号
    "\\\\ast": "∗", // 星号
    "\\\\bullet": "•", // 实心点
    "\\\\div": "÷", // 除号
    "\\\\cap": "∩", // 交集
    "\\\\cup": "∪", // 并集
    "\\\\lor": "∨", // 或
    "\\\\land": "∧", // 与
    "\\\\neg": "¬", // 非
    "\\\\therefore": "∴", // 所以
    "\\\\because": "∵", // 因为
    "\\\\mapsto": "↦", // 映射到
    "\\\\to": "→", // 到
    "\\\\gets": "←", // 从
    "\\\\leftrightarrow": "↔", // 左右箭头
    "\\\\Leftrightarrow": "⇔", // 左右双箭头
    "\\\\uparrow": "↑", // 上箭头
    "\\\\downarrow": "↓", // 下箭头
    "\\\\updownarrow": "↕", // 上下箭头
    "\\\\Uparrow": "⇑", // 上双箭头
    "\\\\Downarrow": "⇓", // 下双箭头
    "\\\\Updownarrow": "⇕", // 上下双箭头
    "\\\\lfloor": "⌊", // 左地板符号
    "\\\\rfloor": "⌋", // 右地板符号
    "\\\\lceil": "⌈", // 左天花板符号
    "\\\\rceil": "⌉", // 右天花板符号
    "\\\\langle": "⟨", // 左角括号
    "\\\\rangle": "⟩", // 右角括号
    "\\\\vert": "|", // 竖线
    "\\\\Vert": "‖", // 双竖线
    "\\\\{": "{", // 左花括号
    "\\\\}": "}", // 右花括号
    "\\\\backslash": "\\", // 反斜杠
    "\\\\ldots": "…", // 水平省略号
    "\\\\cdots": "⋯", // 居中省略号
    "\\\\vdots": "⋮", // 垂直省略号
    "\\\\ddots": "⋱", // 对角线省略号
    "\\\\hat": "^", // 帽子
    "\\\\bar": "¯", // 上划线
    "\\\\vec": "→", // 向量
    "\\\\dot": "˙", // 点
    "\\\\ddot": "¨", // 双点
    "\\\\tilde": "~", // 波浪线
    "\\\\acute": "´", // 尖音
    "\\\\grave": "`", // 重音
    "\\\\check": "ˇ", // 反折音
    "\\\\breve": "˘", // 短音
    "\\\\mathring": "˚", // 圆圈
    "\\\\text": "", // 文本模式（移除）
    "\\\\mathrm": "", // 罗马体（移除）
    "\\\\mathbf": "", // 粗体（移除）
    "\\\\mathit": "", // 斜体（移除）
    "\\\\mathcal": "", // 手写体（移除）
    "\\\\mathbb": "", // 黑板粗体（移除）
    "\\\\mathfrak": "", // 哥特体（移除）
    "\\\\mathscr": "", // 草书体（移除）
    "\\\\mathsf": "", // 无衬线体（移除）
    "\\\\mathtt": "", // 打字机体（移除）
    "\\\\rm": "", // 罗马体（移除）
    "\\\\bf": "", // 粗体（移除）
    "\\\\it": "", // 斜体（移除）
    "\\\\cal": "", // 手写体（移除）
    "\\\\bb": "", // 黑板粗体（移除）
    "\\\\frak": "", // 哥特体（移除）
    "\\\\scr": "", // 草书体（移除）
    "\\\\sf": "", // 无衬线体（移除）
    "\\\\tt": "", // 打字机体（移除）
    "\\\\normalsize": "", // 正常大小（移除）
    "\\\\large": "", // 大号（移除）
    "\\\\Large": "", // 更大号（移除）
    "\\\\LARGE": "", // 最大号（移除）
    "\\\\huge": "", // 巨大号（移除）
    "\\\\Huge": "", // 超巨大号（移除）
    "\\\\small": "", // 小号（移除）
    "\\\\footnotesize": "", // 脚注大小（移除）
    "\\\\scriptsize": "", // 脚本大小（移除）
    "\\\\tiny": "", // 极小号（移除）
    "\\\\color": "", // 颜色（移除）
    "\\\\pagecolor": "", // 页面颜色（移除）
    "\\\\textcolor": "", // 文本颜色（移除）
    "\\\\boxed": "", // 框（移除）
    "\\\\label": "", // 标签（移除）
    "\\\\ref": "", // 引用（移除）
    "\\\\cite": "", // 引用文献（移除）
    "\\\\tag": "", // 标签（移除）
    "\\\\nonumber": "", // 无编号（移除）
    "\\\\quad": "    ", // 1em空格
    "\\\\qquad": "        ", // 2em空格
    "\\\\enspace": " ", // 0.5em空格
    "\\\\thinspace": " ", // 1/6em空格
    "\\\\medspace": " ", // 2/9em空格
    "\\\\thickspace": " ", // 5/18em空格
    "\\\\negthinspace": "", // 负细空格
    "\\\\negmedspace": "", // 负中等空格
    "\\\\negthickspace": "", // 负粗空格
    "\\\\ ": " ", // 空格
    "\\\\~": "~", // 波浪线
    "\\\\`": "`", // 重音符
    "\\\\'": "'", // 尖音符
    "\\\\^": "^", // 抑扬符
    '\\\\"': '"', // 分音符
    "\\\\=": "=", // 等号
    "\\\\.": ".", // 点
    "\\\\u": "˘", // 短音符
    "\\\\v": "ˇ", // 反折音符
    "\\\\H": "˝", // 双尖音符
    "\\\\t": " ̆", // 连接符
    "\\\\c": "¸", // 下加符
    "\\\\d": " ̣", // 下点
    "\\\\b": " ̲", // 下划线
    "\\\\k": "˛", // 钩形符
    "\\\\r": "˚", // 圆圈
    "\\\\aa": "å", // 带圆圈a
    "\\\\AA": "Å", // 带圆圈A
    "\\\\o": "ø", // 带斜线o
    "\\\\O": "Ø", // 带斜线O
    "\\\\l": "ł", // 带斜线l
    "\\\\L": "Ł", // 带斜线L
    "\\\\ss": "ß", // 德语sharp s
    "\\\\i": "ı", // 无点i
    "\\\\j": "ȷ", // 无点j
    "\\\\ae": "æ", // ae连字
    "\\\\AE": "Æ", // AE连字
    "\\\\oe": "œ", // oe连字
    "\\\\OE": "Œ", // OE连字
    "\\\\dh": "ð", // 冰岛语eth
    "\\\\DH": "Ð", // 冰岛语ETH
    "\\\\th": "þ", // 冰岛语thorn
    "\\\\TH": "Þ", // 冰岛语THORN
    "\\\\ng": "ŋ", // eng
    "\\\\NG": "Ŋ", // ENG
    "\\\\textendash": "–", // 短破折号
    "\\\\textemdash": "—", // 长破折号
    "\\\\textquoteleft": "‘", // 左单引号
    "\\\\textquoteright": "’", // 右单引号
    "\\\\textquotedblleft": "“", // 左双引号
    "\\\\textquotedblright": "”", // 右双引号
    "\\\\textdagger": "†", // 匕首
    "\\\\textdaggerdbl": "‡", // 双匕首
    "\\\\textbullet": "•", // 项目符号
    "\\\\textellipsis": "…", // 省略号
    "\\\\textperthousand": "‰", // 千分号
    "\\\\textpertenthousand": "‱", // 万分号
    "\\\\textcopyright": "©", // 版权符号
    "\\\\textregistered": "®", // 注册商标
    "\\\\texttrademark": "™", // 商标
    "\\\\textordfeminine": "ª", // 阴性序数
    "\\\\textordmasculine": "º", // 阳性序数
    "\\\\textsection": "§", // 章节符号
    "\\\\textparagraph": "¶", // 段落符号
    "\\\\texteuro": "€", // 欧元符号
    "\\\\textyen": "¥", // 日元符号
    "\\\\textcent": "¢", // 分币符号
    "\\\\textdegree": "°", // 度符号
    "\\\\textcelsius": "℃", // 摄氏度
    "\\\\textmu": "μ", // 微符号
    "\\\\textohm": "Ω", // 欧姆符号
    "\\\\textestimated": "℮", // 估计符号
    "\\\\textleftarrow": "←", // 左箭头
    "\\\\textrightarrow": "→", // 右箭头
    "\\\\textuparrow": "↑", // 上箭头
    "\\\\textdownarrow": "↓", // 下箭头
    "\\\\textlangle": "⟨", // 左角括号
    "\\\\textrangle": "⟩", // 右角括号
    "\\\\textbar": "|", // 竖线
    "\\\\textbraceleft": "{", // 左花括号
    "\\\\textbraceright": "}", // 右花括号
    "\\\\textbackslash": "\\", // 反斜杠
    "\\\\textasciitilde": "~", // 波浪线
    "\\\\textasciicircum": "^", // 抑扬符
    "\\\\textunderscore": "_", // 下划线
    "\\\\textvisiblespace": "␣", // 可见空格
    "\\\\textdollar": "$", // 美元符号
    "\\\\textflorin": "ƒ", // 弗罗林符号
    "\\\\textlira": "₤", // 里拉符号
    "\\\\textnaira": "₦", // 奈拉符号
    "\\\\textpeso": "₱", // 比索符号
    "\\\\textwon": "₩", // 韩元符号
    "\\\\textdong": "₫", // 越南盾符号
    "\\\\textcolonmonetary": "₡", // 科隆货币符号
    "\\\\textbaht": "฿", // 泰铢符号
    "\\\\textnumero": "№", // 编号符号
    "\\\\textdiscount": "⁒", // 折扣符号
    "\\\\textrecipe": "℞", // 处方符号
    "\\\\textreferencemark": "※", // 参考标记
    "\\\\textinterrobang": "‽", // 感叹问号
    "\\\\textinterrobangdown": "⸘", // 倒感叹问号
    "\\\\textbardbl": "‖", // 双竖线
    "\\\\textbrokenbar": "¦", // 断竖线
    "\\\\textasteriskcentered": "⁎", // 居中星号
    "\\\\textbigcircle": "◯", // 大圆圈
    "\\\\textblank": "␣", // 空白符号
    "\\\\textcircled": "⃝", // 圆圈包围
    "\\\\textmusicalnote": "♪", // 音符
    "\\\\textmusicalnotedbl": "♫", // 双音符
    "\\\\textopenbullet": "◦", // 空心项目符号
    "\\\\textperiodcentered": "·", // 居中点
    "\\\\textphi": "ɸ", // 希腊phi
    "\\\\textquestiondown": "¿", // 倒问号
    "\\\\textquotedbl": '"', // 双引号
    "\\\\textquotesingle": "'", // 单引号
    "\\\\textthreequarters": "¾", // 四分之三
    "\\\\texttwelveudash": "〰", // 波浪线
    "\\\\textvartriangle": "∆", // 变体三角形
    "\\\\textasciiacute": "´", // 尖音符
    "\\\\textacutedbl": "˝", // 双尖音符
    "\\\\textasciibreve": "˘", // 短音符
    "\\\\textasciicaron": "ˇ", // 反折音符
    "\\\\textasciidieresis": "¨", // 分音符
    "\\\\textasciimacron": "¯", // 长音符
    "\\\\textgravedbl": "̏", // 双重音符
    "\\\\textoverline": "̅", // 上划线
    "\\\\textunderbar": "̲", // 下划线
    "\\\\textunderwavy": "̰", // 波浪下划线
    "\\\\textsubdot": "̣", // 下点
    "\\\\textsubcomma": "̦", // 下逗号
    "\\\\textsubring": "̊", // 下圆圈
    "\\\\textsubtilde": "̰", // 下波浪线
    "\\\\textsubumlaut": "̈", // 下分音符
    "\\\\textsubcirc": "̆", // 下短音符
    "\\\\textsubbreve": "̆", // 下短音符
    "\\\\textsubacute": "́", // 下尖音符
    "\\\\textsubgrave": "̀", // 下重音符
    "\\\\textsubcedilla": "̧", // 下加符
    "\\\\textsubcaron": "̌", // 下反折音符
    "\\\\textsubmacron": "̄", // 下长音符
    "\\\\textsubdotbelow": "̣", // 下点
    "\\\\textsubbridge": "̪", // 下桥
    "\\\\textsubarch": "̺", // 下弓
    "\\\\textsubsquare": "̻", // 下方块
    "\\\\textsubseagull": "̼", // 下海鸥
    "\\\\textsubinvbreve": "̑", // 下反短音符
    "\\\\textsubtildebelow": "̰", // 下波浪线
    "\\\\textsubumlautbelow": "̈", // 下分音符
    "\\\\textsubcircbelow": "̆", // 下短音符
    "\\\\textsubbrevebelow": "̆", // 下短音符
    "\\\\textsubacutebelow": "́", // 下尖音符
    "\\\\textsubgravebelow": "̀", // 下重音符
    "\\\\textsubcedillabelow": "̧", // 下加符
    "\\\\textsubcaronbelow": "̌", // 下反折音符
    "\\\\textsubmacronbelow": "̄", // 下长音符
    "\\\\textsubdotbelowbelow": "̣", // 下点
    "\\\\textsubbridgebelow": "̪", // 下桥
    "\\\\textsubarchbelow": "̺", // 下弓
    "\\\\textsubsquarebelow": "̻", // 下方块
    "\\\\textsubseagullbelow": "̼", // 下海鸥
    "\\\\textsubinvbrevebelow": "̑", // 下反短音符
    "\\\\textsubtildebelowbelow": "̰", // 下波浪线
    "\\\\textsubumlautbelowbelow": "̈", // 下分音符
    "\\\\textsubcircbelowbelow": "̆", // 下短音符
    "\\\\textsubbrevebelowbelow": "̆", // 下短音符
    "\\\\textsubacutebelowbelow": "́", // 下尖音符
    "\\\\textsubgravebelowbelow": "̀", // 下重音符
    "\\\\textsubcedillabelowbelow": "̧", // 下加符
    "\\\\textsubcaronbelowbelow": "̌", // 下反折音符
    "\\\\textsubmacronbelowbelow": "̄", // 下长音符
    "\\\\textsubdotbelowbelowbelow": "̣", // 下点
    "\\\\textsubbridgebelowbelow": "̪", // 下桥
    "\\\\textsubarchbelowbelow": "̺", // 下弓
    "\\\\textsubsquarebelowbelow": "̻", // 下方块
    "\\\\textsubseagullbelowbelow": "̼", // 下海鸥
    "\\\\textsubinvbrevebelowbelow": "̑", // 下反短音符
    "\\\\textsubtildebelowbelowbelow": "̰", // 下波浪线
    "\\\\textsubumlautbelowbelowbelow": "̈", // 下分音符
    "\\\\textsubcircbelowbelowbelow": "̆", // 下短音符
    "\\\\textsubbrevebelowbelowbelow": "̆", // 下短音符
    "\\\\textsubacutebelowbelowbelow": "́", // 下尖音符
    "\\\\textsubgravebelowbelowbelow": "̀", // 下重音符
    "\\\\textsubcedillabelowbelowbelow": "̧", // 下加符
    "\\\\textsubcaronbelowbelowbelow": "̌", // 下反折音符
    "\\\\textsubmacronbelowbelowbelow": "̄", // 下长音符
    "\\\\textsubdotbelowbelowbelowbelow": "̣", // 下点
    "\\\\textsubbridgebelowbelowbelow": "̪", // 下桥
    "\\\\textsubarchbelowbelowbelow": "̺", // 下弓
    "\\\\textsubsquarebelowbelowbelow": "̻", // 下方块
    "\\\\textsubseagullbelowbelowbelow": "̼", // 下海鸥
    "\\\\textsubinvbrevebelowbelowbelow": "̑", // 下反短音符
    "\\\\textsubtildebelowbelowbelowbelow": "̰", // 下波浪线
    "\\\\textsubumlautbelowbelowbelowbelow": "̈", // 下分音符
    "\\\\textsubcircbelowbelowbelowbelow": "̆", // 下短音符
    "\\\\textsubbrevebelowbelowbelowbelow": "̆", // 下短音符
    "\\\\textsubacutebelowbelowbelowbelow": "́", // 下尖音符
    "\\\\textsubgravebelowbelowbelowbelow": "̀", // 下重音符
    "\\\\textsubcedillabelowbelowbelowbelow": "̧", // 下加符
    "\\\\textsubcaronbelowbelowbelowbelow": "̌", // 下反折音符
    "\\\\textsubmacronbelowbelowbelowbelow": "̄", // 下长音符
    "\\\\textsubdotbelowbelowbelowbelowbelow": "̣", // 下点
    "\\\\textsubbridgebelowbelowbelowbelow": "̪", // 下桥
    "\\\\textsubarchbelowbelowbelowbelow": "̺", // 下弓
    "\\\\textsubsquarebelowbelowbelowbelow": "̻", // 下方块
    "\\\\textsubseagullbelowbelowbelowbelow": "̼", // 下海鸥
    "\\\\textsubinvbrevebelowbelowbelowbelow": "̑", // 下反短音符
    "\\\\textsubtildebelowbelowbelowbelowbelow": "̰", // 下波浪线
    "\\\\textsubumlautbelowbelowbelowbelowbelow": "̈", // 下分音符
    "\\\\textsubcircbelowbelowbelowbelowbelow": "̆", // 下短音符
    "\\\\textsubbrevebelowbelowbelowbelowbelow": "̆", // 下短音符
    "\\\\textsubacutebelowbelowbelowbelowbelow": "́", // 下尖音符
    "\\\\textsubgravebelowbelowbelowbelowbelow": "̀", // 下重音符
    "\\\\textsubcedillabelowbelowbelowbelowbelow": "̧", // 下加符
    "\\\\textsubcaronbelowbelowbelowbelowbelow": "̌", // 下反折音符
    "\\\\textsubmacronbelowbelowbelowbelowbelow": "̄", // 下长音符
    "\\\\textsubdotbelowbelowbelowbelowbelowbelow": "̣", // 下点
    "\\\\textsubbridgebelowbelowbelowbelowbelow": "̪", // 下桥
    "\\\\textsubarchbelowbelowbelowbelowbelow": "̺", // 下弓
    "\\\\textsubsquarebelowbelowbelowbelowbelow": "̻", // 下方块
    "\\\\textsubseagullbelowbelowbelowbelowbelow": "̼", // 下海鸥
    "\\\\textsubinvbrevebelowbelowbelowbelowbelow": "̑", // 下反短音符
    "\\\\textsubtildebelowbelowbelowbelowbelowbelow": "̰", // 下波浪线
    "\\\\textsubumlautbelowbelowbelowbelowbelowbelow": "̈", // 下分音符
    "\\\\textsubcircbelowbelowbelowbelowbelowbelow": "̆", // 下短音符
    "\\\\textsubbrevebelowbelowbelowbelowbelowbelow": "̆", // 下短音符
    "\\\\textsubacutebelowbelowbelowbelowbelowbelow": "́", // 下尖音符
    "\\\\textsubgravebelowbelowbelowbelowbelowbelow": "̀", // 下重音符
    "\\\\textsubcedillabelowbelowbelowbelowbelowbelow": "̧", // 下加符
    "\\\\textsubcaronbelowbelowbelowbelowbelowbelow": "̌", // 下反折音符
    "\\\\textsubmacronbelowbelowbelowbelowbelowbelow": "̄", // 下长音符
    "\\\\textsubdotbelowbelowbelowbelowbelowbelowbelow": "̣", // 下点
    "\\\\textsubbridgebelowbelowbelowbelowbelowbelow": "̪", // 下桥
    "\\\\textsubarchbelowbelowbelowbelowbelowbelow": "̺", // 下弓
    "\\\\textsubsquarebelowbelowbelowbelowbelowbelow": "̻", // 下方块
    "\\\\textsubseagullbelowbelowbelowbelowbelowbelow": "̼", // 下海鸥
    "\\\\textsubinvbrevebelowbelowbelowbelowbelowbelow": "̑", // 下反短音符
    "\\\\textsubtildebelowbelowbelowbelowbelowbelowbelow": "̰", // 下波浪线
    "\\\\textsubumlautbelowbelowbelowbelowbelowbelowbelow": "̈", // 下分音符
    "\\\\textsubcircbelowbelowbelowbelowbelowbelowbelow": "̆", // 下短音符
    "\\\\textsubbrevebelowbelowbelowbelowbelowbelowbelow": "̆", // 下短音符
    "\\\\textsubacutebelowbelowbelowbelowbelowbelowbelow": "́", // 下尖音符
    "\\\\textsubgravebelowbelowbelowbelowbelowbelowbelow": "̀", // 下重音符
    "\\\\textsubcedillabelowbelowbelowbelowbelowbelowbelow": "̧", // 下加符
    "\\\\textsubcaronbelowbelowbelowbelowbelowbelowbelow": "̌", // 下反折音符
    "\\\\textsubmacronbelowbelowbelowbelowbelowbelowbelow": "̄", // 下长音符
    "\\\\textsubdotbelowbelowbelowbelowbelowbelowbelowbelow": "̣", // 下点
    "\\\\textsubbridgebelowbelowbelowbelowbelowbelowbelow": "̪", // 下桥
    "\\\\textsubarchbelowbelowbelowbelowbelowbelowbelow": "̺", // 下弓
    "\\\\textsubsquarebelowbelowbelowbelowbelowbelowbelow": "̻", // 下方块
    "\\\\textsubseagullbelowbelowbelowbelowbelowbelowbelow": "̼", // 下海鸥
    "\\\\textsubinvbrevebelowbelowbelowbelowbelowbelowbelow": "̑", // 下反短音符
    "\\\\textsubtildebelowbelowbelowbelowbelowbelowbelowbelow": "̰", // 下波浪线
    "\\\\textsubumlautbelowbelowbelowbelowbelowbelowbelowbelow": "̈", // 下分音符
    "\\\\textsubcircbelowbelowbelowbelowbelowbelowbelowbelow": "̆", // 下短音符
    "\\\\textsubbrevebelowbelowbelowbelowbelowbelowbelowbelow": "̆", // 下短音符
    "\\\\textsubacutebelowbelowbelowbelowbelowbelowbelowbelow": "́", // 下尖音符
    "\\\\textsubgravebelowbelowbelowbelowbelowbelowbelowbelow": "̀", // 下重音符
    "\\\\textsubcedillabelowbelowbelowbelowbelowbelowbelowbelow": "̧", // 下加符
    "\\\\textsubcaronbelowbelowbelowbelowbelowbelowbelowbelow": "̌", // 下反折音符
    "\\\\textsubmacronbelowbelowbelowbelowbelowbelowbelowbelow": "̄", // 下长音符
    "\\\\textsubdotbelowbelowbelowbelowbelowbelowbelowbelowbelow": "̣", // 下点
    "\\\\textsubbridgebelowbelowbelowbelowbelowbelowbelowbelow": "̪", // 下桥
    "\\\\textsubarchbelowbelowbelowbelowbelowbelowbelowbelow": "̺", // 下弓
    "\\\\textsubsquarebelowbelowbelowbelowbelowbelowbelowbelow": "̻", // 下方块
    "\\\\textsubseagullbelowbelowbelowbelowbelowbelowbelowbelow": "̼", // 下海鸥
    "\\\\textsubinvbrevebelowbelowbelowbelowbelowbelowbelowbelow": "̑", // 下反短音符
    "\\\\textsubtildebelowbelowbelowbelowbelowbelowbelowbelowbelow": "̰", // 下波浪线
    "\\\\textsubumlautbelowbelowbelowbelowbelowbelowbelowbelowbelow": "̈", // 下分音符
    "\\\\textsubcircbelowbelowbelowbelowbelowbelowbelowbelowbelow": "̆", // 下短音符
    "\\\\textsubbrevebelowbelowbelowbelowbelowbelowbelowbelowbelow": "̆", // 下短音符
    "\\\\textsubacutebelowbelowbelowbelowbelowbelowbelowbelowbelow": "́", // 下尖音符
    "\\\\textsubgravebelowbelowbelowbelowbelowbelowbelowbelowbelow": "̀", // 下重音符
    "\\\\textsubcedillabelowbelowbelowbelowbelowbelowbelowbelowbelow": "̧", // 下加符
    "\\\\textsubcaronbelowbelowbelowbelowbelowbelowbelowbelowbelow": "̌", // 下反折音符
    "\\\\textsubmacronbelowbelowbelowbelowbelowbelowbelowbelowbelow": "̄", // 下长音符
    "\\\\textsubdotbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow": "̣", // 下点
    "\\\\textsubbridgebelowbelowbelowbelowbelowbelowbelowbelowbelow": "̪", // 下桥
    "\\\\textsubarchbelowbelowbelowbelowbelowbelowbelowbelowbelow": "̺", // 下弓
    "\\\\textsubsquarebelowbelowbelowbelowbelowbelowbelowbelowbelow": "̻", // 下方块
    "\\\\textsubseagullbelowbelowbelowbelowbelowbelowbelowbelowbelow": "̼", // 下海鸥
    "\\\\textsubinvbrevebelowbelowbelowbelowbelowbelowbelowbelowbelow": "̑", // 下反短音符
    "\\\\textsubtildebelowbelowbelowbelowbelowbelowbelowbelowbelowbelow": "̰", // 下波浪线
    "\\\\textsubumlautbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow": "̈", // 下分音符
    "\\\\textsubcircbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow": "̆", // 下短音符
    "\\\\textsubbrevebelowbelowbelowbelowbelowbelowbelowbelowbelowbelow": "̆", // 下短音符
    "\\\\textsubacutebelowbelowbelowbelowbelowbelowbelowbelowbelowbelow": "́", // 下尖音符
    "\\\\textsubgravebelowbelowbelowbelowbelowbelowbelowbelowbelowbelow": "̀", // 下重音符
    "\\\\textsubcedillabelowbelowbelowbelowbelowbelowbelowbelowbelowbelow": "̧", // 下加符
    "\\\\textsubcaronbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow": "̌", // 下反折音符
    "\\\\textsubmacronbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow": "̄", // 下长音符
    "\\\\textsubdotbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow": "̣", // 下点
    "\\\\textsubbridgebelowbelowbelowbelowbelowbelowbelowbelowbelowbelow": "̪", // 下桥
    "\\\\textsubarchbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow": "̺", // 下弓
    "\\\\textsubsquarebelowbelowbelowbelowbelowbelowbelowbelowbelowbelow": "̻", // 下方块
    "\\\\textsubseagullbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow": "̼", // 下海鸥
    "\\\\textsubinvbrevebelowbelowbelowbelowbelowbelowbelowbelowbelowbelow": "̑", // 下反短音符
    "\\\\textsubtildebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̰", // 下波浪线
    "\\\\textsubumlautbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̈", // 下分音符
    "\\\\textsubcircbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̆", // 下短音符
    "\\\\textsubbrevebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̆", // 下短音符
    "\\\\textsubacutebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "́", // 下尖音符
    "\\\\textsubgravebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̀", // 下重音符
    "\\\\textsubcedillabelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̧", // 下加符
    "\\\\textsubcaronbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̌", // 下反折音符
    "\\\\textsubmacronbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̄", // 下长音符
    "\\\\textsubdotbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̣", // 下点
    "\\\\textsubbridgebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̪", // 下桥
    "\\\\textsubarchbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̺", // 下弓
    "\\\\textsubsquarebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̻", // 下方块
    "\\\\textsubseagullbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̼", // 下海鸥
    "\\\\textsubinvbrevebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̑", // 下反短音符
    "\\\\textsubtildebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̰", // 下波浪线
    "\\\\textsubumlautbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̈", // 下分音符
    "\\\\textsubcircbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̆", // 下短音符
    "\\\\textsubbrevebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̆", // 下短音符
    "\\\\textsubacutebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "́", // 下尖音符
    "\\\\textsubgravebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̀", // 下重音符
    "\\\\textsubcedillabelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̧", // 下加符
    "\\\\textsubcaronbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̌", // 下反折音符
    "\\\\textsubmacronbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̄", // 下长音符
    "\\\\textsubdotbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̣", // 下点
    "\\\\textsubbridgebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̪", // 下桥
    "\\\\textsubarchbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̺", // 下弓
    "\\\\textsubsquarebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̻", // 下方块
    "\\\\textsubseagullbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̼", // 下海鸥
    "\\\\textsubinvbrevebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̑", // 下反短音符
    "\\\\textsubtildebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̰", // 下波浪线
    "\\\\textsubumlautbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̈", // 下分音符
    "\\\\textsubcircbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̆", // 下短音符
    "\\\\textsubbrevebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̆", // 下短音符
    "\\\\textsubacutebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "́", // 下尖音符
    "\\\\textsubgravebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̀", // 下重音符
    "\\\\textsubcedillabelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̧", // 下加符
    "\\\\textsubcaronbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̌", // 下反折音符
    "\\\\textsubmacronbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̄", // 下长音符
    "\\\\textsubdotbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̣", // 下点
    "\\\\textsubbridgebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̪", // 下桥
    "\\\\textsubarchbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̺", // 下弓
    "\\\\textsubsquarebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̻", // 下方块
    "\\\\textsubseagullbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̼", // 下海鸥
    "\\\\textsubinvbrevebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̑", // 下反短音符
    "\\\\textsubtildebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̰", // 下波浪线
    "\\\\textsubumlautbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̈", // 下分音符
    "\\\\textsubcircbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̆", // 下短音符
    "\\\\textsubbrevebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̆", // 下短音符
    "\\\\textsubacutebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "́", // 下尖音符
    "\\\\textsubgravebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̀", // 下重音符
    "\\\\textsubcedillabelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̧", // 下加符
    "\\\\textsubcaronbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̌", // 下反折音符
    "\\\\textsubmacronbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̄", // 下长音符
    "\\\\textsubdotbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̣", // 下点
    "\\\\textsubbridgebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̪", // 下桥
    "\\\\textsubarchbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̺", // 下弓
    "\\\\textsubsquarebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̻", // 下方块
    "\\\\textsubseagullbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̼", // 下海鸥
    "\\\\textsubinvbrevebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̑", // 下反短音符
    "\\\\textsubtildebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̰", // 下波浪线
    "\\\\textsubumlautbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̈", // 下分音符
    "\\\\textsubcircbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̆", // 下短音符
    "\\\\textsubbrevebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̆", // 下短音符
    "\\\\textsubacutebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "́", // 下尖音符
    "\\\\textsubgravebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̀", // 下重音符
    "\\\\textsubcedillabelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̧", // 下加符
    "\\\\textsubcaronbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̌", // 下反折音符
    "\\\\textsubmacronbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̄", // 下长音符
    "\\\\textsubdotbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̣", // 下点
    "\\\\textsubbridgebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̪", // 下桥
    "\\\\textsubarchbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̺", // 下弓
    "\\\\textsubsquarebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̻", // 下方块
    "\\\\textsubseagullbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̼", // 下海鸥
    "\\\\textsubinvbrevebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̑", // 下反短音符
    "\\\\textsubtildebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̰", // 下波浪线
    "\\\\textsubumlautbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̈", // 下分音符
    "\\\\textsubcircbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̆", // 下短音符
    "\\\\textsubbrevebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̆", // 下短音符
    "\\\\textsubacutebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "́", // 下尖音符
    "\\\\textsubgravebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̀", // 下重音符
    "\\\\textsubcedillabelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̧", // 下加符
    "\\\\textsubcaronbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̌", // 下反折音符
    "\\\\textsubmacronbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̄", // 下长音符
    "\\\\textsubdotbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̣", // 下点
    "\\\\textsubbridgebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̪", // 下桥
    "\\\\textsubarchbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̺", // 下弓
    "\\\\textsubsquarebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̻", // 下方块
    "\\\\textsubseagullbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̼", // 下海鸥
    "\\\\textsubinvbrevebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̑", // 下反短音符
    "\\\\textsubtildebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̰", // 下波浪线
    "\\\\textsubumlautbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̈", // 下分音符
    "\\\\textsubcircbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̆", // 下短音符
    "\\\\textsubbrevebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̆", // 下短音符
    "\\\\textsubacutebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "́", // 下尖音符
    "\\\\textsubgravebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̀", // 下重音符
    "\\\\textsubcedillabelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̧", // 下加符
    "\\\\textsubcaronbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̌", // 下反折音符
    "\\\\textsubmacronbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̄", // 下长音符
    "\\\\textsubdotbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̣", // 下点
    "\\\\textsubbridgebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̪", // 下桥
    "\\\\textsubarchbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̺", // 下弓
    "\\\\textsubsquarebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̻", // 下方块
    "\\\\textsubseagullbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̼", // 下海鸥
    "\\\\textsubinvbrevebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̑", // 下反短音符
    "\\\\textsubtildebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̰", // 下波浪线
    "\\\\textsubumlautbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̈", // 下分音符
    "\\\\textsubcircbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̆", // 下短音符
    "\\\\textsubbrevebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̆", // 下短音符
    "\\\\textsubacutebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "́", // 下尖音符
    "\\\\textsubgravebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̀", // 下重音符
    "\\\\textsubcedillabelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̧", // 下加符
    "\\\\textsubcaronbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̌", // 下反折音符
    "\\\\textsubmacronbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̄", // 下长音符
    "\\\\textsubdotbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̣", // 下点
    "\\\\textsubbridgebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̪", // 下桥
    "\\\\textsubarchbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̺", // 下弓
    "\\\\textsubsquarebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̻", // 下方块
    "\\\\textsubseagullbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̼", // 下海鸥
    "\\\\textsubinvbrevebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̑", // 下反短音符
    "\\\\textsubtildebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̰", // 下波浪线
    "\\\\textsubumlautbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̈", // 下分音符
    "\\\\textsubcircbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̆", // 下短音符
    "\\\\textsubbrevebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̆", // 下短音符
    "\\\\textsubacutebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "́", // 下尖音符
    "\\\\textsubgravebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̀", // 下重音符
    "\\\\textsubcedillabelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̧", // 下加符
    "\\\\textsubcaronbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̌", // 下反折音符
    "\\\\textsubmacronbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̄", // 下长音符
    "\\\\textsubdotbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̣", // 下点
    "\\\\textsubbridgebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̪", // 下桥
    "\\\\textsubarchbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̺", // 下弓
    "\\\\textsubsquarebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̻", // 下方块
    "\\\\textsubseagullbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̼", // 下海鸥
    "\\\\textsubinvbrevebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̑", // 下反短音符
    "\\\\textsubtildebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̰", // 下波浪线
    "\\\\textsubumlautbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̈", // 下分音符
    "\\\\textsubcircbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̆", // 下短音符
    "\\\\textsubbrevebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̆", // 下短音符
    "\\\\textsubacutebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "́", // 下尖音符
    "\\\\textsubgravebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̀", // 下重音符
    "\\\\textsubcedillabelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̧", // 下加符
    "\\\\textsubcaronbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̌", // 下反折音符
    "\\\\textsubmacronbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̄", // 下长音符
    "\\\\textsubdotbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̣", // 下点
    "\\\\textsubbridgebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̪", // 下桥
    "\\\\textsubarchbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̺", // 下弓
    "\\\\textsubsquarebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̻", // 下方块
    "\\\\textsubseagullbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̼", // 下海鸥
    "\\\\textsubinvbrevebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̑", // 下反短音符
    "\\\\textsubtildebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̰", // 下波浪线
    "\\\\textsubumlautbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̈", // 下分音符
    "\\\\textsubcircbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̆", // 下短音符
    "\\\\textsubbrevebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̆", // 下短音符
    "\\\\textsubacutebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "́", // 下尖音符
    "\\\\textsubgravebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̀", // 下重音符
    "\\\\textsubcedillabelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̧", // 下加符
    "\\\\textsubcaronbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̌", // 下反折音符
    "\\\\textsubmacronbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̄", // 下长音符
    "\\\\textsubdotbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̣", // 下点
    "\\\\textsubbridgebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̪", // 下桥
    "\\\\textsubarchbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̺", // 下弓
    "\\\\textsubsquarebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̻", // 下方块
    "\\\\textsubseagullbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̼", // 下海鸥
    "\\\\textsubinvbrevebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̑", // 下反短音符
    "\\\\textsubtildebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̰", // 下波浪线
    "\\\\textsubumlautbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̈", // 下分音符
    "\\\\textsubcircbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̆", // 下短音符
    "\\\\textsubbrevebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̆", // 下短音符
    "\\\\textsubacutebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "́", // 下尖音符
    "\\\\textsubgravebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̀", // 下重音符
    "\\\\textsubcedillabelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̧", // 下加符
    "\\\\textsubcaronbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̌", // 下反折音符
    "\\\\textsubmacronbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̄", // 下长音符
    "\\\\textsubdotbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̣", // 下点
    "\\\\textsubbridgebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̪", // 下桥
    "\\\\textsubarchbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̺", // 下弓
    "\\\\textsubsquarebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̻", // 下方块
    "\\\\textsubseagullbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̼", // 下海鸥
    "\\\\textsubinvbrevebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̑", // 下反短音符
    "\\\\textsubtildebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̰", // 下波浪线
    "\\\\textsubumlautbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̈", // 下分音符
    "\\\\textsubcircbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̆", // 下短音符
    "\\\\textsubbrevebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̆", // 下短音符
    "\\\\textsubacutebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "́", // 下尖音符
    "\\\\textsubgravebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̀", // 下重音符
    "\\\\textsubcedillabelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̧", // 下加符
    "\\\\textsubcaronbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̌", // 下反折音符
    "\\\\textsubmacronbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̄", // 下长音符
    "\\\\textsubdotbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̣", // 下点
    "\\\\textsubbridgebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̪", // 下桥
    "\\\\textsubarchbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̺", // 下弓
    "\\\\textsubsquarebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̻", // 下方块
    "\\\\textsubseagullbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̼", // 下海鸥
    "\\\\textsubinvbrevebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̑", // 下反短音符
    "\\\\textsubtildebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̰", // 下波浪线
    "\\\\textsubumlautbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̈", // 下分音符
    "\\\\textsubcircbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̆", // 下短音符
    "\\\\textsubbrevebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̆", // 下短音符
    "\\\\textsubacutebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "́", // 下尖音符
    "\\\\textsubgravebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̀", // 下重音符
    "\\\\textsubcedillabelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̧", // 下加符
    "\\\\textsubcaronbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̌", // 下反折音符
    "\\\\textsubmacronbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̄", // 下长音符
    "\\\\textsubdotbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̣", // 下点
    "\\\\textsubbridgebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̪", // 下桥
    "\\\\textsubarchbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̺", // 下弓
    "\\\\textsubsquarebelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̻", // 下方块
    "\\\\textsubseagullbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelowbelow":
      "̼", // 下海鸥
  }
  // 先处理长的命令，再处理短的命令
  const sortedKeys = Object.keys(latexMap).sort((a, b) => b.length - a.length)

  let processedText = text
  for (const key of sortedKeys) {
    const regex = new RegExp(key, "g")
    processedText = processedText.replace(regex, latexMap[key])
  }
  return processedText
}
