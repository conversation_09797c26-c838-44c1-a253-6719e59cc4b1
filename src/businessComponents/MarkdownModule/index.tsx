// markdown 与编辑
import React, { useRef } from "react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import rehypeRaw from "rehype-raw";
import RemarkBreaks from "remark-breaks";
import RemarkMath from "remark-math";
import ThinkBlock from "@/component/think";
import ReactECharts from "echarts-for-react";
import CodeMirror from "@uiw/react-codemirror";
import { markdown } from "@codemirror/lang-markdown";
import { Card } from "antd";
// 只读模式（默认）  父组件用法
{
  /* <MarkdownRenderer content={tab.content} tabKey={tab.key} />
// 编辑模式  父组件用法
<MarkdownRenderer
  content={markdownText}
  tabKey="edit-tab"
  editable
  onChange={(val) => setMarkdownText(val)}
/> */
}
interface MarkdownRendererProps {
  content: string;
  tabKey: string;
  editable?: boolean; // 是否可编辑
  onChange?: (val: string) => void;
}

const MarkdownModule: React.FC<MarkdownRendererProps> = ({
  content,
  tabKey,
  editable = false,
  onChange,
}) => {
  const echartsRef = useRef<Record<string, any>>({});

  const markdownComponents = {
    think: (props: any) => (
      <ThinkBlock finished={false}>{props.children}</ThinkBlock>
    ),

    code({ className, children, ...props }: any) {
      const match = /language-(\w+)/.exec(className || "");
      if (match && match[1]) {
        let cleaned = `${children}`.trim();
        if (/^option\s*=/.test(cleaned)) {
          cleaned = cleaned.replace(/^option\s*=\s*/, "");
        }

        try {
          const optionObj = new Function("return " + cleaned)();
          if (optionObj && typeof optionObj === "object" && optionObj.series) {
            optionObj.animation = false;
            return (
              <ReactECharts
                ref={(el) => (echartsRef.current[tabKey] = el)}
                key={tabKey}
                option={optionObj}
                style={{ height: 400, width: "100%", margin: "24px 0" }}
              />
            );
          } else {
            return <pre style={{ color: "red" }}>ECharts 配置无效</pre>;
          }
        } catch (e) {
          return <pre style={{ color: "red" }}>图表生成中...</pre>;
        }
      }

      return (
        <pre>
          <Card style={{ width: "100%", overflow: "auto" }}>
            <code className={className} {...props}>
              {children}
            </code>
          </Card>
        </pre>
      );
    },
  };

  if (editable) {
    return (
      <CodeMirror
        value={content}
        height="300px"
        extensions={[markdown()]}
        onChange={(val) => {
          onChange?.(val);
        }}
      />
    );
  }

  return (
    <ReactMarkdown
      rehypePlugins={[rehypeRaw]}
      remarkPlugins={[remarkGfm, RemarkBreaks, RemarkMath]}
      components={markdownComponents}
    >
      {content}
    </ReactMarkdown>
  );
};

export default MarkdownModule;
