.law-review{
  .law-file-list{
    background: var(--ant-color-bg-container);
    padding: 0px 40px 0px 50px;
    height: 65px;
    .ant-tabs-nav{
      height: 55px;
      margin-bottom: 18px;
      &::before{
        border-bottom:0px;
      }
    }
    .ant-tabs-tab-btn{
      font-size: var(--ant-font-size-lg);
      font-weight: bold;
      color:#333;
    }
  }
  .law-file-view{
    background: var(--ant-color-bg-container);
    .ant-tabs-nav-list{
      .ant-tabs-tab{
        margin-top: var(--ant-margin);
        margin-left: var(--ant-margin-md);
      }
      >.ant-tabs-tab:nth-child(1){
        margin-left: 0px;
      }
      .tabs-btn{
        color: #333333;
        background: #F8FAFC;
        min-width: 125px;
        border-radius: 6px;
        padding: 11px 24px;
        font-weight:normal;
        border: 1px solid #F8FAFC;
      }
      >div:nth-child(1).ant-tabs-tab-active{
        .tabs-btn{
          background:var(--ant-color-primary);
          border: 1px solid var(--ant-color-primary);;  
          color: #fff;
        }
      }
      >div:nth-child(2).ant-tabs-tab-active{
        .tabs-btn{
          background:#FDE8E9;
          border: 1px solid #FFCCC7;
          color: var(--ant-color-error);
        }
      }
      >div:nth-child(3).ant-tabs-tab-active{
       .tabs-btn{
          background:var(--ant-color-warning-bg);
          border: 1px solid var(--ant-color-warning-bg-hover);
          color: var(--ant-color-warning);
        }
      }
      >div:nth-child(4).ant-tabs-tab-active{
       .tabs-btn{
          background:var(--ant-color-success-bg);
          border: 1px solid var(--ant-color-success-bg-hover); 
          color: var(--ant-color-success);
        }
      }
      >div:nth-child(5).ant-tabs-tab-active{
        .tabs-btn{
          background:var(--ant-color-primary-bg);
          border: 1px solid var(--ant-color-primary-bg-hover);  
          color: var(--ant-color-primary);
        }
      }
    }
    .ant-tabs-ink-bar{
      width:0px !important;
    }
    .ant-tabs-nav{
      &::before{
        border-bottom:0px;
      }
    }
    .ant-tabs-tab-btn{
      font-size: var(--ant-font-size);
      font-weight: bold;
      color:#333;
    }
  }
  .law-right{
    padding:0px 30px;
    background: var(--ant-color-bg-container);
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.02),0px 1px 6px -1px rgba(0, 0, 0, 0.02),0px 1px 2px 0px rgba(0, 0, 0, 0.03);
    .law-right-header{
      padding-top: var(--ant-margin-md);
      padding-bottom: var(--ant-margin-sm);
      border-bottom: 1px solid rgba(0,0,0,0.1);
      .ant-result{
        padding: 0px !important;
      }
      .ant-result-icon{
        margin-bottom: 0px;
        margin-right: 4px;
        align-items: center;
        margin-top:-3px;
        svg{
          width:22px;
          height:22px;
        }
      }
      .ant-result-title{
        display:none;
      }
    }
    .law-contract-card{
      .ant-card-head{
        border-bottom:0px;
      }
      .ant-card-body{
        padding: var(--ant-padding-md);
        padding-top: 5px;
        .tips-card{
          .ant-card-body{
            padding: var(--ant-padding)
          }
        }
      }
    }
  }
}

.my-split-docx-preview-wrapper{
  background: var(--ant-color-bg-container) !important;
  height: calc(100vh - 215px);
  padding: 20px !important;
  box-shadow: 0 0 5px 2px rgba(0, 0, 0, 0.06);
  border-radius: 6px;
  margin: 5px;
  .docx-wrapper>section.docx{
    padding: 10px 20px;
  }
  .my-split-docx-preview{
    width: 100% !important;
    margin-bottom:0px !important;
    padding: 0px !important;
    height: calc(100vh - 215px);
    overflow: auto;
    box-shadow: none !important;
    min-height:0px !important;
  }
}