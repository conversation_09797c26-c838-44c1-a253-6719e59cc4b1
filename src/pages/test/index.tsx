// DraftPage.tsx
import React, {
  useState,
  useMemo,
  Suspense,
  useRef,
  createRef,
  RefObject,
} from "react";
import { Button, Flex, Steps } from "antd";
import { getToken, getUserInfo } from "@/utils/auth";
import { cacheGet } from "@/utils/cacheUtil";
import useSSEChat from "@/hooks/useSSEChat";
import HeaderCom from "@/component/Header";
import "./index.less";

const pageInfo = {
  pageName: "合同起草",
  pageDesc: "AI根据模版及资料快速起草合同",
  steps: [
    {
      title: "信息填写",
      content: "<MentionsModule />",
      submitName: "提交信息",
      isExport: false,
      agentId: "7a6d9473-2ecc-451e-b463-0d68bafe70af",
    },
    {
      title: "选择模版",
      content: "<MallModule />",
      submitName: "选择模版1",
      isExport: false,
      agentId: "",
    },
    {
      title: "生成大纲",
      content: "<MarkdownModule />",
      submitName: "生成大纲1",
      isExport: false,
      agentId: "",
    },
    {
      title: "生成合同",
      content: "<MarkdownModule />",
      submitName: "生成合同",
      isExport: true,
      agentId: "",
    },
  ],
};

// 自动匹配业务组件
const modules = import.meta.glob("/src/businessComponents/**/index.tsx");

const DraftPage: React.FC = () => {
  const sseChat = useSSEChat();
  const [current, setCurrent] = useState(0);
  const currentStep = pageInfo.steps[current];

  // 所有组件的 ref 映射
  const refs = useRef<Record<number, RefObject<any>>>({});

  // 懒加载当前组件
  const DynamicComponent = useMemo(() => {
    const match = currentStep.content.match(/<(\w+)\s*\/>/);
    const componentName = match?.[1];

    if (!componentName) return null;

    const modulePath = `/src/businessComponents/${componentName}/index.tsx`;
    const loader = modules[modulePath];

    if (!loader) {
      console.warn("模块未找到:", modulePath);
      return null;
    }

    // 创建 ref（如果不存在）
    if (!refs.current[current]) {
      refs.current[current] = createRef();
    }

    const LazyComponent = React.lazy(loader as any);

    return () => <LazyComponent ref={refs.current[current]} />;
  }, [current]);

  // 提交操作
  const getSubmitInfo = async (item: any) => {
    const ref = refs.current[current];
    const childData = ref?.current?.getMentionsData?.(); // 获取子组件暴露方法

    if (item?.agentId) {
      const tokenInfo = await getToken();
      const userInfo = await getUserInfo();
      const tenantId = cacheGet("tenantId");

      const fileData: any[] = [];
      childData?.localFile?.forEach((item: any) => {
        fileData.push({
          type: item.fileType || "document",
          transfer_method: "local_file",
          upload_file_id: item.id,
        });
      });

      sseChat.start({
        url: "/dify/broker/agent/stream",
        headers: {
          "Content-Type": "application/json",
          Token: tokenInfo || "",
        },
        body: {
          insId: "1",
          bizType: "app:agent",
          bizId: item.agentId,
          agentId: item.agentId,
          path: "/chat-messages",
          query: childData?.query || "",
          difyJson: {
            inputs: {
              docFiles: fileData,
              Token: tokenInfo || "",
              tenantid: tenantId || "",
              outputTemplate: null, // templateFile 可根据实际情况补上
            },
            response_mode: "streaming",
            user: userInfo?.id || "anonymous",
            conversation_id: "",
            query: childData?.query || "",
          },
        },
        query: {},
        message: childData?.query || "",
        onFinished: () => {
          if (current < pageInfo.steps.length - 1) {
            setCurrent(current + 1);
          }
        },
      });
    } else {
      if (current < pageInfo.steps.length - 1) {
        setCurrent(current + 1);
      }
    }
  };

  return (
    <Flex vertical className="test-page" align="center">
      <HeaderCom
        mainTitle={pageInfo.pageName}
        subTitle={pageInfo.pageDesc}
      ></HeaderCom>
      <Flex vertical className="test-page-con" justify="center">
        <Steps current={current} style={{ marginBottom: 24 }}>
          {pageInfo.steps.map((item) => (
            <Steps.Step key={item.title} title={item.title} />
          ))}
        </Steps>

        <div
          style={{
            minHeight: 300,
            padding: 24,
            border: "1px solid #eee",
            marginBottom: 24,
          }}
        >
          <Suspense fallback={<div>加载中...</div>}>
            {DynamicComponent ? <DynamicComponent /> : <div>组件未找到</div>}
          </Suspense>
        </div>

        <div style={{ textAlign: "right" }}>
          {current > 0 && (
            <Button
              onClick={() => setCurrent(current - 1)}
              style={{ marginRight: 8 }}
            >
              上一步
            </Button>
          )}
          {current < pageInfo.steps.length - 1 && (
            <Button type="primary" onClick={() => getSubmitInfo(currentStep)}>
              {currentStep.submitName}
            </Button>
          )}
          {currentStep.isExport && (
            <Button type="primary" onClick={() => console.log("导出操作")}>
              导出
            </Button>
          )}
        </div>
      </Flex>
    </Flex>
  );
};

export default DraftPage;
