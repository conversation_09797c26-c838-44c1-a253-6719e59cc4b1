import axios from "axios";
import { cacheGet } from "./cacheUtil";
import { isNotNull, redirectToLogin } from "./common";

// 创建 axios
const service = axios.create({
  // 请求地址
  baseURL: "",
  // 请求超时时间(毫秒)
  timeout: 1200000,
});

/**
 * 请求拦截器
 * 发送请求前对请求体进行处理
 */
service.interceptors.request.use(
  (config) => {
    // 通过 import.meta.env 读取环境变量
    const BASE_API = import.meta.env.VITE_BASE_API;
    const API_HEADER_KEY = import.meta.env.VITE_API_HEADER_KEY;

    if (!config.baseURL && window.electronAPI) {
      config.baseURL = BASE_API;
    }

    config.headers.TenantId = cacheGet("tenantId");
    const token = cacheGet("token");
    if (isNotNull(token)) {
      config.headers.Token = token;
      if (API_HEADER_KEY) {
        config.headers[API_HEADER_KEY] = token;
      }
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

/**
 * 响应拦截器
 * 得到请求响应体后对其进行处理
 */
service.interceptors.response.use(
  (response) => {
    if (
      response.data.code === 11011 ||
      response.data.code === 11012 ||
      response.data.code === 11013 ||
      response.data.code === 11014 ||
      response.data.code === 11015 ||
      response.data.code === 11001
    ) {
      redirectToLogin();
      // 请求失败 抛出错误信息
      return Promise.reject(new Error(response.data.msg || "Error"));
    }
    return response.data;
  },
  (error) => {
    console.log(error);
    if (error.response.status === 401) {
      // 登录失效则触发退出登录
      redirectToLogin();
      // 请求失败 抛出错误信息
      return Promise.reject(new Error(error || "Error"));
    } else {
      // 请求失败 抛出错误信息
      return Promise.reject(
        new Error(error?.response?.data?.msg || error || "Error")
      );
    }
  }
);

export default service;
