import React from "react";
import {
  Modal,
  Timeline,
  Tag,
  Button,
  Space,
  Typography,
  Flex,
  theme,
} from "antd";
import { CheckCircleOutlined, CloseCircleOutlined } from "@ant-design/icons";

const { Text } = Typography;

interface ReviewResultModalProps {
  visible: boolean;
  title?: string;
  onCancel: () => void;
  onContinue?: () => void;
  onBackIndex?: () => void;
  reviewItems: []; // 评审的数据
}
const { useToken } = theme;
const ReviewResultModal: React.FC<ReviewResultModalProps> = ({
  visible,
  title,
  onCancel,
  onBackIndex,
  onContinue,
  reviewItems,
}) => {
  const { token } = useToken();
  return (
    <Modal
      title={title}
      open={visible}
      onCancel={onCancel}
      width={600}
      footer={
        <>
          {title == "输入信息评审" ? (
            <Flex justify="center" gap={token.margin}>
              <Button type="primary" onClick={onBackIndex}>
                返回首页
              </Button>
              <Button type="link" onClick={onContinue}>
                忽略错误，继续下一步
              </Button>
            </Flex>
          ) : (
            <Flex justify="center" gap={token.margin}>
              <Button type="primary" onClick={onContinue}>
                重新生成
              </Button>
              <Button onClick={onCancel}>返回</Button>
            </Flex>
          )}
        </>
      }
    >
      <Timeline
        mode="left"
        style={{ height: "500px", overflowY: "auto", padding: token.marginMD }}
      >
        {reviewItems.map((item: any, index) => (
          <Timeline.Item
            key={index}
            dot={
              item?.["评审结果"] === "通过" ? (
                <CheckCircleOutlined
                  style={{ color: "#52c41a", fontSize: "16px" }}
                />
              ) : (
                <CloseCircleOutlined
                  style={{ color: "#f5222d", fontSize: "16px" }}
                />
              )
            }
          >
            <div style={{ marginBottom: token.margin }}>
              {title == "输入信息评审" ? (
                <>
                  {item?.["评审细节"]?.map((item: any) => {
                    return (
                      <Space
                        direction="vertical"
                        size={4}
                        style={{ marginBottom: token.marginXS }}
                      >
                        <Flex gap={token.margin}>
                          <Text strong>{item?.["参数名"]}</Text>
                          <Tag
                            bordered={false}
                            color={
                              item?.["校验结果"] === "通过" ? "green" : "red"
                            }
                          >
                            {item?.["校验结果"]}
                          </Tag>
                        </Flex>
                        <Text style={{ color: token.colorTextDescription }}>
                          说明：{item?.["说明"]}
                        </Text>
                        {item?.["校验结果"] === "异常" && (
                          <Text style={{ color: token.colorTextDescription }}>
                            异常处理建议：{item?.["异常处理建议"]}
                          </Text>
                        )}
                      </Space>
                    );
                  })}
                </>
              ) : (
                <>
                  <Flex vertical>
                    <Flex gap={token.margin}>
                      <Tag
                        bordered={false}
                        color={item?.["评审结果"] === "通过" ? "green" : "red"}
                      >
                        {item?.["评审结果"]}
                      </Tag>
                    </Flex>
                    <Text
                      style={{
                        color: token.colorTextDescription,
                        marginTop: token.marginXS,
                      }}
                    >
                      {item?.["描述"]}
                    </Text>
                  </Flex>
                </>
              )}
            </div>
          </Timeline.Item>
        ))}
        <Timeline.Item
          dot={
            title == "输入信息评审" ||
            (title == "输出信息评审" &&
              reviewItems?.[0]?.["评审结果"] === "异常") ? (
              <CloseCircleOutlined
                style={{ color: "#f5222d", fontSize: token.fontSizeLG }}
              />
            ) : (
              <CheckCircleOutlined
                style={{ color: "#52c41a", fontSize: token.fontSizeLG }}
              />
            )
          }
        >
          <div style={{ marginTop: 16 }}>
            <Text strong>最终结论</Text>
            <Tag
              color={
                title == "输入信息评审" ||
                (title == "输出信息评审" &&
                  reviewItems?.[0]?.["评审结果"] === "异常")
                  ? "#f10"
                  : "#52c41a"
              }
              style={{ marginLeft: token.marginXS }}
            >
              {title == "输入信息评审" ||
              (title == "输出信息评审" &&
                reviewItems?.[0]?.["评审结果"] === "异常")
                ? "失败"
                : "通过"}
            </Tag>
          </div>
        </Timeline.Item>
      </Timeline>
    </Modal>
  );
};

export default ReviewResultModal;
