<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>拓扑图生成器</title>
    <script src="https://unpkg.com/vis-network/standalone/umd/vis-network.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.17.5/xlsx.full.min.js"></script>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            max-width: 1200px; 
            margin: 0 auto; 
            padding: 20px; 
        }
        #myNetwork { 
            width: 100%; 
            height: 600px; 
            border: 1px solid lightgray; 
        }
        #dataTable {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            table-layout: fixed; /* 确保表格宽度固定 */
        }
        #dataTable th, #dataTable td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
            word-wrap: break-word; /* 确保单元格内容换行 */
        }
        #nodeDetailModal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0,0,0,0.4);
        }
        .modal-content {
            background-color: #fefefe;
            margin: 15% auto;
            padding: 20px;
            border: 1px solid #888;
            width: 80%;
            max-width: 600px;
            z-index: 1001;
        }
        .modal-close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        .modal-details {
            margin-top: 20px;
        }
        .modal-details table {
            width: 100%;
            border-collapse: collapse;
        }
        .modal-details th, 
        .modal-details td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
      
        .search-container {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
}

#searchInput {
    flex: 1;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 16px;
}

button {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    background-color: #007bff;
    color: white;
    cursor: pointer;
    font-size: 16px;
}

button:hover {
    background-color: #0056b3;
}
#dataTable {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
    table-layout: fixed; /* 确保表格宽度固定 */
}

#dataTable th, #dataTable td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
    word-wrap: break-word; /* 确保单元格内容换行 */
}
.hidden-input {
    opacity: 0;
    width: 0;
    height: 0;
    overflow: hidden;
}
.file-name {
    margin-top: 10px;
    display: block;
}
.file-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    background-color: #007bff;
    color: white;
    cursor: pointer;
    font-size: 16px;
}
    </style>
</head>
<body>
    <div id="content2">
    <h2>拓扑图生成器</h2>
    <div>
        <label for="fileInput">
            <span class="file-btn" for="fileInput">选择文件</span> 
            <span class="file-name" id="fileName">未选择文件</span>
        </label>
        <input type="file" id="fileInput" accept=".xlsx, .xls, .csv" class="hidden-input">
    </div>

    <div class="search-container">
        <input type="text" id="searchInput" placeholder="按BUName_ZH查询">
        <button id="searchButton">查询</button>
        <button id="clearButton">清空查询</button>
        <button id="fullscreenButton">全屏显示</button>
    </div>
    <div id="myNetwork"></div>
</div>
    <table id="dataTable"></table>

    <!-- 节点详情模态框 -->
    <div id="nodeDetailModal">
        <div class="modal-content">
            <span class="modal-close">&times;</span>
            <h3 id="modalNodeLabel">节点详情</h3>
            <div class="modal-details" id="modalNodeDetails"></div>
        </div>
    </div>

    <script>
        let globalData = []; // 存储全局数据
        let nodeDetailsMap = new Map(); // 存储节点详情映射
        let allNodes = new vis.DataSet(); // 存储所有节点
        let allEdges = new vis.DataSet(); // 存储所有边
        document.getElementById('fileInput').addEventListener('change', function(e) {
            const fileName = e.target.files[0] ? e.target.files[0].name : '未选择文件';
            document.getElementById('fileName').textContent = fileName;
            
            const file = e.target.files[0];
            const reader = new FileReader();

            reader.onload = function(event) {
                const data = new Uint8Array(event.target.result);
                const workbook = XLSX.read(data, {type: 'array'});
                
                // 假设数据在第一个工作表
                const worksheet = workbook.Sheets[workbook.SheetNames[0]];
                const jsonData = XLSX.utils.sheet_to_json(worksheet);
                globalData = jsonData; // 保存全局数据

                // 处理数据并生成Vis-Network格式
                const { nodes, edges } = processExcelData(jsonData);

                // 保存所有节点和边
                allNodes = nodes;
                allEdges = edges;

                // 显示原始数据表格
                displayDataTable(jsonData);

                // 创建网络图
                createNetwork(nodes, edges);
            };

            reader.readAsArrayBuffer(file);
        });

          document.getElementById('searchButton').addEventListener('click', function() {
        const searchValue = document.getElementById('searchInput').value.trim();
        if (searchValue) {
            const filteredNodes = allNodes.get({
                filter: function(node) {
                    return node.label.includes(searchValue);
                }
            });
            const filteredNodeIds = new Set(filteredNodes.map(node => node.id));
    
            // 获取与查询结果节点相连的所有节点
            const connectedNodes = new Set();
            const connectedEdges = allEdges.get({
                filter: function(edge) {
                    if (filteredNodeIds.has(edge.from) || filteredNodeIds.has(edge.to)) {
                        connectedNodes.add(edge.from);
                        connectedNodes.add(edge.to);
                        return true;
                    }
                    return false;
                }
            });
    
            // 合并查询结果节点及其相连的节点
            const finalNodeIds = new Set([...filteredNodeIds, ...connectedNodes]);
            const finalNodes = allNodes.get({
                filter: function(node) {
                    return finalNodeIds.has(node.id);
                }
            });
            const finalEdges = new vis.DataSet(connectedEdges);
    
            createNetwork(new vis.DataSet(finalNodes), finalEdges);
    
            // 更新表格数据
            const filteredData = globalData.filter(row => {
                return row.BUName_ZH && row.BUName_ZH.includes(searchValue);
            });
            displayDataTable(filteredData);
        }
    });
        document.getElementById('clearButton').addEventListener('click', function() {
            createNetwork(allNodes, allEdges);
            document.getElementById('searchInput').value = '';
            displayDataTable(globalData); // 恢复显示所有数据
        });

        document.getElementById('fullscreenButton').addEventListener('click', function() {
            const networkContainer = document.getElementById('myNetwork');
            if (!document.fullscreenElement) {
                networkContainer.requestFullscreen().catch(err => {
                    alert(`Error attempting to enable full-screen mode: ${err.message} (${err.name})`);
                });
            } else {
                document.exitFullscreen();
            }
        });

        function processExcelData(data) {
            const nodes = new vis.DataSet();
            const edges = new vis.DataSet();
            
            // 用于跟踪唯一节点ID
            const nodeMap = new Map();
            let nodeId = 1;

            // 处理每一行数据
            data.forEach(row => {
                // 创建BUName_ZH节点 (level 0)
                if (row.BUName_ZH && !nodeMap.has(row.BUName_ZH)) {
                    nodeMap.set(row.BUName_ZH, nodeId);
                    nodes.add({
                        id: nodeId,
                        label: row.BUName_ZH,
                        level: 0,
                        group: 'bu',
                        title: `BU名称: ${row.BUName_ZH}`
                    });
                    
                    // 存储节点详情
                    nodeDetailsMap.set(nodeId, {
                        Stage: row.Stage || '',
                        Batch: row.Batch || '',
                        BUName: row.BUName || '',
                        BUName_ZH: row.BUName_ZH || '',
                        StorageName: row.StorageName || '',
                        StorageAddress: row.StorageAddress || '',
                        RemoteDirectory: row.RemoteDirectory || '',
                        'Size(GB)': row['Size(GB)'] || '',
                        'Used(GB)': row['Used(GB)'] || '',
                        'Mounted on': row['Mounted on'] || ''
                    });
                    
                    nodeId++;
                }

                // 创建StorageName节点 (level 1)
                if (row.StorageName && !nodeMap.has(row.StorageName)) {
                    const buNameNodeId = nodeMap.get(row.BUName_ZH);
                    nodeMap.set(row.StorageName, nodeId);
                    nodes.add({
                        id: nodeId,
                        label: row.StorageName,
                        level: 1,
                        group: 'storage',
                        title: row.StorageName
                    });
                    
                    // 存储节点详情
                    nodeDetailsMap.set(nodeId, {
                        Stage: row.Stage || '',
                        Batch: row.Batch || '',
                        BUName: row.BUName || '',
                        BUName_ZH: row.BUName_ZH || '',
                        StorageName: row.StorageName || '',
                        StorageAddress: row.StorageAddress || '',
                        RemoteDirectory: row.RemoteDirectory || '',
                        'Size(GB)': row['Size(GB)'] || '',
                        'Used(GB)': row['Used(GB)'] || '',
                        'Mounted on': row['Mounted on'] || ''
                    });
                    
                    // 连接StorageName到BUName_ZH
                    edges.add({
                        from: nodeId,
                        to: buNameNodeId
                    });
                    nodeId++;
                }

                // 创建StorageAddress节点 (level 2)
                if (row.StorageAddress && !nodeMap.has(row.StorageAddress)) {
                    const storageNameNodeId = nodeMap.get(row.StorageName);
                    nodeMap.set(row.StorageAddress, nodeId);
                    nodes.add({
                        id: nodeId,
                        label: row.StorageAddress,
                        level: 2,
                        // group: row['Size(GB)'] || 'default',
                        group: 'StorageAddress',
                        title: row.StorageAddress
                    });
                    
                    // 存储节点详情
                    nodeDetailsMap.set(nodeId, {
                        Stage: row.Stage || '',
                        Batch: row.Batch || '',
                        BUName: row.BUName || '',
                        BUName_ZH: row.BUName_ZH || '',
                        StorageName: row.StorageName || '',
                        StorageAddress: row.StorageAddress || '',
                        RemoteDirectory: row.RemoteDirectory || '',
                        'Size(GB)': row['Size(GB)'] || '',
                        'Used(GB)': row['Used(GB)'] || '',
                        'Mounted on': row['Mounted on'] || ''
                    });
                    
                    // 连接StorageAddress到StorageName
                    edges.add({
                        from: nodeId,
                        to: storageNameNodeId
                    });
                    nodeId++;
                }

                // 创建RemoteDirectory节点 (level 3)
                if (row.RemoteDirectory && !nodeMap.has(row.RemoteDirectory)) {
                    const storageAddressNodeId = nodeMap.get(row.StorageAddress);
                    nodeMap.set(row.RemoteDirectory, nodeId);
                    nodes.add({
                        id: nodeId,
                        label: row.RemoteDirectory,
                        level: 3,
                        // group: row['Size(GB)'] || 'default',
                        Group: 'server',
                        title: row.RemoteDirectory
                    });
                    
                    // 存储节点详情
                    nodeDetailsMap.set(nodeId, {
                        Stage: row.Stage || '',
                        Batch: row.Batch || '',
                        BUName: row.BUName || '',
                        BUName_ZH: row.BUName_ZH || '',
                        StorageName: row.StorageName || '',
                        StorageAddress: row.StorageAddress || '',
                        RemoteDirectory: row.RemoteDirectory || '',
                        'Size(GB)': row['Size(GB)'] || '',
                        'Used(GB)': row['Used(GB)'] || '',
                        'Mounted on': row['Mounted on'] || ''
                    });
                    
                    // 连接RemoteDirectory到StorageAddress
                    edges.add({
                        from: nodeId,
                        to: storageAddressNodeId
                    });
                    nodeId++;
                }
            });

            return { nodes, edges };
        }
function createNetwork(nodes, edges) {
    const container = document.getElementById('myNetwork');
    const data = { nodes, edges };
    const options = {
        layout: {
            hierarchical: {
                direction: 'LR',
                sortMethod: 'directed'
            }
        },
        nodes: {
            shape: 'box',
            margin: 10,
            widthConstraint: {
                minimum: 100,
                maximum: 100
            },
            font: {
                size: 14,
                multi: true,
                align: 'center'
            }
        },
        edges: {
            width: 2,
            color: {
                color: '#2B7CE9',
                highlight: '#FFA500',
                hover: '#848484'
            },
            smooth: {
                type: 'cubicBezier',
                forceDirection: 'horizontal',
                roundness: 0.4
            }
        },
        physics: {
            hierarchicalRepulsion: {
                nodeDistance: 100
            }
        },
        groups: {
                bu: {
                color: {    background: '#4ECDC4', border: '#45B7A4'},
                    icon: { face: '"Font Awesome 5 Free"', code: '\uf06e', size: 50 }
                },
                storage: {
                   
                    color: {background: '#96CEB4', border: '#88BEA6' },
                    icon: { face: '"Font Awesome 5 Free"', code: '\uf233', size: 50 }
                },
                StorageAddress: {
                    
                    color: { background: '#45B7D1', border: '#3DA1B9' },
                    icon: { face: '"Font Awesome 5 Free"', code: '\uf233', size: 50 }
                },
                server: {
                  
                    color: {background: '#ffeead', border: '#ffeead' },
                    icon: { face: '"Font Awesome 5 Free"', code: '\uf233', size: 50 }
                }
            },
    };
    const network = new vis.Network(container, data, options);

    // 添加节点点击事件
    network.on('click', function(params) {
        if (params.nodes.length > 0) {
            const nodeId = params.nodes[0];
            showNodeDetails(nodeId);
        }
    });
}
        function showNodeDetails(nodeId) {
            const nodeDetails = nodeDetailsMap.get(nodeId);
            if (!nodeDetails) return;

            const modal = document.getElementById('nodeDetailModal');
            const modalNodeLabel = document.getElementById('modalNodeLabel');
            const modalNodeDetails = document.getElementById('modalNodeDetails');

            // 设置节点标签
            const node = document.querySelector(`[data-node-id="${nodeId}"]`);
            modalNodeLabel.textContent = `节点详情: ${node ? node.textContent : '未知节点'}`;

            // 创建详情表格
            const detailTable = document.createElement('table');
            const detailsToShow = [
                'Stage', 'Batch', 'BUName', 'BUName_ZH', 
                'StorageName', 'StorageAddress', 
                'RemoteDirectory', 'Size(GB)', 'Used(GB)', 'Mounted on'
            ];

            detailsToShow.forEach(key => {
                const row = detailTable.insertRow();
                const cellKey = row.insertCell(0);
                const cellValue = row.insertCell(1);
                cellKey.textContent = key;
                cellValue.textContent = nodeDetails[key] || '无';
            });

            // 清空并添加新的详情
            modalNodeDetails.innerHTML = '';
            modalNodeDetails.appendChild(detailTable);

            // 显示模态框
            modal.style.display = 'block';
        }

        // 模态框关闭事件
        document.querySelector('.modal-close').addEventListener('click', function() {
            document.getElementById('nodeDetailModal').style.display = 'none';
        });

        // 点击模态框外部关闭
        window.addEventListener('click', function(event) {
            const modal = document.getElementById('nodeDetailModal');
            if (event.target == modal) {
                modal.style.display = 'none';
            }
        });

        function displayDataTable(data) {
            const table = document.getElementById('dataTable');
            table.innerHTML = ''; // 清空之前的表格

            // 创建表头
            const headers = Object.keys(data[0]);
            const headerRow = table.insertRow();
            headers.forEach(header => {
                const th = document.createElement('th');
                th.textContent = header;
                headerRow.appendChild(th);
            });

            // 填充数据
            data.forEach(row => {
                const tr = table.insertRow();
                headers.forEach(header => {
                    const td = tr.insertCell();
                    td.textContent = row[header] || '';
                });
            });
        }
//重新上传文件后刷新相关数据
                document.getElementById('fileInput').addEventListener('change', function(e) {
            const file = e.target.files[0];
            const reader = new FileReader();
        
            reader.onload = function(event) {
                const data = new Uint8Array(event.target.result);
                const workbook = XLSX.read(data, {type: 'array'});
                
                // 假设数据在第一个工作表
                const worksheet = workbook.Sheets[workbook.SheetNames[0]];
                const jsonData = XLSX.utils.sheet_to_json(worksheet);
                globalData = jsonData; // 保存全局数据
        
                // 处理数据并生成Vis-Network格式
                const { nodes, edges } = processExcelData(jsonData);
        
                // 保存所有节点和边
                allNodes = nodes;
                allEdges = edges;
        
                // 显示原始数据表格
                displayDataTable(jsonData);
        
                // 创建网络图
                createNetwork(nodes, edges);
            };
        
            reader.readAsArrayBuffer(file);
        });
    </script>
</body>
</html>