import { feedbacks } from '@/api/common'
import { LikeOutlined, DislikeOutlined } from '@ant-design/icons'
import { Flex, Space, Tooltip } from 'antd'
import { useState } from 'react'

const FeedbackButtons = ({ messageId, token }: { messageId: string; token: string }) => {
  const [likeStatus, setLikeStatus] = useState<boolean | null>(null) // null: 未操作, true: 点赞, false: 不喜欢

  const handleFeedback = (status: boolean | null) => {
    if (likeStatus === status) {
      setLikeStatus(null) // 取消操作
      feedbacks(messageId, token, 'null')
    } else {
      setLikeStatus(status) // 设置新状态
      feedbacks(messageId, token, status ? 'like' : 'dislike')
    }
  }

  return (
    <Flex>
      <Space size={12}>
        <Tooltip title={likeStatus === true ? '取消点赞' : '点赞'}>
          <LikeOutlined
            onClick={() => handleFeedback(true)}
            style={{ color: likeStatus === true ? '#1890ff' : undefined, fontSize: '20px', cursor: 'pointer' }}
          />
        </Tooltip>
        <Tooltip title={likeStatus === false ? '取消不喜欢' : '不喜欢'}>
          <DislikeOutlined
            onClick={() => handleFeedback(false)}
            style={{ color: likeStatus === false ? '#ff4d4f' : undefined, fontSize: '20px', cursor: 'pointer' }}
          />
        </Tooltip>
      </Space>
    </Flex>
  )
}

export default FeedbackButtons
