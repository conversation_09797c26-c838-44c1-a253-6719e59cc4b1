@import '@/assets/styles/variables.less';
.copilot-header{
  width: 100%;
  background: linear-gradient(180deg, #BDE1FF 0%, rgba(224, 242, 255, 0) 100%);
  border-radius: .5rem .5rem 0 0;
  padding: var(--ant-margin-md) var(--ant-margin-lg);
  min-height: 72px;
  .main-title{
    color: transparent;
    background: @linear-gradient-1;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    user-select: none;
    font-size: 30px;
    font-weight: bold;
  }
  .sub-title{
    color:#333;
    font-size: var(--ant-font-size-lg);
    margin-top: var(--ant-margin-sm)
  }
}