export function extractJSONFromString(text: string) {
  try {
    // 首先尝试匹配 ```json``` 格式
    const jsonBlockPattern = /```(?:json|JSON)\s*\n?(.*?)\s*```/s
    let match = text.trim().match(jsonBlockPattern)

    if (match) {
      return match[1].trim()
    } else {
      // 如果没有找到```json```标记，尝试直接匹配JSON对象
      const jsonPattern = /{.*}/s
      match = text.match(jsonPattern)
      if (match) {
        return match[0].trim()
      }
    }
    return null
  } catch (e) {
    throw new Error(`发生错误：${e}`)
  }
}

export function extractAllJSONFromString(text: string): string[] {
  const jsonBlocks: string[] = []
  // 匹配所有以```json或```JSON开头、以```结尾的块，不区分大小写
  const jsonBlockRegex = /```(?:json|JSON)\s*([\s\S]*?)\s*```/gi
  const blockMatches = text.matchAll(jsonBlockRegex)

  for (const match of blockMatches) {
    const content = match[1]?.trim()
    if (content) {
      jsonBlocks.push(content)
    }
  }

  if (jsonBlocks.length > 0) {
    return jsonBlocks
  }

  // 如果没有找到块，尝试匹配行内的JSON对象
  const inlineJsonRegex = /{[\s\S]*?}/g
  const inlineMatches = text.match(inlineJsonRegex) || []
  return inlineMatches.map(item => item.trim())
}
