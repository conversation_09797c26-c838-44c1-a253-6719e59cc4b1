declare namespace RAGChat {
  /**
   * 聊天列表中的每一个聊天项类型声明
   */
  export interface ChatItem {
    /**
     * 本次输入/输出的唯一id
     */
    id: string

    /**
     * 本次输入/输出的纯文本内容
     */
    content: string

    /**
     * 类型：AI-AI输出，USER-用户输入
     */
    type: 'AI' | 'USER'

    /**
     * 后端返回的消息id
     */
    message_id?: string

    /**
     * AI智能助手的思考步骤，当对接应用为智能助手类型时，该字段才可能不为空
     */
    agent_thoughts?: Array<AgentThoughtItem>

    /**
     * 该输入/输出需要展示的文件
     */
    message_files?: Array<VisibleFileItem>
  }

  /**
   * 智能助手的每一个思考步骤类型声明
   */
  export interface AgentThoughtItem {
    /**
     * 助手思考的内容
     */
    thought: string

    /**
     * agent思考在整个消息中的位置，比如如果是第一轮迭代则该值为1，用于数据替换
     */
    position: number

    /**
     * 使用的工具列表，以【;】分割多个工具
     */
    tool: string

    /**
     * 工具的输入，JSON格式的字符串
     */
    tool_input: string

    /**
     * 工具调用的返回结果，JSON格式的字符串
     */
    observation: string
  }

  /**
   * 用户可见的文件类型声明
   */
  export interface VisibleFileItem {
    /**
     * 文件id
     */
    id: string

    /**
     * 文件类型，暂时只支持image-图片
     */
    type: 'image'

    /**
     * 文件归属，暂时只支持assistant-AI输出
     */
    belongs_to: 'assistant'

    /**
     * 文件路径
     */
    url: string
  }

  /**
   * AI输出错误数据类型声明
   */
  export interface AgentError {
    /**
     * 任务id，用于请求跟踪或停止SSE
     */
    task_id: string
    /**
     * 消息的唯一id
     */
    message_id: string
    /**
     * http状态码
     */
    status: number
    /**
     * 错误码
     */
    code: string
    /**
     * 错误信息
     */
    message: string
  }

  /**
   * AI调用的请求参数类型
   */
  export interface ChatRequest {
    /**
     * app应用设定的变量值，包含了多组键值对，本质上是一个Object
     */
    inputs: Record<string, any>
    /**
     * 用户提问的文本内容，如果用户携带了提示词，这里是经过提示词转化后的最终提问内容
     * 比如用户在输入框内输入“今天天气如何？”：
     * 1.如果用户没有选择提示词，则query="今天天气如何？"
     * 2.如果用户选择了提示词：回答下列问题：${content}，则query="回答下列问题：今天天气如何？"
     */
    query: string
    /**
     * 消息响应模式：
     * 1.streaming: 基于SSE的流式输出结果
     * 2.blocking（待扩展，暂不开放）：阻塞式输出，服务端等待AI回答完毕后将内容一口气返回
     */
    response_mode: 'streaming'
    /**
     * 当前用户标识
     */
    user: string
    /**
     * 当前会话的id，如需基于某次聊天的内容进行继续聊天，此处需要传之前消息返回的conversation_id
     */
    conversation_id?: string
    /**
     * 用户上传的文件
     */
    files?: Array<ChatFileRequest>
    /**
     * 是否自动生成聊天的标题
     */
    auto_generate_name?: boolean
  }

  /**
   * AI调用传输的文件参数类型
   */
  export interface ChatFileRequest {
    /**
     * 文件的类型，目前仅支持image
     */
    type: 'image'
    /**
     * 文件的传递方式：
     * 1.remote_url: 传递一个公网可访问的图片地址
     * 2.local_file: 上传文件流
     */
    transfer_method: 'remote_url' | 'local_file'
    /**
     * 当transfer_method为remote_url时，对应的图片地址
     */
    url?: string
    /**
     * 当transfer_method为local_file时，对应文件的id（需要先调用上传文件接口）
     */
    upload_file_id?: string
  }

  /**
   * AI调用流式输出的事件类型枚举
   */
  export enum StreamingEventType {
    /**
     * 文本事件：AI纯文本输出事件
     */
    MESSAGE = 'message',
    /**
     * 智能文本输出事件：智能模式下AI纯文本输出事件
     */
    AGENT_MESSAGE = 'agent_message',
    /**
     * 思考步骤事件：展示智能模式下AI的思考步骤，涉及到工具调用
     */
    AGENT_THOUGHT = 'agent_thought',
    /**
     * 文件事件：表示有新的文件需要展示
     */
    MESSAGE_FILE = 'message_file',
    /**
     * 消息结束事件：收到此事件代表流式输出正常完成
     */
    MESSAGE_END = 'message_end',
    /**
     * 消息内容替换事件：待变消息内容被替换，在内容审计场景下可能会发生
     */
    MESSAGE_REPLACE = 'message_replace',
    /**
     * 异常事件：流式输出过程发生异常，需要客户端自行处理
     */
    ERROR = 'error',
    /**
     * 每隔一段事件EventStream会继续宁ping，保持连接存活
     */
    PING = 'ping'
  }

  // TODO 设计阻塞式输出的响应结果类型
  type BlockingChatResponse = undefined

  /**
   * AI调用的流式输出返回结果类型
   */
  export type StreamingChatResponse<T> = T extends StreamingEvent ? T : never

  /**
   * 声明式接口，继承该接口则代表属于一个流式输出的事件类型
   */
  interface StreamingEvent {
    /**
     * 事件类型
     */
    event: StreamingEventType
  }
}

/* AI聊天相关的类型 */
export = RAGChat
export as namespace RAGChat
