<!-- 东方 -->
<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合同校验系统</title>
    <script src="./tailwindcss.js"></script>
    <!-- <script src="/marked.min.js"></script> -->
    <style>
        .h-full {
            height: 140% !important;
        }

        .modal {
            display: none;
        }

        .modal.active {
            display: flex;
        }
    </style>
</head>

<body class="bg-gray-50">
    <div class="container mx-auto px-4 py-8 max-w-6xl">
        <h1 class="text-2xl font-bold mb-6 text-center">合同校验系统</h1>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- 左侧：校验规则 -->
            <div class="space-y-6">
                <!-- 上传按钮 -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h2 class="text-lg font-semibold mb-4">合同上传</h2>
                    <div class="flex items-center space-x-2">
                        <input type="file" id="contractFile" accept=".pdf" class="hidden"
                            onchange="handleFileSelect(event)">
                        <label for="contractFile"
                            class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded cursor-pointer flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 4v16m8-8H4" />
                            </svg>
                            上传合同
                        </label>
                        <span class="text-sm text-gray-500">仅支持 PDF 格式</span>
                    </div>
                </div>

                <!-- 校验规则选择 -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h2 class="text-lg font-semibold mb-4 flex justify-between items-center">
                        校验规则
                        <button id="toggleRulesBtn" class="text-blue-500 hover:text-blue-700 focus:outline-none">
                            收起
                        </button>
                    </h2>
                    <div id="rulesContent" class="space-y-4 ">
                        <!-- 财务条款 -->
                        <div class="border rounded-lg p-4">
                            <div class="flex items-center mb-3">
                                <input type="checkbox" id="financial" class="w-4 h-4 text-blue-600 rounded" checked>
                                <label for="financial" class="ml-2 font-medium">付款金额</label>
                            </div>
                            <div class="ml-6 space-y-2 text-sm text-gray-600">
                                <div class="flex items-center">
                                    <input type="checkbox" class="w-4 h-4 text-blue-600 rounded" checked>
                                    <label class="ml-2">总金额是否明确</label>
                                </div>
                                <div class="flex items-center">
                                    <input type="checkbox" class="w-4 h-4 text-blue-600 rounded" checked>
                                    <label class="ml-2">付款方式是否约定</label>
                                </div>
                                <div class="flex items-center">
                                    <input type="checkbox" class="w-4 h-4 text-blue-600 rounded" checked>
                                    <label class="ml-2">税费承担是否明确</label>
                                </div>
                                <div class="flex items-center">
                                    <input type="checkbox" class="w-4 h-4 text-blue-600 rounded" checked>
                                    <label class="ml-2">额外费用处理是否约定</label>
                                </div>
                                <div class="flex items-center">
                                    <input type="checkbox" class="w-4 h-4 text-blue-600 rounded" checked>
                                    <label class="ml-2">价格调整机制是否明确</label>
                                </div>
                            </div>
                        </div>

                        <!-- 合同条款 -->
                        <div class="border rounded-lg p-4">
                            <div class="flex items-center mb-3">
                                <input type="checkbox" id="contract" class="w-4 h-4 text-blue-600 rounded" checked>
                                <label for="contract" class="ml-2 font-medium">付款节点</label>
                            </div>
                            <div class="ml-6 space-y-2 text-sm text-gray-600">
                                <div class="flex items-center">
                                    <input type="checkbox" class="w-4 h-4 text-blue-600 rounded" checked>
                                    <label class="ml-2">首付款条件是否明确</label>
                                </div>
                                <div class="flex items-center">
                                    <input type="checkbox" class="w-4 h-4 text-blue-600 rounded" checked>
                                    <label class="ml-2">进度款节点是否合理</label>
                                </div>
                                <div class="flex items-center">
                                    <input type="checkbox" class="w-4 h-4 text-blue-600 rounded" checked>
                                    <label class="ml-2">验收款比例是否合理</label>
                                </div>
                                <div class="flex items-center">
                                    <input type="checkbox" class="w-4 h-4 text-blue-600 rounded" checked>
                                    <label class="ml-2">质保金比例是否约定</label>
                                </div>
                                <div class="flex items-center">
                                    <input type="checkbox" class="w-4 h-4 text-blue-600 rounded" checked>
                                    <label class="ml-2">付款延迟处理是否约定</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <button id="submitBtn"
                    class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded">开始校验</button>
                <!-- 校验结果 -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h2 class="text-lg font-semibold mb-4">校验结果</h2>
                    <div id="validationResult" class="hidden">

                    </div>
                    <div id="resultContainer" class="mt-4 p-4 bg-gray-50 rounded-md min-h-[200px] border">
                        <p class="text-gray-500">查询结果将在这里显示</p>
                    </div>
                    <!-- <div id="noValidationResult" class="text-center text-gray-500 py-8">
                        请上传文件并选择校验规则
                    </div> -->
                    <div id="debugContainer" class="mt-4 p-4 bg-red-50 rounded-md text-red-800 hidden">
                        调试信息
                    </div>
                </div>
            </div>

            <!-- 右侧：PDF 预览 -->
            <div class="bg-white rounded-lg shadow">
                <div class="p-6 border-b">
                    <h2 class="text-lg font-semibold">合同预览</h2>
                </div>
                <div id="fileInfo" class="hidden flex flex-col h-[calc(100vh-300px)]">
                    <!-- PDF 信息栏 -->
                    <div class="px-6 py-3 bg-gray-50 border-b">
                        <div class="flex items-center justify-between">
                            <div class="text-sm">
                                <span class="font-medium" id="fileName">未选择文件</span>
                                <span class="text-gray-500 ml-2" id="fileSize"></span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button onclick="toggleFullscreen()" class="p-2 hover:bg-gray-200 rounded" title="全屏">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M4 8V4m0 0h4M4 4l5 5m11-5V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5v-4m0 4h-4m4 0l-5-5" />
                                    </svg>
                                </button>
                                <button onclick="downloadPDF()" class="p-2 hover:bg-gray-200 rounded" title="下载">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                    <!-- PDF 预览区域 -->
                    <div class="flex-1 relative">
                        <embed id="pdfViewer" class="w-full h-full" type="application/pdf" src="" toolbar="0"
                            navpanes="0" scrollbar="0">
                    </div>
                </div>
                <div id="noFileInfo" class="flex items-center justify-center h-[calc(100vh-300px)]">
                    <div class="text-center text-gray-500">
                        <svg class="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        <p class="text-lg">暂无文件预览</p>
                        <p class="text-sm mt-2">请上传 PDF 格式的合同文件</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div id="errorModal" class="modal fixed inset-0 bg-black bg-opacity-50 items-center justify-center">
        <div style="width: 500px;" class="bg-white rounded-lg p-6 max-w-sm mx-auto relative">
            <p id="errorMessage" class="mb-4"></p>
            <div class="mb-4">
                <label for="keyInput" class="block text-sm font-medium text-gray-700 mb-1">请输入新的Key:</label>
                <input type="text" id="keyInput" class="w-full px-3 py-2 border border-gray-300 rounded-md">
            </div>
            <div class="flex justify-end space-x-2">
                <button id="cancelModal"
                    class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400">取消</button>
                <button id="confirmKey" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">确认</button>
            </div>
        </div>
    </div>

    <script>
        const closeModalButton = document.getElementById('cancelModal');
        const confirmKeyButton = document.getElementById('confirmKey');
        const modal = document.getElementById('errorModal');
        const errorMessage = document.getElementById('errorMessage');
        const keyInput = document.getElementById('keyInput');

        // 存储当前URL参数
        const urlParams = new URLSearchParams(window.location.search);

        // 动态显示弹窗并传入错误信息  
        function showErrorModal (message) {
            errorMessage.textContent = message;
            modal.classList.add('active');
        }

        closeModalButton.addEventListener('click', () => {
            modal.classList.remove('active');
        });

        confirmKeyButton.addEventListener('click', () => {
            const newKey = keyInput.value.trim();
            if (newKey) {
                // 更新URL中的key参数
                urlParams.set('key', newKey);
                window.history.replaceState({}, '', `${window.location.pathname}?${urlParams.toString()}`);

                // 关闭弹窗
                modal.classList.remove('active');

                // 可以在这里添加重新尝试操作的逻辑
                // 例如: retryLastOperation();
            } else {
                alert('请输入有效的Key');
            }
        });

        // Optional: Close modal when clicking outside of it  
        modal.addEventListener('click', (event) => {
            if (event.target === modal) {
                modal.classList.remove('active');
            }
        });

        let currentPdfUrl = null;
        let uploadedFileId = null;
        document.getElementById('toggleRulesBtn').addEventListener('click', function () {
            const rulesContent = document.getElementById('rulesContent');
            if (rulesContent.classList.contains('hidden')) {
                rulesContent.classList.remove('hidden');
                this.textContent = '收起';
            } else {
                rulesContent.classList.add('hidden');
                this.textContent = '展开';
            }
        });
        function handleFileSelect (event) {
            const file = event.target.files[0];
            if (file) {
                if (file.type !== 'application/pdf') {
                    alert('请上传 PDF 格式的文件');
                    return;
                }

                // 显示文件信息
                document.getElementById('fileInfo').classList.remove('hidden');
                document.getElementById('noFileInfo').classList.add('hidden');
                document.getElementById('fileName').textContent = file.name;
                document.getElementById('fileSize').textContent = formatFileSize(file.size);

                // 如果存在之前的 URL，先释放它
                if (currentPdfUrl) {
                    URL.revokeObjectURL(currentPdfUrl);
                }

                // 创建新的文件 URL
                currentPdfUrl = URL.createObjectURL(file);
                const pdfViewer = document.getElementById('pdfViewer');
                pdfViewer.src = currentPdfUrl + '#toolbar=0&navpanes=0&scrollbar=0';

                // 上传文件
                uploadFile('app-1m4cuhm4eX1BkI7ygRXe4Piz', file).then(fileId => {
                    uploadedFileId = fileId;
                    // 显示校验结果
                    setTimeout(() => {
                        document.getElementById('validationResult').classList.remove('hidden');
                        // document.getElementById('noValidationResult').classList.add('hidden');
                    }, 1500);
                }).catch(error => {
                    console.error('文件上传错误', error);
                    alert('文件上传失败');
                });
            }
        }

        function extractContent (str, tag) {
            // 创建正则表达式以匹配指定标签内的内容
            const regex = new RegExp(`<${tag}>(.*?)</${tag}>`, 'gs')
            const matches = []
            let match = null

            // 使用正则表达式提取内容
            while ((match = regex.exec(str)) !== null) {
                matches.push(match[1].trim()) // match[1] 是标签内的内容
            }

            return matches[0] || ''
        }

        function formatFileSize (bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function toggleFullscreen () {
            const pdfViewer = document.getElementById('pdfViewer');
            if (!document.fullscreenElement) {
                pdfViewer.requestFullscreen().catch(err => {
                    alert(`全屏模式错误: ${err.message}`);
                });
            } else {
                document.exitFullscreen();
            }
        }

        function downloadPDF () {
            if (currentPdfUrl) {
                const link = document.createElement('a');
                link.href = currentPdfUrl;
                link.download = document.getElementById('fileName').textContent;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }
        }

        async function callChatMessagesAPI (apikey, query, fileId) {
            const key = urlParams.get('key')
            if (!key) {
                showErrorModal('对不起,您的key无效或已过期')
                return
            }

            const resultContainer = document.getElementById('resultContainer');
            const debugContainer = document.getElementById('debugContainer');

            // 重置容器
            resultContainer.innerHTML = '正在分析合同信息，请耐心等待...';
            debugContainer.innerHTML = '';
            debugContainer.classList.add('hidden');

            try {
                const response = await fetch('http://10.1.32.174:90/v1/chat-messages', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${apikey}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        "inputs": {
                            key
                        },
                        "query": query,
                        "response_mode": "blocking", // 阻塞模式
                        "conversation_id": "",
                        "user": "web-user-" + Math.random().toString(36).substr(2, 9),
                        "files": [
                            {
                                type: "document",
                                transfer_method: "local_file",
                                upload_file_id: fileId
                            }
                        ],

                    })
                });

                // 显示完整的响应状态和头部信息
                debugContainer.innerHTML = `
                    响应状态: ${response.status} ${response.statusText}
                    响应头:
                    ${JSON.stringify(Object.fromEntries(response.headers), null, 2)}
                `;
                debugContainer.classList.remove('hidden');

                if (!response.ok) {
                    // 尝试解析错误响应体
                    const errorText = await response.text();
                    throw new Error(`HTTP错误 ${response.status}: ${errorText}`);
                }

                // 解析响应
                const data = await response.json();

                const errorStr = extractContent(data.answer, 'error')
                if (errorStr) {
                    showErrorModal(errorStr)
                    return
                }

                // 提取JSON数据并生成表格
                const jsonData = JSON.parse(data.answer.replace(/```json\n|```/g, ''));

                let tableHTML = '<table class="min-w-full bg-white"><thead><tr><th class="py-2">规则项</th><th class="py-2">检验结果</th><th class="py-2">分析内容</th></tr></thead><tbody>';
                jsonData.forEach(item => {
                    tableHTML += `<tr><td class="border px-4 py-2">${item.规则项}</td><td class="border px-4 py-2">${item.检验结果}</td><td class="border px-4 py-2">${item.分析内容}</td></tr>`;
                });
                tableHTML += '</tbody></table>';

                // 显示结果
                resultContainer.innerHTML = tableHTML;
            } catch (error) {
                // 详细的错误处理
                console.error('查询错误', error);
                resultContainer.innerHTML = `错误: ${error.message}`;

                // 显示详细的调试信息
                debugContainer.innerHTML += `
                    <br>捕获的错误: ${error.message}
                    <br>错误堆栈: ${error.stack}
                `;
                debugContainer.classList.remove('hidden');
            }
        }

        async function uploadFile (apikey, file) {
            const formData = new FormData();
            formData.append('file', file);
            formData.append('user', 'web-user-' + Math.random().toString(36).substr(2, 9));

            try {
                const response = await fetch('http://10.1.32.174:90/v1/files/upload', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${apikey}`
                    },
                    body: formData
                });
                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`HTTP错误 ${response.status}: ${errorText}`);
                }

                const data = await response.json();
                return data.id; // 假设返回的JSON中包含文件ID
            } catch (error) {
                console.error('文件上传错误', error);
                throw error;
            }
        }

        document.getElementById('submitBtn').addEventListener('click', async () => {
            const checkboxes = document.querySelectorAll('input[type="checkbox"]:checked');
            const query = Array.from(checkboxes).map(checkbox => checkbox.nextElementSibling.textContent.trim()).join(' ');

            // 收起校验规则内容
            const rulesContent = document.getElementById('rulesContent');
            rulesContent.classList.add('hidden');
            document.getElementById('toggleRulesBtn').textContent = '展开';

            if (uploadedFileId) {
                callChatMessagesAPI('app-1m4cuhm4eX1BkI7ygRXe4Piz', query, uploadedFileId);
            } else {
                alert('请先上传合同文件');
            }
        });

        // 清理资源
        window.addEventListener('beforeunload', () => {
            if (currentPdfUrl) {
                URL.revokeObjectURL(currentPdfUrl);
            }
        });

        document.addEventListener("DOMContentLoaded", function () {
            const key = new URLSearchParams(window.location.search).get('key')
            if (!key) {
                showErrorModal('对不起,您的key无效或已过期')
            }
        });
    </script>
</body>

</html>