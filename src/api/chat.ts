import request from "@/utils/request";
import { getUserInfo } from "@/utils/auth";

const API_BASE_INS: string = import.meta.env["VITE_API_BASE_INS"] || "";
// 停止响应
export function stopMessage(data: any) {
  let path: string = `/chat-messages/${data.taskId}/stop`;
  if (data.type === "completion") {
    path = `/completion-messages/${data.taskId}/stop`;
  }
  const userInfo = getUserInfo();
  return request({
    url: `${API_BASE_INS}/dify/broker/json`,
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: {
      agentId: data.agentId,
      path,
      difyJson: {
        user: userInfo?.id,
      },
      method: "POST",
    },
  });
}
