.left-sidebar-module {
  position: relative;
  
  // 会话管理侧边栏
  .session-sidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: 300px;
    height: 100vh;
    background: #fff;
    border-right: 1px solid #e8e8e8;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    overflow-y: auto;

    &.visible {
      transform: translateX(0);
    }

    .sidebar-header {
      padding: 16px;
      border-bottom: 1px solid #e8e8e8;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .sidebar-title {
        font-size: 16px;
        font-weight: 600;
        color: #262626;
      }

      .close-btn {
        border: none;
        background: none;
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;

        &:hover {
          background: #f5f5f5;
        }
      }
    }

    .sidebar-content {
      padding: 16px;
    }
  }

  // 侧边栏切换按钮
  .sidebar-toggle {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1001;
    border: 1px solid #d9d9d9;
    background: #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: left 0.3s ease;

    &.sidebar-open {
      left: 320px;
    }
  }
}

