.question-module {
  padding: 0 20px;
  background: #fff;
  border-radius: 8px;
  // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: 100%;

  // 顶部 Tabs（胶囊样式）
  .question-tabs {
    height: 100%;
    .ant-tabs-nav {
      margin: 0 0 var(--ant-margin-lg);
      &::before {
        border-bottom: none;
      }
      
    }
    .ant-tabs-content-holder{
      overflow: auto;
    }
    .ant-tabs-content-holder::-webkit-scrollbar {
      display: none;
    }
    .ant-tabs-tab {
      background: #f5f7fa;
      border-radius: 20px;
      padding: 6px 16px;
      // margin-right: 8px;
      line-height: 1;
    }

    .ant-tabs-tab-active {
      background: #1677ff;
    }

    .ant-tabs-tab-active .ant-tabs-tab-btn {
      color: #fff;
    }

    .ant-tabs-ink-bar {
      display: none;
    }
  }

  .question-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;

    h3 {
      margin: 0;
      color: #1890ff;
      font-size: 20px;
      font-weight: 600;
    }

    .question-actions {
      display: flex;
      gap: 8px;
    }
  }

  .question-filters {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 16px;
    background: #fafafa;
    border-radius: 6px;

    .category-filter {
      display: flex;
      align-items: center;
      gap: 12px;

      span {
        font-weight: 500;
        color: #666;
      }

      .ant-tag {
        margin: 0;
        transition: all 0.3s;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
      }
    }

    .question-count {
      color: #666;
      font-size: 14px;
    }
  }

  .questions-list {
    .question-item {
      border: 1px solid #e8e8e8;
      border-radius: 8px;
      transition: all 0.3s;

      &:hover {
        border-color: #1890ff;
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.1);
      }

      .question-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 16px;
        padding-bottom: 12px;
        border-bottom: 1px solid #f0f0f0;

        h4 {
          margin: 0;
          flex: 1;
          font-size: 16px;
          line-height: 1.5;
          color: #333;
        }

        .ant-tag {
          margin-left: 12px;
          flex-shrink: 0;
        }
      }

      .question-details {
        .detail-section {
          margin-bottom: 16px;

          &:last-child {
            margin-bottom: 0;
          }

          strong {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 600;
          }

          .tags-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;

            .ant-tag {
              margin: 0;
              padding: 4px 8px;
              border-radius: 4px;
            }
          }

          .scoring-criteria {
            display: flex;
            flex-direction: column;
            gap: 8px;

            .criterion-item {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 8px 12px;
              background: #f8f9fa;
              border-radius: 4px;
              border-left: 3px solid #1890ff;

              .weight {
                color: #1890ff;
                font-weight: 600;
                font-size: 14px;
              }
            }
          }
        }
      }
    }
  }

  .no-questions {
    text-align: center;
    padding: 40px 20px;
    color: #999;

    p {
      margin: 0;
      font-size: 16px;
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    padding: 16px;

    .question-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .question-actions {
        width: 100%;
        justify-content: flex-end;
      }
    }

    .question-filters {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;

      .category-filter {
        flex-wrap: wrap;
        gap: 8px;
      }
    }

    .questions-list {
      .question-item {
        .question-header {
          flex-direction: column;
          align-items: flex-start;
          gap: 12px;

          h4 {
            font-size: 15px;
          }

          .ant-tag {
            margin-left: 0;
          }
        }
      }
    }
  }
}


.qr-category {
  h3 {
    margin: 0 0 16px;
    font-size: 18px;
    font-weight: 600;
    color: #111;
  }
}

.qr-questions {
  margin: 0;
  padding-left: 24px;

  .qr-question-item {
    margin-bottom: 24px;
  }
}

.qr-question {
  font-size: 16px;
  color: #111;
  margin-bottom: 8px;
}

.qr-section {
  margin: 8px 0 12px;

  strong {
    display: block;
    margin-bottom: 6px;
    color: #333;
    font-weight: 600;
  }
}

.qr-text {
  color: #444;
  line-height: 1.7;
}

.qr-points-list {
  list-style: disc;
  padding-left: 20px;
  margin: 0;

  li {
    line-height: 1.7;
  }
}
