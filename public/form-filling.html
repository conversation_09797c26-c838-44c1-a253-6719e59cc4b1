<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能填写表单</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* 底部固定输入区域样式 */
        #bottom-fixed-area {
    position: fixed;
    bottom: 0;
    right: 0;
    width: 500px;
    border-radius: 10px 0px 0 0;
    background-color: white;
    box-shadow: -4px 0 6px rgba(0,0,0,0.1);
    z-index: 1000;
    padding: 15px;
    transition: all 0.3s ease;
}
        body {
            padding-bottom: 150px; /* 为底部区域预留空间 */
        }
        @media (min-width: 1536px) {
    .container {
        max-width: 100% !important; /* 最大宽度 */
    }
}
    </style>
</head>
<body class="bg-gray-100 p-4">
    <div class="max-w-4xl mx-auto bg-white shadow-lg rounded-lg p-8">
        <h1 class="text-3xl font-bold text-center mb-8 text-blue-700">企业信息登记表</h1>
        
        <form id="enterpriseForm" class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- 基本信息 -->
            <div class="form-group">
                <label class="block text-gray-700 font-bold mb-2">企业名称</label>
                <input type="text" id="enterpriseName" name="enterpriseName" class="w-full px-3 py-2 border rounded-lg" required>
            </div>

            <div class="form-group">
                <label class="block text-gray-700 font-bold mb-2">统一社会信用代码</label>
                <input type="text" id="creditCode" name="creditCode" class="w-full px-3 py-2 border rounded-lg" required>
            </div>

            <div class="form-group">
                <label class="block text-gray-700 font-bold mb-2">所属行业</label>
                <select id="industry" name="industry" class="w-full px-3 py-2 border rounded-lg">
                    <option value="">请选择行业</option>
                    <option value="IT">信息技术</option>
                    <option value="FINANCE">金融服务</option>
                    <option value="MANUFACTURING">制造业</option>
                    <option value="EDUCATION">教育</option>
                    <option value="HEALTHCARE">医疗卫生</option>
                </select>
            </div>

            <div class="form-group">
                <label class="block text-gray-700 font-bold mb-2">企业类型</label>
                <select id="enterpriseType" name="enterpriseType" class="w-full px-3 py-2 border rounded-lg">
                    <option value="">请选择企业类型</option>
                    <option value="STATE_OWNED">国有企业</option>
                    <option value="PRIVATE">民营企业</option>
                    <option value="FOREIGN">外资企业</option>
                    <option value="JOINT_VENTURE">合资企业</option>
                </select>
            </div>

            <div class="form-group">
                <label class="block text-gray-700 font-bold mb-2">注册资本(万元)</label>
                <input type="number" id="registeredCapital" name="registeredCapital" class="w-full px-3 py-2 border rounded-lg">
            </div>

            <div class="form-group">
                <label class="block text-gray-700 font-bold mb-2">成立日期</label>
                <input type="date" id="foundDate" name="foundDate" class="w-full px-3 py-2 border rounded-lg">
            </div>

            <div class="form-group">
                <label class="block text-gray-700 font-bold mb-2">法定代表人</label>
                <input type="text" id="legalRepresentative" name="legalRepresentative" class="w-full px-3 py-2 border rounded-lg">
            </div>

            <div class="form-group">
                <label class="block text-gray-700 font-bold mb-2">联系电话</label>
                <input type="tel" id="contactPhone" name="contactPhone" class="w-full px-3 py-2 border rounded-lg">
            </div>

            <div class="form-group">
                <label class="block text-gray-700 font-bold mb-2">电子邮箱</label>
                <input type="email" id="email" name="email" class="w-full px-3 py-2 border rounded-lg">
            </div>

            <div class="form-group">
                <label class="block text-gray-700 font-bold mb-2">企业网站</label>
                <input type="url" id="website" name="website" class="w-full px-3 py-2 border rounded-lg">
            </div>

            <div class="form-group col-span-2">
                <label class="block text-gray-700 font-bold mb-2">注册地址</label>
                <input type="text" id="registeredAddress" name="registeredAddress" class="w-full px-3 py-2 border rounded-lg">
            </div>

            <div class="form-group">
                <label class="block text-gray-700 font-bold mb-2">省份</label>
                <input type="text" id="province" name="province" class="w-full px-3 py-2 border rounded-lg">
            </div>

            <div class="form-group">
                <label class="block text-gray-700 font-bold mb-2">城市</label>
                <input type="text" id="city" name="city" class="w-full px-3 py-2 border rounded-lg">
            </div>

            <div class="form-group col-span-2">
                <label class="block text-gray-700 font-bold mb-2">主营业务</label>
                <textarea id="mainBusiness" name="mainBusiness" rows="3" class="w-full px-3 py-2 border rounded-lg"></textarea>
            </div>

            <div class="form-group">
                <label class="block text-gray-700 font-bold mb-2">员工总数</label>
                <input type="number" id="employeeCount" name="employeeCount" class="w-full px-3 py-2 border rounded-lg">
            </div>

            <div class="form-group">
                <label class="block text-gray-700 font-bold mb-2">年营业收入(万元)</label>
                <input type="number" id="annualRevenue" name="annualRevenue" class="w-full px-3 py-2 border rounded-lg">
            </div>

            <div class="form-group col-span-2">
                <label class="block text-gray-700 font-bold mb-2">企业简介</label>
                <textarea id="enterpriseIntro" name="enterpriseIntro" rows="4" class="w-full px-3 py-2 border rounded-lg"></textarea>
            </div>

            <div class="form-group">
                <label class="block text-gray-700 font-bold mb-2">税务登记号</label>
                <input type="text" id="taxRegistrationNumber" name="taxRegistrationNumber" class="w-full px-3 py-2 border rounded-lg">
            </div>

            <div class="form-group">
                <label class="block text-gray-700 font-bold mb-2">开户银行</label>
                <input type="text" id="bankName" name="bankName" class="w-full px-3 py-2 border rounded-lg">
            </div>
        </form>

        <div class="mt-6 flex space-x-4">
            <button onclick="autoFillForm()" class="bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600 transition">
                智能填充
            </button>
            <button 
            onclick="resetForm()" 
            class="bg-gray-300 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-400 transition"
        >
            表单重置
        </button>
        </div>
    </div>

    <!-- 左侧固定区域 -->
    <div id="bottom-fixed-area" class="container mx-auto">
        <div class="grid grid-cols-1 gap-4"></div>
            <div>
                <label class="block text-gray-700 font-bold mb-2">智能填写表单</label>
                <button 
                    onclick="document.getElementById('bottom-fixed-area').style.display = 'none';" 
                    class="absolute top-0 right-0 mt-2 mr-2 text-gray-500 hover:text-gray-700 transition"
                >
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
                <textarea 
                    id="additionalNotes" 
                    name="additionalNotes"
                    rows="3" 
                    class="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-200 transition"
                    placeholder="请输入公司信息，例如：北京神州新桥科技有限公司"
                ></textarea>
            </div>
            <pre
            id="search-results"
            class="bg-gray-50 p-4 rounded-lg whitespace-pre-wrap border border-gray-200 hidden"  style="margin-bottom: 10px;"
          >
          </pre>
            <div class="flex justify-center space-x-4">
                <button 
                    onclick="submitForm()" id="submitButton"
                    class="bg-green-500 text-white px-6 py-2 rounded-lg hover:bg-green-600 transition" 
                >
                    智能解析后自动填充表单
                </button>
              
            </div>
        </div>
    </div>
    </div>

    <script>

//流式输出
async function callChatMessagesAPI(apikey, query) {
    const resultContainer = document.getElementById("search-results");
    // 重置容器
    resultContainer.innerHTML = "正在加载...";

    try {
      const response = await fetch(
      "https://copilot.sino-bridge.com:90/v1/chat-messages",
      {
         method: "POST",
         headers: {
            Authorization: `Bearer ${apikey}`,
            "Content-Type": "application/json",
         },
         body: JSON.stringify({
            inputs: { },
            query: query,
            response_mode: "streaming", // 流式模式
            conversation_id: "",
            user: "web-user-" + Math.random().toString(36).substr(2, 9),
            files: [],
         }),
      }
      );

      if (!response.ok) {
      // 尝试解析错误响应体
      const errorText = await response.text();
      throw new Error(`HTTP错误 ${response.status}: ${errorText}`);
      }
    resultContainer.classList.remove("hidden");

      // 处理流式响应
      const reader = response.body.getReader();
      const decoder = new TextDecoder("utf-8");
      let resultText = "";

      while (true) {
      const { done, value } = await reader.read();
      if (done) break;
      const chunk = decoder.decode(value, { stream: true });
      const lines = chunk.split("\n");
      for (const line of lines) {
         if (line.trim()) {
            try {
            if (line.startsWith("data: ")) {
              const jsonString = line.substring(6).trim();
              if (jsonString) {
                 try {
                    const data = JSON.parse(
                      jsonString
                         .replace(/\\n/g, "\\n")
                         .replace(/\\'/g, "\\'")
                         .replace(/\\"/g, '\\"')
                         .replace(/\\&/g, "\\&")
                         .replace(/\\r/g, "\\r")
                         .replace(/\\t/g, "\\t")
                         .replace(/\\b/g, "\\b")
                         .replace(/\\f/g, "\\f")
                    );
                    if (data.event === "message") {
                      resultText += data.answer;
                      resultContainer.innerHTML = `<pre class="whitespace-pre-wrap break-words">${resultText}</pre>`;
                      await new Promise((resolve) => setTimeout(resolve, 50)); // 控制输出速度
                    } else if (data.event === "message_end") {
                   
                    
                      console.log('流式输出结束');
                       // 提取JSON数据并生成表格
            const jsonData = JSON.parse(
                resultText.replace(/```json\n|```/g, "")
            );
            console.log("解析的JSON数据:", jsonData);
      
            // 将解析的JSON数据填充到表单中
            Object.keys(jsonData).forEach(key => {
                 const element = document.getElementById(key);
                 if (element) {
                      element.value = jsonData[key];
                 }
            });
            return; // 结束流式输出
                    }
                 } catch (e) {
                    // console.error('JSON解析错误', e);
                    // console.error('错误的JSON字符串:', jsonString);
                 }
              }
            }
            } catch (e) {
            // console.error('JSON解析错误', e);
            }
         }
      }
      }

      resultText += decoder.decode();
      resultContainer.innerHTML = `<pre class="whitespace-pre-wrap break-words">${resultText}</pre>`;
    } catch (error) {
      // 详细的错误处理
      console.error("查询错误", error);
      resultContainer.innerHTML = "查询错误，请稍后重试。";
    }
 }

 function autoFillForm() {
            document.getElementById('bottom-fixed-area').style.display = 'block';
        }

        // 默认隐藏 bottom-fixed-area
        document.getElementById('bottom-fixed-area').style.display = 'none';
        
    function submitForm() {
        const submitButton = document.getElementById('submitButton');
        submitButton.innerText = '解析中，请等待...';
        submitButton.disabled = true;
        submitButton.classList.add('bg-gray-400');

        const additionalNotes = document.getElementById('additionalNotes').value;

        // 调用 performWebSearch 接口
        const apikey = 'app-fjk3jeYoK5ma42d75AEfu6Zl';
        const query = additionalNotes;
        callChatMessagesAPI(apikey, query).finally(() => {
        submitButton.innerText = '智能解析后自动填充表单';
        submitButton.disabled = false;
        submitButton.classList.remove('bg-gray-400');
        });
    }

        function resetForm() {
            document.getElementById('enterpriseForm').reset();
            document.getElementById('additionalNotes').value = '';
            document.getElementById('search-results').innerHTML = '';
        }

        function validateForm(data) {
            // 必填项验证
            const requiredFields = ['enterpriseName', 'creditCode', 'industry'];
            
            for (let field of requiredFields) {
                if (!data[field] || data[field].trim() === '') {
                    alert(`请填写必填项：${field}`);
                    return false;
                }
            }

            return true;
        }
    </script>
</body>
</html>