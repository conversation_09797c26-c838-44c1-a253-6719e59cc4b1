.analysis-module {
  .resume-analysis-card {
    // background: #fff;
    .candidate-info {
      h3 {
        margin-bottom: 16px;
        font-size: 20px;
        font-weight: 600;
        color: #262626;
      }

      .candidate-tags {
        margin-bottom: 16px;
        margin-left: 12px;
        display: none;
        
        .ant-tag {
          margin-right: 8px;
          margin-bottom: 8px;
          font-weight: 500;
          border-radius: 12px;
          padding: 4px 12px;
          
          &.ant-tag-red {
            background-color: #fff2f0;
            border-color: #ffccc7;
            color: #cf1322;
          }
          
          &.ant-tag-green {
            background-color: #f6ffed;
            border-color: #b7eb8f;
            color: #389e0d;
          }
        }
      }

      .candidate-details {
        color: #666;
        font-size: 14px;
        line-height: 1.6;
        margin: 0;
      }

      .education-details {
        color: #666;
        font-size: 14px;
        line-height: 1.6;
        margin: 8px 0 0 0;
        padding: 8px 12px;
        background-color: #f5f5f5;
        border-radius: 4px;
      }
    }

    .analysis-section {
      margin-bottom: 24px;

      h4 {
        font-size: 16px;
        font-weight: 600;
        color: #262626;
        margin-bottom: 12px;
      }

      .highlights-list,
      .risks-list {
        margin: 0;
        padding-left: 20px;
        
        li {
          color: #666;
          line-height: 1.8;
          margin-bottom: 8px;
          
          &:last-child {
            margin-bottom: 0;
          }
          &::marker {
            color: #3173ff;
          }
        }
      }

      .risks-list {
        .risk-item {
          // color: #ff4d4f;
          // 为风险提示添加橙色圆点
          &::marker {
            color: #ffa727;
          }
        }
      }

      .meta-notes {
        color: #666;
        font-size: 14px;
        line-height: 1.6;
        margin: 0;
        padding: 12px;
        background-color: #f0f8ff;
        border-left: 4px solid #1890ff;
        border-radius: 4px;
      }

      .no-data {
        text-align: center;
        padding: 40px 20px;
        color: #999;
        font-size: 16px;
        
        p {
          margin: 0;
        }
      }
    }
  }
}
