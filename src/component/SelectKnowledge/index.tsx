/** 数据空状态组件 */
import React, { useEffect, useMemo, useRef, useState } from "react";
import {
  Button,
  Empty,
  Flex,
  Input,
  message,
  Spin,
  theme,
  Tooltip,
} from "antd";
import "./index.less";
import KnowledgeCard from "./components/knowledgeCard";
import LearnCard from "./components/learnCard";
import IconFont from "@/component/IconFont";
import { SearchOutlined } from "@ant-design/icons";
import { baseGetList, getItemDetail, recentList } from "@/api/knowledge";
import { debounce } from "@/utils/debounce";

/** 数据空状态组件参数 */
export interface EmptyDataProps {
  selectKnowledgeArr?: [];
  selectKnIdsArr?: [];
  openDrawer?: boolean;
  drawerOpenTime?: string | number;
  onGetKnowledge?: (data: any) => void;
  onCloseFun: () => void;
}
const { useToken } = theme;
const EmptyData: React.FC<EmptyDataProps> = ({
  openDrawer,
  selectKnowledgeArr,
  selectKnIdsArr,
  onGetKnowledge,
  drawerOpenTime,
  onCloseFun,
}) => {
  const { token } = useToken();
  const [searchValue, setSearchValue] = useState<string>(""); // 搜索框值
  const [pageLoading, setPageLoading] = useState<boolean>(false); // 页面整体的loading
  const [searchData, setSearchData] = useState<any>({}); // 搜索下的数据 enterpriseQueryVO 企业库  teamQueryVO 个人库 projectQueryVO 项目库
  const [expandedMap, setExpandedMap] = useState<Record<string, boolean>>({}); // 搜索查看更多
  const [checkedIds, setCheckedIds] = useState<string[]>([]); // 知识库，知识选中的数据id
  const [checkedData, setCheckedData] = useState([]); // 知识库，知识选中的数据
  const [knowledgeList, setKnowledgeList] = useState([]); // 知识数据
  const [currentId, setCurrentId] = useState(""); // 当前选中的知识库id
  const [searchWebValue, setSearchWebValue] = useState<string>(""); // 知识搜索框值
  // 搜索点击查看更多
  const toggleExpand = (key: string) => {
    setExpandedMap((prev) => ({ ...prev, [key]: true }));
  };
  const typeMap = {
    // 搜索key 对应的类型
    enterpriseQueryVO: 3, // 企业
    projectQueryVO: 2, // 项目
    teamQueryVO: 4, // 个人
  };
  const latestRequestIdRef = useRef(false); // 请求标识
  const searchRef = useRef(() => {});
  // debounce 永远不变，内部调用 ref 获取最新逻辑  网页知识库列表接口
  const debounceSearch = useMemo(() => {
    return debounce(() => {
      searchRef.current();
    }, 600);
  }, []);
  // 搜素框change
  const inputChange = (e) => {
    const value = e.target.value.trim();
    setSearchValue(value);
    // 不是输入法状态才搜索
    if (!latestRequestIdRef.current) {
      debounceSearch();
    }
  };

  // 查询最近的数据
  const getRecentList = () => {
    setPageLoading(true);
    recentList({})
      .then((res: any) => {
        if (res.code === 200) {
          setSearchData(res?.data || {});
        } else {
          message.open({
            type: "error",
            content: "获取失败",
          });
        }
        setPageLoading(false);
      })
      .catch(() => {
        message.open({
          type: "error",
          content: "获取失败",
        });
      });
  };
  useEffect(() => {
    if (openDrawer) {
      const mergedArr = [...selectKnowledgeArr, ...selectKnIdsArr];
      // 提取 id 数组（字符串）
      const ids = mergedArr.map((item) => String(item.id));
      // 设置状态
      setCheckedIds(ids);
      setCheckedData(mergedArr);
      getRecentList();
    }
  }, [drawerOpenTime]);
  useEffect(() => {
    searchRef.current = () => {
      setPageLoading(true);
      baseGetList({ libName: searchValue })
        .then((res: any) => {
          if (res.code === 200) {
            setSearchData(res?.data || {});
          } else {
            message.open({
              type: "error",
              content: "获取失败",
            });
          }
          setPageLoading(false);
        })
        .catch(() => {
          message.open({
            type: "error",
            content: "获取失败",
          });
          setPageLoading(false);
        });
    };
  }, [searchValue]);

  const handleCompositionStart = () => {
    latestRequestIdRef.current = true;
  };

  const handleCompositionEnd = (e) => {
    latestRequestIdRef.current = false;
    setSearchValue(e.target.value);
    debounceSearch();
  };

  // 用户选中的数据
  const handleCheckChange = (checked: boolean, data: any, type: string) => {
    setCheckedIds((prev) => {
      if (checked) {
        return [...prev, data?.id];
      } else {
        return prev.filter((itemId) => itemId !== data?.id);
      }
    });
    setCheckedData((prev) => {
      if (checked) {
        const newData = { ...data, isTypeKnow: type }; // 1 是知识库 2是知识
        return [...prev, newData];
      } else {
        return prev.filter((item: any) => item?.id !== data?.id);
      }
    });
  };

  // 搜素框 网页列表change
  const inputWebChange = (e) => {
    const value = e.target.value.trim();
    setSearchWebValue(value);
    // 不是输入法状态才搜索
    if (!latestRequestIdRef.current) {
      debounceKnowSearch();
    }
  };

  const getJumpUrlRef = useRef(() => {});
  const latestUrlRef = useRef(false); // 请求标识
  useEffect(() => {
    setKnowledgeList([]);
    getJumpUrlRef.current = () => {
      setPageLoading(true);
      getItemDetail({
        pageNum: 1,
        pageSize: 300000,
        entity: {
          title: searchWebValue,
          libId: currentId,
        },
      })
        .then((res) => {
          if (res.code === 200) {
            setKnowledgeList(res?.data.records || []);
          } else {
            message.open({
              type: "error",
              content: "获取失败",
            });
          }
          setPageLoading(false);
        })
        .catch(() => {
          message.open({
            type: "error",
            content: "获取失败",
          });
        });
    };
  }, [searchWebValue, currentId]);
  const handleWebStart = () => {
    latestUrlRef.current = true;
  };

  const handleWebEnd = (e) => {
    latestUrlRef.current = false;
    setSearchWebValue(e.target.value);
    debounceSearch();
  };
  // debounce 永远不变，内部调用 ref 获取最新逻辑  网页知识库列表接口
  const debounceKnowSearch = useMemo(() => {
    return debounce(() => {
      getJumpUrlRef.current();
    }, 600);
  }, []);

  // 问答
  const onQue = () => {
    onGetKnowledge(checkedData);
  };
  return (
    <>
      <Spin spinning={pageLoading} size="default">
        <Flex className="knowledge-select-warp">
          {currentId ? (
            <Flex vertical className="knowledge-content-page">
              <Flex className="knowledge-content-input" gap={token.marginSM}>
                <Input
                  prefix={<SearchOutlined />}
                  allowClear
                  placeholder="搜索知识"
                  style={{ width: "100%" }}
                  onChange={inputWebChange}
                  value={searchWebValue}
                  onCompositionStart={handleWebStart}
                  onCompositionEnd={handleWebEnd}
                />
                <Button
                  type="text"
                  onClick={() => {
                    setSearchWebValue("");
                    setCurrentId("");
                  }}
                >
                  取消
                </Button>
              </Flex>
              <Flex className="page-con">
                {knowledgeList && knowledgeList.length > 0 ? (
                  <Flex className="grid-size" wrap="wrap" gap={token.marginSM}>
                    {knowledgeList?.map((item) => (
                      <div key={item.id}>
                        <LearnCard
                          data={item}
                          key={item.id}
                          checked={checkedIds.includes(item.id)}
                          onCheckChange={handleCheckChange}
                        />
                      </div>
                    ))}
                  </Flex>
                ) : (
                  <Empty
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                    description="暂无数据"
                  />
                )}
              </Flex>
            </Flex>
          ) : (
            <>
              <Flex className="know-search-chat" vertical>
                <Flex className="knowledge-content-input" gap={token.marginSM}>
                  <Input
                    prefix={<SearchOutlined />}
                    allowClear
                    placeholder="请输入关键字"
                    style={{ width: "100%" }}
                    onChange={inputChange}
                    value={searchValue}
                    onCompositionStart={handleCompositionStart}
                    onCompositionEnd={handleCompositionEnd}
                  />
                </Flex>
                <Flex className="know-search-con" vertical>
                  {Object.entries(searchData).some(
                    ([, value]) =>
                      (value?.knowledgeLibBaseList?.length || 0) > 0 ||
                      (value?.knowledgeLibDocList?.length || 0) > 0
                  ) ? (
                    Object.entries(searchData).map(([key, value]) => {
                      const baseList = value?.knowledgeLibBaseList || [];
                      const docList = value?.knowledgeLibDocList || [];
                      const totalLength = baseList.length + docList.length;

                      if (totalLength === 0) return null;

                      const isExpanded = expandedMap[key];
                      const maxToShow = 6;

                      const displayBaseList = isExpanded
                        ? baseList
                        : baseList.slice(0, maxToShow);
                      const remainingSlots = maxToShow - displayBaseList.length;
                      const displayDocList = isExpanded
                        ? docList
                        : docList.slice(0, Math.max(remainingSlots, 0));
                      const isSearchType = typeMap[key];
                      return (
                        <div key={key} style={{ marginBottom: token.marginMD }}>
                          <Flex className="con-tit">
                            {key === "enterpriseQueryVO" && <span>企业</span>}
                            {key === "teamQueryVO" && <span>个人</span>}
                            {key === "projectQueryVO" && <span>项目</span>}
                          </Flex>

                          <Flex
                            wrap="wrap"
                            gap={token.marginSM}
                            style={{ marginBottom: token.marginSM }}
                            className="grid-size"
                          >
                            {displayBaseList.map((item) => (
                              <div
                                key={item.id}
                                onClick={() => {
                                  setCurrentId(item.id);
                                  setKnowledgeList([]);
                                  debounceKnowSearch();
                                  setSearchWebValue("");
                                }}
                              >
                                <KnowledgeCard
                                  data={item}
                                  knowledgeType={isSearchType}
                                  checked={checkedIds.includes(item.id)}
                                  onCheckChange={handleCheckChange}
                                />
                              </div>
                            ))}
                          </Flex>
                          <Flex
                            wrap="wrap"
                            gap={token.marginSM}
                            style={{ marginBottom: token.marginSM }}
                            className="grid-size"
                          >
                            {displayDocList.map((item) => (
                              <div key={item.id}>
                                <LearnCard
                                  key={item.id}
                                  data={item}
                                  onCheckChange={handleCheckChange}
                                  knowledgeType={isSearchType}
                                  checked={checkedIds.includes(item.id)}
                                />
                              </div>
                            ))}
                          </Flex>
                          {!isExpanded && totalLength > maxToShow && (
                            <span
                              style={{
                                cursor: "pointer",
                                fontSize: token.fontSizeSM,
                                color: token.colorInfoText,
                                marginTop: token.marginXS,
                                display: "inline-block",
                              }}
                              onClick={() => toggleExpand(key)}
                            >
                              查看全部（{totalLength}）
                            </span>
                          )}
                        </div>
                      );
                    })
                  ) : (
                    <Empty
                      image={Empty.PRESENTED_IMAGE_SIMPLE}
                      description="暂无数据"
                    />
                  )}
                </Flex>
              </Flex>
            </>
          )}
          {/* 知识库底部操作栏 */}
          {checkedIds && checkedIds.length > 0 && (
            <Flex className="fixed-bottom-chat" gap={token.marginXS}>
              <Flex className="fixed-bottom-operate" vertical align="center">
                <span>{checkedIds?.length || 0}</span>
                <span style={{ marginTop: "7px" }}>已选</span>
              </Flex>
              <div className="fixed-bottom-line"></div>
              <Flex
                className="fixed-bottom-operate operate-hover"
                vertical
                align="center"
                onClick={onQue}
              >
                <span>
                  <IconFont
                    type="AIChatOutlined"
                    isGradien
                    width={16}
                    height={16}
                  />
                </span>
                <span>问答</span>
              </Flex>
              <div className="fixed-bottom-line"></div>
              <Flex
                className="fixed-bottom-close fixed-bottom-operate operate-hover"
                align="center"
              >
                <Tooltip placement="top" title="点击将清空选中项">
                  <span
                    onClick={() => {
                      setCheckedIds([]);
                      setCheckedData([]); // 清空选中的数据
                      onCloseFun();
                    }}
                  >
                    清空
                  </span>
                </Tooltip>
              </Flex>
            </Flex>
          )}
        </Flex>
      </Spin>
    </>
  );
};

export default EmptyData;
