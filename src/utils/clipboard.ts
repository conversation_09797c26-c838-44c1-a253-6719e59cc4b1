/** 剪贴板操作工具类 */

/**
 * 复制文本内容到操作系统剪贴板
 * @param {string} data - 文本内容
 * @return Promise<boolean> - 是否复制成功
 */
export async function copyText(data: string): Promise<boolean> {
  // 首先尝试使用现代的 Clipboard API
  if (navigator.clipboard && navigator.clipboard.writeText) {
    try {
      await navigator.clipboard.writeText(data)
      return true
    } catch (err) {
      // 如果 Clipboard API 失败，尝试使用 execCommand 方法
      return fallbackCopyText(data)
    }
  } else {
    // 如果 Clipboard API 不可用，使用 execCommand 方法
    return fallbackCopyText(data)
  }
}

/**
 * 使用 document.execCommand('copy') 复制文本的后备方法
 * 适用于非安全上下文（HTTP）
 * @param {string} text - 要复制的文本
 * @return {boolean} - 是否复制成功
 */
function fallbackCopyText(text: string): boolean {
  try {
    // 创建一个临时的文本区域元素
    const textArea = document.createElement('textarea')
    textArea.value = text

    // 设置样式使其不可见
    textArea.style.position = 'fixed'
    textArea.style.top = '0'
    textArea.style.left = '0'
    textArea.style.width = '2em'
    textArea.style.height = '2em'
    textArea.style.padding = '0'
    textArea.style.border = 'none'
    textArea.style.outline = 'none'
    textArea.style.boxShadow = 'none'
    textArea.style.background = 'transparent'
    textArea.style.opacity = '0'

    document.body.appendChild(textArea)
    textArea.focus()
    textArea.select()

    // 执行复制命令
    const successful = document.execCommand('copy')

    // 清理
    document.body.removeChild(textArea)

    return successful
  } catch (err) {
    console.error('Fallback copy method failed:', err)
    return false
  }
}
