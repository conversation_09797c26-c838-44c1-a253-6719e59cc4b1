<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>公文助手</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
      .tab-transition {
        transition: all 0.3s ease-in-out;
      }
      h1{
        color: rgb(28, 98, 212);
        padding: 15px;
        text-align: center;
      }
    </style>
  </head>

  <body>
    <div id="app" class="container mx-auto p-4 max-w-4xl">
      <h1 class="text-3xl font-bold mb-4">公文助手</h1>
      <!-- Tab导航 -->
      <div
        id="tab-nav"
        class="flex mb-4 bg-gray-100 rounded-full p-1 shadow-inner"
      >
        <button
          data-tab="通告"
          class="tab-btn flex-1 px-4 py-2 rounded-full flex items-center justify-center space-x-2 text-gray-600 hover:bg-gray-200 hover:text-gray-800"
        >
          <!-- <span>📢</span> -->
          <span class="font-medium">通告</span>
        </button>
        <button
          data-tab="函件"
          class="tab-btn flex-1 px-4 py-2 rounded-full flex items-center justify-center space-x-2 text-gray-600 hover:bg-gray-200 hover:text-gray-800"
        >
          <!-- <span>📝</span> -->
          <span class="font-medium">函件</span>
        </button>
        <button
          data-tab="演讲稿"
          class="tab-btn flex-1 px-4 py-2 rounded-full flex items-center justify-center space-x-2 text-gray-600 hover:bg-gray-200 hover:text-gray-800"
        >
          <!-- <span>🎤</span> -->
          <span class="font-medium">演讲稿</span>
        </button>
        <button
          data-tab="邮件"
          class="tab-btn flex-1 px-4 py-2 rounded-full flex items-center justify-center space-x-2 text-gray-600 hover:bg-gray-200 hover:text-gray-800"
        >
          <!-- <span>✉️</span> -->
          <span class="font-medium">邮件</span>
        </button>
      </div>

      <!-- 通知公告页 -->
      <div
        id="notice-section"
        class="bg-white border border-gray-200 p-6 rounded-lg shadow-sm"
      >
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-xl font-bold text-gray-800 flex items-center">
            <!-- <span class="mr-2">📢</span> -->
            通知公告
          </h2>
          <button
            id="expand-btn"
            class="text-gray-600 hover:text-gray-800 bg-gray-100 rounded-full p-2"
          >
            收起 ▲
          </button>
        </div>

        <div id="form-section" class="space-y-4">
          <div>
            <label class="block text-gray-700 mb-2 font-medium">通知内容</label>
            <textarea
              id="content-input"
              placeholder="请输入通知内容"
              class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-200 transition"
              rows="4"
            ></textarea>
          </div>
          <div>
            <label class="block text-gray-700 mb-2 font-medium">主送机关</label>
            <input
              id="recipient-input"
              type="text"
              placeholder="请输入主送机关"
              class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-200 transition"
            />
          </div>
          <div>
            <label class="block text-gray-700 mb-2 font-medium">发文机关</label>
            <input
              id="issuer-input"
              type="text"
              placeholder="请输入发文机关"
              class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-200 transition"
            />
          </div>
          <div>
            <label class="block text-gray-700 mb-2 font-medium">结束语</label>
            <input
              id="closing-input"
              type="text"
              placeholder="请输入结束语"
              class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-200 transition"
            />
          </div>
          <div class="text-center">
            <button
              id="generate-btn"
              class="bg-blue-500 text-white px-8 py-3 rounded-full hover:bg-blue-600 transition transform hover:scale-105 shadow-md"
            >
              生成全文
            </button>
            <label class="inline-flex items-center mt-3">
              <input
                type="checkbox"
                id="web-search-checkbox"
                class="form-checkbox h-5 w-5 text-blue-600 hidden"
              />
              <span class="ml-2 text-gray-700">联网搜索</span>
              <span class="relative ml-2">
                <span
                  class="block w-10 h-6 bg-gray-300 rounded-full shadow-inner"
                ></span>
                <span
                  class="dot absolute left-1 top-1 w-4 h-4 bg-white rounded-full transition transform"
                ></span>
              </span>
            </label>
            <style>
              #web-search-checkbox:checked + span + span .dot {
                transform: translateX(100%);
                background-color: #4caf50;
              }
            </style>
          </div>
        </div>
      </div>

      <!-- 函件页 -->
      <div
        id="letter-section"
        class="bg-white border border-gray-200 p-6 rounded-lg shadow-sm hidden"
      >
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-xl font-bold text-gray-800 flex items-center">
            <!-- <span class="mr-2">📝</span> -->
            函件
          </h2>
          <button
            id="expand-letter-btn"
            class="text-gray-600 hover:text-gray-800 bg-gray-100 rounded-full p-2"
          >
            收起 ▲
          </button>
        </div>

        <div id="letter-form-section" class="space-y-4">
          <div>
            <label class="block text-gray-700 mb-2 font-medium">函件目的</label>
            <input
              id="letter-purpose-input"
              type="text"
              placeholder="请输入函件目的"
              class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-200 transition"
            />
          </div>
          <div>
            <label class="block text-gray-700 mb-2 font-medium">收件人</label>
            <input
              id="letter-recipient-input"
              type="text"
              placeholder="请输入收件人"
              class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-200 transition"
            />
          </div>
          <div>
            <label class="block text-gray-700 mb-2 font-medium">函件主题</label>
            <input
              id="letter-subject-input"
              type="text"
              placeholder="请输入函件主题"
              class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-200 transition"
            />
          </div>
          <div>
            <label class="block text-gray-700 mb-2 font-medium">函件内容</label>
            <textarea
              id="letter-content-input"
              placeholder="请输入函件内容"
              class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-200 transition"
              rows="4"
            ></textarea>
          </div>
          <div class="text-center">
            <button
              id="generate-letter-btn"
              class="bg-blue-500 text-white px-8 py-3 rounded-full hover:bg-blue-600 transition transform hover:scale-105 shadow-md"
            >
              生成函件
            </button>
          </div>
        </div>
      </div>

      <!-- 邮件页 -->
      <div
        id="email-section"
        class="bg-white border border-gray-200 p-6 rounded-lg shadow-sm hidden"
      >
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-xl font-bold text-gray-800 flex items-center">
            <!-- <span class="mr-2">✉️</span> -->
            邮件
          </h2>
          <button
          id="email-expand-btn"
          class="text-gray-600 hover:text-gray-800 bg-gray-100 rounded-full p-2"
        >
          收起 ▲
        </button>
        </div>

        <div id="email-form-section" class="space-y-4">
          <div>
            <label class="block text-gray-700 mb-2 font-medium">邮件目的</label>
            <input
              id="email-purpose-input"
              type="text"
              placeholder="请输入邮件目的"
              class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-200 transition"
            />
          </div>
          <div>
            <label class="block text-gray-700 mb-2 font-medium"
              >收件人信息</label
            >
            <input
              id="email-recipient-input"
              type="text"
              placeholder="请输入收件人信息"
              class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-200 transition"
            />
          </div>
          <div>
            <label class="block text-gray-700 mb-2 font-medium">邮件主题</label>
            <input
              id="email-subject-input"
              type="text"
              placeholder="请输入邮件主题"
              class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-200 transition"
            />
          </div>
          <div>
            <label class="block text-gray-700 mb-2 font-medium">邮件内容</label>
            <textarea
              id="email-content-input"
              placeholder="请输入邮件内容"
              class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-200 transition"
              rows="4"
            ></textarea>
          </div>
          <div class="text-center">
            <button
              id="generate-email-btn"
              class="bg-blue-500 text-white px-8 py-3 rounded-full hover:bg-blue-600 transition transform hover:scale-105 shadow-md"
            >
              生成邮件
            </button>
          </div>
        </div>
      </div>

      <!-- 演讲稿页 -->
      <div
        id="speech-section"
        class="bg-white border border-gray-200 p-6 rounded-lg shadow-sm hidden"
      >
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-xl font-bold text-gray-800 flex items-center">
            <!-- <span class="mr-2">🎤</span> -->
            演讲稿
          </h2>
          <button
          id="speech-expand-btn"
          class="text-gray-600 hover:text-gray-800 bg-gray-100 rounded-full p-2"
        >
          收起 ▲
        </button>
        </div>

        <div id="speech-form-section" class="space-y-4">
          <div>
            <label class="block text-gray-700 mb-2 font-medium"
              >演讲稿标题</label
            >
            <input
              id="speech-title-input"
              type="text"
              placeholder="请输入演讲稿标题"
              class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-200 transition"
            />
          </div>
          <div>
            <label class="block text-gray-700 mb-2 font-medium">发言场合</label>
            <input
              id="speech-occasion-input"
              type="text"
              placeholder="请输入发言场合"
              class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-200 transition"
            />
          </div>
          <div>
            <label class="block text-gray-700 mb-2 font-medium">发言内容</label>
            <textarea
              id="speech-content-input"
              placeholder="请输入发言内容"
              class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-200 transition"
              rows="4"
            ></textarea>
          </div>
          <div>
            <label class="block text-gray-700 mb-2 font-medium">发言身份</label>
            <input
              id="speech-identity-input"
              type="text"
              placeholder="请输入发言身份"
              class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-200 transition"
            />
          </div>
          <div>
            <label class="block text-gray-700 mb-2 font-medium">听众角色</label>
            <input
              id="speech-audience-input"
              type="text"
              placeholder="请输入听众角色"
              class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-200 transition"
            />
          </div>
          <div>
            <label class="block text-gray-700 mb-2 font-medium">发言背景</label>
            <textarea
              id="speech-background-input"
              placeholder="请输入发言背景"
              class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-200 transition"
              rows="4"
            ></textarea>
          </div>
          <div class="text-center">
            <button
              id="generate-speech-btn"
              class="bg-blue-500 text-white px-8 py-3 rounded-full hover:bg-blue-600 transition transform hover:scale-105 shadow-md"
            >
              生成演讲稿
            </button>
          </div>
        </div>
      </div>
      <!-- 联网搜索内容 -->
      <div
        id="search-results-section"
        class="mt-6 bg-white p-6 border border-gray-200 rounded-lg shadow-sm hidden"
      >
        <h3 class="text-lg font-bold mb-4 text-gray-800">联网搜索内容</h3>
        <div id="search-results" class="space-y-4">
          <!-- 搜索结果将显示在这里 -->
          暂无数据
        </div>
      </div>
      <!-- 文章生成页面 -->
      <div
        id="document-section"
        class="mt-6 bg-white p-6 border border-gray-200 rounded-lg shadow-sm"
      >
        <h3 class="text-lg font-bold mb-4 text-gray-800">生成的文档</h3>
        <pre
          id="resultContainer"
          class="bg-gray-50 p-4 rounded-lg whitespace-pre-wrap border border-gray-200"
        >
暂无内容</pre
        >
        <!-- <div  class="mt-4"></div> -->
        <div class="flex justify-center space-x-4 mt-4">
          <button
            id="copy-btn"
            class="bg-green-500 text-white px-6 py-3 rounded-full hover:bg-green-600 transition transform hover:scale-105 shadow-md flex items-center space-x-2"
          >
            <!-- <span>📋</span> -->
            <span>复制文章</span>
          </button>
          <button
            id="download-btn"
            class="bg-blue-500 text-white px-6 py-3 rounded-full hover:bg-blue-600 transition transform hover:scale-105 shadow-md flex items-center space-x-2"
          >
            <!-- <span>📥</span> -->
            <span>下载Word</span>
          </button>
        </div>
      </div>
    </div>

    <script>
      document.addEventListener("DOMContentLoaded", () => {
        // 选择器
        const tabBtns = document.querySelectorAll(".tab-btn");
        const expandBtn = document.getElementById("expand-btn");
        const  emailExpandBtn = document.getElementById("email-expand-btn");
        const  speechExpandBtn = document.getElementById("speech-expand-btn");
        const formSection = document.getElementById("form-section");
        const generateBtn = document.getElementById("generate-btn");
        const documentSection = document.getElementById("document-section");
        const generatedDocument = document.getElementById("resultContainer");
        const copyBtn = document.getElementById("copy-btn");
        const downloadBtn = document.getElementById("download-btn");
      
        // 输入框
        const contentInput = document.getElementById("content-input");
        const recipientInput = document.getElementById("recipient-input");
        const issuerInput = document.getElementById("issuer-input");
        const closingInput = document.getElementById("closing-input");

        // 新增的选择器
        const letterSection = document.getElementById("letter-section");
        const generateLetterBtn = document.getElementById(
          "generate-letter-btn"
        );
        const letterPurposeInput = document.getElementById(
          "letter-purpose-input"
        );
        const letterRecipientInput = document.getElementById(
          "letter-recipient-input"
        );
        const letterSubjectInput = document.getElementById(
          "letter-subject-input"
        );
        const letterContentInput = document.getElementById(
          "letter-content-input"
        );
        const noticeSection = document.getElementById("notice-section");
        const expandLetterBtn = document.getElementById("expand-letter-btn");
        const letterFormSection = document.getElementById(
          "letter-form-section"
        );
        const emailSection = document.getElementById("email-section");
        const generateEmailBtn = document.getElementById("generate-email-btn");
        const emailPurposeInput = document.getElementById(
          "email-purpose-input"
        );
        const emailRecipientInput = document.getElementById(
          "email-recipient-input"
        );
        const emailSubjectInput = document.getElementById(
          "email-subject-input"
        );
        const emailContentInput = document.getElementById(
          "email-content-input"
        );
        const speechSection = document.getElementById("speech-section");
        const generateSpeechBtn = document.getElementById(
          "generate-speech-btn"
        );
        const speechTitleInput = document.getElementById("speech-title-input");
        const speechOccasionInput = document.getElementById(
          "speech-occasion-input"
        );
        const speechContentInput = document.getElementById(
          "speech-content-input"
        );
        const speechIdentityInput = document.getElementById(
          "speech-identity-input"
        );
        const speechAudienceInput = document.getElementById(
          "speech-audience-input"
        );
        const speechBackgroundInput = document.getElementById(
          "speech-background-input"
        );

        const speechFormSection = document.getElementById(
          "speech-form-section"
        );
        const emailFormSection = document.getElementById("email-form-section");
        // Tab切换
        tabBtns.forEach((btn) => {
          btn.addEventListener("click", () => {
            // 移除所有按钮的激活状态
            tabBtns.forEach((b) => {
              b.classList.remove(
                "bg-blue-500",
                "text-white",
                "shadow-lg",
                "scale-105"
              );
              b.classList.add(
                "text-gray-600",
                "hover:bg-gray-200",
                "hover:text-gray-800"
              );
            });

            // 为当前按钮添加激活状态
            btn.classList.remove(
              "text-gray-600",
              "hover:bg-gray-200",
              "hover:text-gray-800"
            );
            btn.classList.add(
              "bg-blue-500",
              "text-white",
              "shadow-lg",
              "scale-105"
            );

            // 切换到相应的页面
            if (btn.dataset.tab === "通告") {
              noticeSection.classList.remove("hidden");
              letterSection.classList.add("hidden");
              emailSection.classList.add("hidden");
              speechSection.classList.add("hidden");
            } else if (btn.dataset.tab === "函件") {
              noticeSection.classList.add("hidden");
              letterSection.classList.remove("hidden");
              emailSection.classList.add("hidden");
              speechSection.classList.add("hidden");
            } else if (btn.dataset.tab === "邮件") {
              noticeSection.classList.add("hidden");
              letterSection.classList.add("hidden");
              emailSection.classList.remove("hidden");
              speechSection.classList.add("hidden");
            } else if (btn.dataset.tab === "演讲稿") {
              noticeSection.classList.add("hidden");
              letterSection.classList.add("hidden");
              emailSection.classList.add("hidden");
              speechSection.classList.remove("hidden");
            } else {
              noticeSection.classList.add("hidden");
              letterSection.classList.add("hidden");
              emailSection.classList.add("hidden");
              speechSection.classList.add("hidden");
            }
          });
        });

        // 初始化时设置“通告”按钮为选中状态
        const initialTab = tabBtns[0];
        initialTab.classList.add("bg-blue-500", "text-white", "shadow-lg", "scale-105");
        noticeSection.classList.remove("hidden");
        letterSection.classList.add("hidden");
        emailSection.classList.add("hidden");
        speechSection.classList.add("hidden");

        // 展开/收起 通知公告
        expandBtn.addEventListener("click", () => {
          formSection.classList.toggle("hidden");
          expandBtn.textContent = formSection.classList.contains("hidden")
            ? "展开 ▼"
            : "收起 ▲";
        });

        // 展开/收起函件表单
        expandLetterBtn.addEventListener("click", () => {
          letterFormSection.classList.toggle("hidden");
          expandLetterBtn.textContent = letterFormSection.classList.contains(
            "hidden"
          )
            ? "展开 ▼"
            : "收起 ▲";
        });

          // 展开/收起 邮件
          emailExpandBtn.addEventListener("click", () => {
          emailFormSection.classList.toggle("hidden");
          emailExpandBtn.textContent = emailFormSection.classList.contains("hidden")
            ? "展开 ▼"
            : "收起 ▲";
        });
        // 展开/收起 演讲稿
         speechExpandBtn.addEventListener("click", () => {
            speechFormSection.classList.toggle("hidden");
          speechExpandBtn.textContent = speechFormSection.classList.contains("hidden")
            ? "展开 ▼"
            : "收起 ▲";
        });

        // 生成通知
        generateBtn.addEventListener("click", async () => {
          const query = `
通知内容：${contentInput.value}

主送机关：${recipientInput.value}
发文机关：${issuerInput.value}

${closingInput.value}
`;
          // 收起表单
          formSection.classList.add("hidden");
          expandBtn.textContent = "展开 ▼";

          if (document.getElementById("web-search-checkbox").checked) {
            document
              .getElementById("search-results-section")
              .classList.remove("hidden");
            performWebSearch(
              "app-AtvXJAaRQtSq6qx73xkTUsdq",
              query,
              "联网查询"
            );
          }
          callChatMessagesAPI(
            "app-AtvXJAaRQtSq6qx73xkTUsdq",
            query,
            "通告"
          );
        });

        // 生成函件
        generateLetterBtn.addEventListener("click", async () => {
          const letter = `
函件目的：${letterPurposeInput.value}

收件人：${letterRecipientInput.value}
函件主题：${letterSubjectInput.value}

函件内容：
${letterContentInput.value}
`;

          // 调用API
          await callChatMessagesAPI(
            "app-AtvXJAaRQtSq6qx73xkTUsdq",
            letter,
            "函件"
          );
        });

        // 生成邮件
        generateEmailBtn.addEventListener("click", async () => {
          const email = `
邮件目的：${emailPurposeInput.value}

收件人信息：${emailRecipientInput.value}
邮件主题：${emailSubjectInput.value}

邮件内容：
${emailContentInput.value}
`;

          // 调用API
          await callChatMessagesAPI(
            "app-AtvXJAaRQtSq6qx73xkTUsdq",
            email,
            "邮件"
          );
        });

        // 生成演讲稿
        generateSpeechBtn.addEventListener("click", async () => {
          const speech = `
演讲稿标题：${speechTitleInput.value}

发言场合：${speechOccasionInput.value}
发言内容：${speechContentInput.value}
发言身份：${speechIdentityInput.value}
听众角色：${speechAudienceInput.value}
发言背景：${speechBackgroundInput.value}
`;

          await callChatMessagesAPI(
            "app-AtvXJAaRQtSq6qx73xkTUsdq",
            speech,
            "演讲稿"
          );
        });

        // 复制文档
        copyBtn.addEventListener("click", () => {
          navigator.clipboard.writeText(generatedDocument.textContent);
          alert("文章已复制到剪贴板");
        });

        // 下载文档
        downloadBtn.addEventListener("click", () => {
          const blob = new Blob([generatedDocument.textContent], {
            type: "text/plain;charset=utf-8",
          });
          const link = document.createElement("a");
          link.href = URL.createObjectURL(blob);
          link.download = "公文.docx";
          link.click();
        });
      });

    //流式输出
    async function callChatMessagesAPI(apikey, query, typeId) {
      const resultContainer = document.getElementById("resultContainer");
      // 重置容器
      resultContainer.innerHTML = "正在加载...";

      try {
        const response = await fetch(
        "https://copilot.sino-bridge.com:90/v1/chat-messages",
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${apikey}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            inputs: { type: typeId.toString() },
            query: query,
            response_mode: "streaming", // 流式模式
            conversation_id: "",
            user: "web-user-" + Math.random().toString(36).substr(2, 9),
            files: [],
          }),
        }
        );

        if (!response.ok) {
        // 尝试解析错误响应体
        const errorText = await response.text();
        throw new Error(`HTTP错误 ${response.status}: ${errorText}`);
        }

        // 处理流式响应
        const reader = response.body.getReader();
        const decoder = new TextDecoder("utf-8");
        let resultText = "";

        while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        const chunk = decoder.decode(value, { stream: true });
        const lines = chunk.split("\n");
        for (const line of lines) {
          if (line.trim()) {
            try {
            if (line.startsWith("data: ")) {
              const jsonString = line.substring(6).trim();
              if (jsonString) {
                try {
                  const data = JSON.parse(
                    jsonString
                      .replace(/\\n/g, "\\n")
                      .replace(/\\'/g, "\\'")
                      .replace(/\\"/g, '\\"')
                      .replace(/\\&/g, "\\&")
                      .replace(/\\r/g, "\\r")
                      .replace(/\\t/g, "\\t")
                      .replace(/\\b/g, "\\b")
                      .replace(/\\f/g, "\\f")
                  );
                  if (data.event === "message") {
                    // const thinkIndex = data.answer.indexOf("</think>");

                    // if (thinkIndex !== -1) {
                    //   resultText += `<span style="font-size: 10px; color: gray;">${data.answer.substring(0, thinkIndex + 7)}</span>`;
                    // //   resultText += `<span style="font-size: 16px; color: black;">${data.answer.substring(thinkIndex + 7)}</span>`;
                    // } else {
                    //   resultText += `<span style="font-size: 16px; color: black;">${data.answer}</span>`;
                    // }
                    // resultContainer.innerHTML = `<pre class="whitespace-pre-wrap break-words">${resultText}</pre>`;
                    resultText += data.answer;
                    resultContainer.innerHTML = `<pre class="whitespace-pre-wrap break-words">${resultText}</pre>`;
                    await new Promise((resolve) => setTimeout(resolve, 50)); // 控制输出速度
                  }
                } catch (e) {
                  // console.error('JSON解析错误', e);
                  // console.error('错误的JSON字符串:', jsonString);
                }
              }
            }
            } catch (e) {
            // console.error('JSON解析错误', e);
            }
          }
        }
        }

        resultText += decoder.decode();
        resultContainer.innerHTML = `<pre class="whitespace-pre-wrap break-words">${resultText}</pre>`;
      } catch (error) {
        // 详细的错误处理
        console.error("查询错误", error);
        resultContainer.innerHTML = "查询错误，请稍后重试。";
      }
    }

      function convertToJSON(data) {
        // 将字符串转换为数组，去掉多余的引号并按逗号分割
        const items = data
          .split(/",\s*"/)
          .map((item) => item.replace(/(^"|"$)/g, ""));

        // 创建一个空数组用于存储结果
        const result = [];

        // 遍历数组，每三项作为一组
        for (let i = 0; i < items.length; i += 3) {
          // 取出当前组的三项
          const group = items.slice(i, i + 3);

          // 如果当前组的长度小于3，跳过
          if (group.length < 3) continue;

          // 将当前组转换为对象并添加到结果数组
          result.push({
            url: group[0],
            title: group[1],
            description: group[2],
          });
        }

        return result;
      }

      //阻塞模式
      async function performWebSearch(apikey, query, typeId) {
        const searchResults = document.getElementById("search-results");
        searchResults.innerHTML = "正在搜索, 请耐心等候...";
        try {
          const response = await fetch(
            "https://copilot.sino-bridge.com:90/v1/chat-messages",
            {
              method: "POST",
              headers: {
                Authorization: `Bearer ${apikey}`,
                "Content-Type": "application/json",
              },

              body: JSON.stringify({
                inputs: { type: typeId.toString() },
                query: query,
                response_mode: "blocking", // 阻塞模式
                conversation_id: "",
                user: "web-user-" + Math.random().toString(36).substr(2, 9),
                files: [],
              }),
            }
          );

          if (!response.ok) {
            // 尝试解析错误响应体
            const errorText = await response.text();
            throw new Error(`HTTP错误 ${response.status}: ${errorText}`);
          }

          // 解析响应
          const data = await response.json();

          const answer = convertToJSON(data.answer);

          // 显示结果  //   <p class="text-gray-700 mt-2">${item.description}</p>

          searchResults.innerHTML = answer
            .map(
              (item) => `
                <div class="border-b border-gray-200 pb-4 mb-4">
                  <a href="${item.url}" target="_blank" class="text-blue-500 hover:underline">${item.title}</a>
              
                </div>
              `
            )
            .join("");
        } catch (error) {
          // 详细的错误处理
          console.error("查询错误", error);
        }
      }
    </script>
  </body>
</html>
