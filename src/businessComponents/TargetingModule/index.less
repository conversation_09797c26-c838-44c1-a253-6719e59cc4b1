.targeting{
  .targeting-file-info{
    position:relative;
    .targeting-file-list{
     
      background: var(--ant-color-bg-container);
      padding: 0px 40px 0px 50px;
      height: 65px;
      .ant-tabs-nav{
        width:calc(100% - 250px);
        height: 55px;
        margin-bottom: 18px;
        &::before{
          border-bottom:0px;
        }
      }
      .ant-tabs-tab-btn{
        font-size: var(--ant-font-size-lg);
        font-weight: bold;
        color:#333;
      }
    }
    .targeting-file-mb{
      width: calc(100% - 120px);
      overflow-x: auto;
    }
    .targeting-file-type{
      background: var(--ant-color-bg-container);
      position:absolute;
      right: 40px;
      top: 0px;
      height: 65px;
    }
  }
  .targeting-file-view{
    background: var(--ant-color-bg-container);
    height: 65px;
    .ant-tabs-nav{
      height: 55px;
      &::before{
        border-bottom:0px;
      }
    }
    .ant-tabs-tab-btn{
      font-size: var(--ant-font-size-lg);
      font-weight: bold;
      color:#333;
    }
  }
  .targeting-right{
    padding:0px 30px;
    background: var(--ant-color-bg-container);
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.02),0px 1px 6px -1px rgba(0, 0, 0, 0.02),0px 1px 2px 0px rgba(0, 0, 0, 0.03);
    .targeting-right-header{
      padding-top: var(--ant-margin-md);
      padding-bottom: var(--ant-margin-sm);
      border-bottom: 1px solid rgba(0,0,0,0.1);
      .ant-result{
        padding: 0px !important;
      }
      .ant-result-icon{
        margin-bottom: 0px;
        margin-right: 4px;
        align-items: center;
        margin-top:-3px;
        svg{
          width:22px;
          height:22px;
        }
      }
      .ant-result-title{
        display:none;
      }
    }
    .targeting-list-scroll{
      height: calc(100vh - 323px);
      overflow-y: auto;
    }
  }
  .split-card{
    padding: 10px 20px 20px;
    height: 190px;
    margin-bottom: 10px;
    .split-card-detail{
      padding: 0px;
    }
    .ant-card-head{
      padding: 0px;
      min-height: 42px !important;
      font-size: var(--ant-font-size-lg);
      font-weight: bold;
      line-height: 22px;
      color: #333333;
      border-bottom:0px;
    }
    .ant-card-body{
      padding: 0px;
      margin-top:var(--ant-margin-xs);
      font-size: var(--ant-font-size);
      line-height: var(--ant-line-height);
      color: #333333;
    }
    &:hover{
      border: 1px solid var(--ant-color-primary);
    }
  }
}
.targent-split-file-modal{
  .split-card{
    padding: 10px 20px 20px;
    height: 190px;
    .split-card-detail{
      padding: 0px;
    }
    .ant-card-head{
      padding: 0px;
      min-height: 42px !important;
      font-size: var(--ant-font-size-lg);
      font-weight: bold;
      line-height: 22px;
      color: #333333;
      border-bottom:0px;
    }
    .ant-card-body{
      padding: 0px;
      margin-top:var(--ant-margin-xs);
      font-size: var(--ant-font-size);
      line-height: var(--ant-line-height);
      color: #333333;
    }
    &:hover{
      border: 1px solid var(--ant-color-primary);
    }
  }
  .active-card{
    background: rgba(0, 0, 0, 0.02);
    &:hover{
      border-color: var(--ant-color-border-secondary);
    }
  }
}
.targenting-split-file-detail{
  .ant-modal-body{
    max-height: 70vh;
    min-height: 500px;
    overflow-y: auto
  }
}

.my-split-docx-preview-wrapper{
  background: var(--ant-color-bg-container) !important;
  height: calc(100vh - 215px);
  padding: 20px !important;
  box-shadow: 0 0 5px 2px rgba(0, 0, 0, 0.06);
  border-radius: 6px;
  margin: 5px;
  .docx-wrapper>section.docx{
    padding: 10px 20px;
  }
  .my-split-docx-preview{
    width: 100% !important;
    margin-bottom:0px !important;
    padding: 0px !important;
    height: calc(100vh - 215px);
    overflow: auto;
    box-shadow: none !important;
    min-height:0px !important;
  }
}