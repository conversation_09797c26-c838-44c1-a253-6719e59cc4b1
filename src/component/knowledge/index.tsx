import React, { useRef, useState } from 'react'
import { Radio, Flex, Input, Modal, Spin } from 'antd'
import { DatabaseTwoTone, SearchOutlined } from '@ant-design/icons'
import './index.less'

interface CardData {
  id: number | string
  libName: string
  libDesc: string
  checked: boolean
}
interface Props {
  cardData: CardData[]
  knowledModel: boolean
  knowledLoading?: boolean;
  closeKnowledModel: (type: boolean, id: string) => void
  keywordSearch: (keyword: string) => void
  onCheckboxChange: (id: string, checked: boolean) => void
  closeKnowledModelFalg: (type: boolean) => void
}

const Knowledge: React.FC<Props> = ({
  cardData,
  knowledModel,
  closeKnowledModel,
  keywordSearch,
  onCheckboxChange,
  closeKnowledModelFalg
}) => {
  const [selectedKnowledgeId, setSelectedKnowledgeId] = useState<string | number | null>(
    cardData.find(item => item.checked)?.id || null
  )
  const inputRef = useRef(null)

  const handlerSubmit = () => {
    closeKnowledModel(false, selectedKnowledgeId?.toString() || '')
  }
  const closeMoal = () => {
    closeKnowledModelFalg(false)
  }
  const onChange = (item: CardData) => {
    setSelectedKnowledgeId(item.id)
    onCheckboxChange(item.id.toString(), true)
  }
  const inputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    keywordSearch(e.target.value.trim())
  }

  return (
    <div className='char-knowledge-content'>
      {/* 知识库详情 */}
      <div className='content-box'>
        {/* 文件知识库 */}
        <Modal
          title='选择知识库'
          centered
          mask={false}
          width={400}
          open={knowledModel}
          onOk={handlerSubmit}
          onCancel={closeMoal}
          okText='确认'
          cancelText='取消'
        >
          <>
            <Flex className='chat-search-input'>
              <Input
                ref={inputRef}
                onChange={inputChange}
                allowClear
                prefix={<SearchOutlined />}
                placeholder='搜索知识库'
                style={{ width: '100%' }}
              />
            </Flex>
            <Spin spinning={false} size='default'>
              <Flex className='modal-know-wcl custom-scrollbar' vertical>
                {cardData &&
                  cardData.map((item, index) => {
                    return (
                      <Flex key={index} className={`cardBox-wcl ${item.id === selectedKnowledgeId ? 'active' : ''}`}>
                        <Radio checked={item.id === selectedKnowledgeId} onChange={() => onChange(item)}>
                          <Flex className='top' justify='center' align='center'>
                            <Flex className='icon-card'>
                              <DatabaseTwoTone />
                            </Flex>
                            <Flex className='left-gas' vertical>
                              <Flex>
                                <Flex className='first-title'>{item.libName}</Flex>
                              </Flex>
                              <Flex className='two-title'>{item.libDesc}</Flex>
                            </Flex>
                          </Flex>
                        </Radio>
                      </Flex>
                    )
                  })}
              </Flex>
            </Spin>
          </>
        </Modal>
      </div>
    </div>
  )
}

export default React.memo(Knowledge)
