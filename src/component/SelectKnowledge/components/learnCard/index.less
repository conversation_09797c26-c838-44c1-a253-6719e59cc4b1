.learn-card-chat {
  background: var(--ant-color-fill-tertiary);
  padding: var(--ant-padding-sm) var(--ant-padding-xs) var(--ant-padding-sm) var(--ant-padding-sm);
  border: 1px solid var(--ant-color-border-secondary);
  border-radius: var(--ant-border-radius);
  flex: 1;
  cursor: pointer;
  .extend-icon {
    width: 24px;
    height: 24px;
  }
  .knowledge-name{
    font-weight: bold;
    font-size: var(--ant-font-size);
    line-height: 24px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    word-break: break-all;
    -webkit-box-orient: vertical;
  }
  .knowledge-checkbox{
    opacity: 0
  }
  &:hover{
    border: 1px solid var(--ant-color-primary-border-hover);
    .knowledge-checkbox{
      opacity: 1 !important;
    }
  }
}