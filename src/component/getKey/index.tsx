import { useEffect, useMemo, useState } from "react"
import { useLocation } from "react-router-dom"
import { Modal, Input, message } from "antd"

export const GetKey: React.FC<{
  open: boolean
  onChange: (value: string) => void
  onClose: (open: boolean) => void
}> = ({ open, onChange, onClose }) => {
  const { search } = useLocation()
  const searchParams = useMemo(() => new URLSearchParams(search), [search])
  const tenantId =
    searchParams.get("tenantId") || searchParams.get("tenantid") || ""
  const pathKey = searchParams.get("key")
  const [visible, setVisible] = useState(false)
  const [key, setKey] = useState("")
  const [defaultKey] = useState("46e648f6-826f-4da4-a3d2-2538b5582e88")

  useEffect(() => {
    if (handleSpecialTenantId()) return
    setKey(pathKey || "")
    if (pathKey) {
      setVisible(false)
      onClose(false)
      onChange(pathKey)
    } else {
      setVisible(true)
    }
  }, [pathKey])

  useEffect(() => {
    if (handleSpecialTenantId()) return

    if (open) {
      setVisible(true)
    }
  }, [open])

  // 特殊处理，租户id为1时，不需要key弹框 2025-07-01
  const handleSpecialTenantId = () => {
    if (tenantId === String(1)) {
      setKey(defaultKey)
      setVisible(false)
      onChange(defaultKey)
      return true
    }
    return false
  }

  const handleOk = () => {
    if (!key) {
      message.warning("请输入有效的 Key!")
      return
    }
    onChange(key)

    // 关闭模态框
    setVisible(false)
    onClose(false)
    // 清空输入
    setKey("")
  }

  const handleCancel = () => {
    setVisible(false)
    onClose(false)
    onChange("")
    setKey("")
  }

  return (
    <>
      <Modal
        title="请输入 Key"
        open={visible}
        onOk={handleOk}
        onCancel={handleCancel}
        maskClosable={false}
      >
        <Input
          value={key}
          onChange={(e) => setKey(e.target.value)}
          placeholder="请输入你的 Key"
        />
      </Modal>
    </>
  )
}

export default GetKey
