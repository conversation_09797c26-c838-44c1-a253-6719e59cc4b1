import React, { useRef, useState } from "react";
import MentionsComponent from "@/businessComponents/MentionsModule";
import "./index.less";
import { Button, Flex, theme, Input } from "antd";
import { getToken, getUserInfo } from "@/utils/auth";
import { cacheGet } from "@/utils/cacheUtil";
import type { MentionsComponentRef } from "@/businessComponents/MentionsModule";
import useSSEChat from "@/hooks/useSSEChat";
import MarkdownRenderer from "@/businessComponents/MarkdownModule";
const { useToken } = theme;
const { Search } = Input;
export const Tool: React.FC = () => {
  const { token } = useToken();
  const mentionsRef = useRef<MentionsComponentRef>(null);
  // const handleChange = (value: string, type: string) => {
  //   console.log(value, type, 1);
  // };
  const pageInfo = {
    pageName: "合同起草",
    pageDesc: "AI根据模版及资料快速起草合同",
    steps: [
      {
        title: "信息填写", // 步骤条名称
        content: "<MentionsModule />", // 步骤对应的组件
        submitName: "提交信息",
        isExport: false,
      },
      {
        title: "选择模版",
        content: "<MallModule />",
        submitName: "选择模版1",
        isExport: false,
      },
      {
        title: "生成大纲",
        content: "<MarkdownModule />",
        submitName: "生成大纲1",
        isExport: false,
      },
      {
        title: "生成合同",
        content: "<MarkdownModule />",
        submitName: "生成合同",
        isExport: true,
      },
    ],
  };
  const sseChat = useSSEChat();
  const getSubmitInfo = async (templateFile?: any) => {
    const tokenInfo = await getToken();
    const userInfo = await getUserInfo();
    const tenantId = cacheGet("tenantId");
    // 文件信息
    const data: any = mentionsRef.current?.getMentionsData();
    const fileData: any = [];
    data?.localFile.forEach((item: any) => {
      // 文件
      const obj = {
        type: item.fileType ? item.fileType : "document",
        transfer_method: "local_file",
        upload_file_id: item.id,
      };
      fileData.push(obj);
    });
    sseChat.start({
      url: "/dify/broker/agent/stream",
      headers: {
        "Content-Type": "application/json",
        Token: token || "",
      },
      body: {
        insId: "1",
        bizType: "app:agent",
        bizId: "0f31b4cb-5752-409d-b47d-38a9c1e162d6", // 取agentId
        agentId: "0f31b4cb-5752-409d-b47d-38a9c1e162d6", // 取agentId
        path: "/chat-messages",
        query: data.query || "",
        difyJson: {
          inputs: {
            docFiles: fileData,
            Token: tokenInfo || "",
            tenantid: tenantId || "",
            outputTemplate: templateFile?.id
              ? {
                  // 模板信息
                  type: "document",
                  transfer_method: "local_file",
                  upload_file_id: templateFile.id,
                }
              : null,
          },
          response_mode: "streaming",
          user: userInfo?.id || "anonymous",
          conversation_id: "",
          query: data.query || "",
        },
      },
      query: {},
      message: data.query || "",
      onFinished: (res) => {
        console.log(res, 111);
      },
    });
  };

  return (
    <>
      <Flex
        vertical
        justify="center"
        style={{
          width: "800px",
          height: "300px",
          margin: "0px auto ",
        }}
      >
        <MentionsComponent
          agentId="0f31b4cb-5752-409d-b47d-38a9c1e162d6"
          ref={mentionsRef}
          // onQueryChange={(val) => {
          //   handleChange(val, "content");
          // }}
        />
        <Flex style={{ marginTop: "20px" }}>
          <Button
            type="primary"
            style={{ width: "400px", margin: "auto" }}
            onClick={getSubmitInfo}
          >
            开始起草
          </Button>
        </Flex>
      </Flex>
    </>
  );
};

export default Tool;
