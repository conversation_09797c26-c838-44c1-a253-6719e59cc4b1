<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>政务业务查询</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.17.5/xlsx.full.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
       h1{
        color: rgb(28, 98, 212);
        padding: 15px;
        text-align: center;
      }
        </style>
</head>
<body class="bg-gray-100 min-h-screen flex flex-col">
    <main class="container mx-auto flex-grow p-6">
        <div class="bg-white rounded-lg shadow-lg p-6">
            <div class="mb-6">
                <h1 class="text-3xl font-bold mb-4">业务查询</h1>
                
                <!-- 服务查询 -->
                <div class="mb-4">
                    <div class="flex">
                        <input type="text" id="serviceType" 
                            placeholder="请输入想要查的业务（例如：一业一证办理流程）" 
                            class="flex-grow p-2 border rounded-l-lg">
                        <button onclick="matchService()" 
                            class="bg-blue-600 text-white px-4 py-2 rounded-r-lg hover:bg-blue-700">
                            查询服务
                        </button>
                    </div>
                    <!-- <div id="serviceMatchResult" class="mt-2 text-sm text-gray-600"></div> -->
                </div>
                <div
                id="document-section"
                class="mt-6 bg-white p-6 border border-gray-200 rounded-lg shadow-sm"
              >
                <h3 class="text-lg font-bold mb-4 text-gray-800">查询结果</h3>
                
                <pre
                  id="resultContainer"
                  class="bg-gray-50 p-4 rounded-lg whitespace-pre-wrap border border-gray-200"
                >暂无内容
                </pre>
                </div>
              </div>

              
             
                <!-- 自然语言输入 -->
            
                 </div>
                </div>
            </div>
            </div>
        </div>
    </main>

    <script>

                 //流式输出
    async function callChatMessagesAPI(apikey, query) {
      const resultContainer = document.getElementById("resultContainer");
      // 重置容器
      resultContainer.innerHTML = "正在加载...";

      try {
        const response = await fetch(
        "https://copilot.sino-bridge.com:90/v1/chat-messages",
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${apikey}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            inputs: {},
            query: query,
            response_mode: "streaming", // 流式模式
            conversation_id: "",
            user: "web-user-" + Math.random().toString(36).substr(2, 9),
            files: [],
          }),
        }
        );

        if (!response.ok) {
        // 尝试解析错误响应体
        const errorText = await response.text();
        throw new Error(`HTTP错误 ${response.status}: ${errorText}`);
        }

        // 处理流式响应
        const reader = response.body.getReader();
        const decoder = new TextDecoder("utf-8");
        let resultText = "";

        while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        const chunk = decoder.decode(value, { stream: true });
        const lines = chunk.split("\n");
        for (const line of lines) {
          if (line.trim()) {
            try {
            if (line.startsWith("data: ")) {
              const jsonString = line.substring(6).trim();
              if (jsonString) {
                try {
                  const data = JSON.parse(
                    jsonString
                      .replace(/\\n/g, "\\n")
                      .replace(/\\'/g, "\\'")
                      .replace(/\\"/g, '\\"')
                      .replace(/\\&/g, "\\&")
                      .replace(/\\r/g, "\\r")
                      .replace(/\\t/g, "\\t")
                      .replace(/\\b/g, "\\b")
                      .replace(/\\f/g, "\\f")
                  );
                  if (data.event === "message") {
                    resultText += data.answer;
                    resultContainer.innerHTML = `<pre class="whitespace-pre-wrap break-words">${resultText}</pre>`;
                    await new Promise((resolve) => setTimeout(resolve, 50)); // 控制输出速度
                  }
                } catch (e) {
                  // console.error('JSON解析错误', e);
                  // console.error('错误的JSON字符串:', jsonString);
                }
              }
            }
            } catch (e) {
            // console.error('JSON解析错误', e);
            }
          }
        }
        }

        resultText += decoder.decode();
        resultContainer.innerHTML = `<pre class="whitespace-pre-wrap break-words">${resultText}</pre>`;
      } catch (error) {
        // 详细的错误处理
        console.error("查询错误", error);
        resultContainer.innerHTML = "查询错误，请稍后重试。";
      }
    }
     

        // 服务匹配
        function matchService() {
            const serviceInput = document.getElementById('serviceType').value.trim();
            const serviceMatchResult = document.getElementById('serviceMatchResult');
            callChatMessagesAPI('app-bW6qXSGdwMTsQ9rDy2YQo1Pp', serviceInput);

            
        }

    </script>
</body>
</html>