import request from "@/utils/request";

const VITE_API_BASE_SYS: string = import.meta.env["VITE_API_BASE_SYS"] || "";
const VITE_USERINFO_BASS: string = import.meta.env["VITE_USERINFO_BASS"] || "";
/** 获取当前用户所有资源 */
export function getResource(): Promise<any> {
  return request({
    url: `${VITE_USERINFO_BASS}/oauth/anyone/visible/resource`,
    method: "GET",
  });
}

/** 修改用户头像 */
export function editUserAvatar(data: any): Promise<any> {
  return request({
    url: `${VITE_API_BASE_SYS}/sys/user/profile/avatar`,
    method: "POST",
    data,
  });
}

/** 修改用户信息 */
export function editUserInfo(data: any): Promise<any> {
  return request({
    url: `${VITE_USERINFO_BASS}/oauth/anyone/baseInfo`,
    method: "PUT",
    data,
  });
}

/** 修改密码信息 */
export function editPassword(data: any): Promise<any> {
  return request({
    url: `${VITE_USERINFO_BASS}/oauth/anyone/password`,
    method: "PUT",
    data,
  });
}
/** 获取当前用户信息 */
export function getCurrentUserInfo(params: any): Promise<any> {
  return request({
    url: `${VITE_USERINFO_BASS}/oauth/anyone/getUserInfoById`,
    method: "GET",
  });
}

export function getUserInfoById(): Promise<any> {
  return request({
    url: `${VITE_USERINFO_BASS}/oauth/anyone/getUserInfoById`,
    method: "GET",
  });
}

/** 通过refreshToken获取token */
export function refreshGetToken(params: any): Promise<any> {
  return request({
    url: `${VITE_USERINFO_BASS}/oauth/anyTenant/refresh`,
    method: "POST",
    params,
  });
}

// 获取当前租户下用户信息
export function getBaseEmployee(params: any): Promise<any> {
  return request({
    url: `${VITE_USERINFO_BASS}/api/base/baseEmployee/findAll`,
    method: "GET",
    params,
  });
}
