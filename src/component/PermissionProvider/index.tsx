import { createContext, useContext, useEffect, useMemo, useState } from 'react'
import { getResource } from '@/api/user'
import { cacheGet } from '@/utils/cacheUtil'

interface PermissionContextType {
  permissions: string[]
  setPermissions: React.Dispatch<React.SetStateAction<string[]>>
}

const PermissionContext = createContext<PermissionContextType>({
  permissions: [],
  setPermissions: () => {}
})

export function PermissionProvider({ children }: { children: React.ReactNode }) {
  const [permissions, setPermissions] = useState<string[]>([])

  useEffect(() => {
    if (cacheGet('token')) {
      getResource().then(resource => {
        setPermissions(resource.data.resourceList)
      })
    }
  }, [])

  const value = useMemo(() => ({ permissions, setPermissions }), [permissions, setPermissions])
  return <PermissionContext.Provider value={value}>{children}</PermissionContext.Provider>
}

export function usePermissions() {
  return useContext(PermissionContext)
}
