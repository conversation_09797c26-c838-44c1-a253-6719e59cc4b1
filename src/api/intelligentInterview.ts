import { SSEClient } from '@/utils/sse-client'
import { createClient } from '@supabase/supabase-js'
import { PolyvWebinarAPI, CreateWebinarRequest, GetRecordFilesRequest, RecordFile } from './polyvWebinar'
import axios from 'axios'

const VITE_AI_API_BASE = import.meta.env['VITE_AI_API_BASE'] || ""

// Supabase配置
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'your-supabase-url'
const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY || 'your-supabase-anon-key'

// 创建Supabase客户端，添加必要的头部配置
const supabase = createClient(supabaseUrl, supabaseKey, {
  global: {
    headers: {
      'Accept': 'application/json',
      'Content-Type': 'application/json',
      'Prefer': 'return=representation'
    }
  },
  db: {
    schema: 'public'
  }
})

// 智能面试助手Token
const intelligentInterviewToken = import.meta.env.VITE_INTELLIGENT_INTERVIEW_TOKEN || 'app-IntelligentInterviewToken123'
// 智能出题功能Token
const intelligentQuestionsToken = import.meta.env.VITE_INTELLIGENT_QUESTIONS_TOKEN || 'app-5cEli8xK3eKa0dzrhEcGa1Sn'
// 评估报告功能Token
const evaluationReportToken = import.meta.env.VITE_EVALUATION_REPORT_TOKEN || 'app-YBRsW7PtSItDhoIFVNVmVCEJ'

// SSE客户端实例
const sseClient = new SSEClient(intelligentInterviewToken)
// 智能出题专用SSE客户端实例
const questionsSSEClient = new SSEClient(intelligentQuestionsToken)
// 评估报告专用SSE客户端实例
const evaluationSSEClient = new SSEClient(evaluationReportToken)

export interface SSERequest {
  onMessage: (text: string) => void
  onError: (error: Error) => void
  onFinish: (data?: any) => void
}

export interface ResumeAnalysisRequest {
  candidateName: string
  jobPosition: string
  resumeFileUrl: string
  files: Array<{
    type: string
    transfer_method: string
    upload_file_id: string
  }>
}

export interface QuestionsGenerationRequest {
  resumeAnalysis: string
  jobRequirements: string
}

export interface MeetingCreationRequest {
  title: string
  candidateName: string
  interviewerName: string
  duration: number
  scheduledTime: string // 面试安排的时间
}

export interface TranscriptGenerationRequest {
  videoUrl: string
}

export interface ReportGenerationRequest {
  sessionId: string
  resumeAnalysis: string
  interviewQuestions: string
  transcript: string
}

export interface ResumeDataUpdate {
  resume_file_url?: string
  resume_analysis_md?: string
  interview_questions_md?: string
  job_position?: string
  job_requirements?: string
}

export interface InterviewDataUpdate {
  meeting_url?: string
  video_url?: string
  transcript_md?: string
  webinar_id?: string
  host_password?: string
  attendee_password?: string
  host_url?: string
  duration?: number
  meeting_title?: string
  interviewer_name?: string
  scheduled_time?: string
}

export interface ReportDataSave {
  interview_report_md: string
}

// SSE请求适配器
const sendSSERequest = async (params: {
  url: string
  method: string
  data: any
  onMessage: (text: string) => void
  onError: (error: Error) => void
  onFinish: (data?: any) => void
}) => {
  return sseClient.sendMessage({
    type: 'chat',
    query: params.data.query || '',
    inputs: params.data.inputs || {},
    conversationId: params.data.conversation_id || '',
    user: params.data.user || 'intelligent-interview',
    files: params.data.files || [],
    responseMode: params.data.response_mode || 'streaming',
    onMessage: (message: string | null, finished: boolean) => {
      if (message) {
        params.onMessage(message)
      }
      if (finished) {
        params.onFinish()
      }
    },
    onError: params.onError,
    onFinish: params.onFinish
  })
}

// 评估报告专用SSE请求适配器
const sendEvaluationSSERequest = async (params: {
  url: string
  method: string
  data: any
  onMessage: (text: string) => void
  onError: (error: Error) => void
  onFinish: (data?: any) => void
}) => {
  console.log('发送评估报告SSE请求:', params.data)
  console.log('使用Token:', evaluationReportToken)

  return evaluationSSEClient.sendMessage({
    type: 'chat',
    query: params.data.query || '',
    inputs: params.data.inputs || {},
    conversationId: params.data.conversation_id || '',
    user: params.data.user || 'intelligent-interview',
    files: params.data.files || [],
    responseMode: params.data.response_mode || 'streaming',
    onMessage: (message: string | null, finished: boolean) => {
      if (message) {
        params.onMessage(message)
      }
      if (finished) {
        params.onFinish()
      }
    },
    onError: params.onError,
    onFinish: params.onFinish
  })
}

// 智能出题专用SSE请求适配器
const sendQuestionsSSERequest = async (params: {
  url: string
  method: string
  data: any
  onMessage: (text: string) => void
  onError: (error: Error) => void
  onFinish: (data?: any) => void
}) => {
  console.log('发送智能出题SSE请求:', params.data)
  console.log('使用Token:', intelligentQuestionsToken)

  return questionsSSEClient.sendMessage({
    type: 'chat',
    query: params.data.query || '',
    inputs: params.data.inputs || {},
    conversationId: params.data.conversation_id || '',
    user: params.data.user || 'intelligent-interview',
    files: params.data.files || [],
    responseMode: params.data.response_mode || 'streaming',
    onMessage: (message: string | null, finished: boolean) => {
      if (message) {
        params.onMessage(message)
      }
      if (finished) {
        params.onFinish()
      }
    },
    onError: params.onError,
    onFinish: params.onFinish
  })
}

// 使用Axios上传文件到Dify - 更稳定的实现
export async function uploadFileToDify(file: File, token: string) {
  console.log('uploadFileToDify 开始:', { fileName: file.name, token: token.substring(0, 10) + '...' })

  // 使用axios.postForm方法，专门用于文件上传
  const url = `${VITE_AI_API_BASE}/files/upload`
  console.log('请求URL:', url)

  try {
    const response = await axios.postForm(url, {
      file: file,
      user: 'intelligent-interview'
    }, {
      headers: {
        Authorization: `Bearer ${token}`
      },
      timeout: 60000, // 60秒超时
      onUploadProgress: (progressEvent: any) => {
        if (progressEvent.total) {
          const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          console.log(`上传进度: ${percentCompleted}%`)
        }
      }
    })

    console.log('Axios响应状态:', response.status, response.statusText)
    console.log('解析的JSON数据:', response.data)
    return response.data
  } catch (error) {
    console.error('uploadFileToDify 错误:', error)

    if (axios.isAxiosError(error)) {
      if (error.code === 'ECONNABORTED') {
        throw new Error('文件上传超时，请重试')
      }
      if (error.response) {
        throw new Error(`HTTP ${error.response.status}: ${error.response.statusText}`)
      }
      if (error.request) {
        throw new Error('网络连接失败，请检查网络')
      }
    }

    throw error
  }
}

// 上传文件到 Supabase Storage
export async function uploadFileToSupabase(file: File): Promise<{ url: string; path: string }> {
  try {
    console.log('开始上传到 Supabase:', {
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type
    })

    // 生成唯一文件名
    const fileExt = file.name.split('.').pop()
    const fileName = `${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`
    const filePath = fileName // 直接使用文件名，不加 resumes/ 前缀

    console.log('生成的文件路径:', filePath)

    // 上传到 Supabase Storage
    const { data, error } = await supabase.storage
      .from('resumes')
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: false
      })

    console.log('Supabase 上传响应:', { data, error })

    if (error) {
      console.error('Supabase 上传错误详情:', error)
      throw new Error(`Supabase 上传失败: ${error.message}`)
    }

    // 获取公共 URL
    const { data: urlData } = supabase.storage
      .from('resumes')
      .getPublicUrl(filePath)

    console.log('获取的公共 URL:', urlData.publicUrl)

    return {
      url: urlData.publicUrl,
      path: filePath
    }
  } catch (error) {
    console.error('上传到 Supabase 失败:', error)
    throw error
  }
}

export const intelligentInterview = {
  // 获取面试会话列表
  async getSessionList(): Promise<Array<{id: string, candidate_name: string, status: string, created_at: string}>> {
    try {
      const { data, error } = await supabase
        .from('interview_sessions')
        .select('id, candidate_name, status, created_at')
        .order('created_at', { ascending: false })
        .limit(20)

      if (error) throw error
      return data || []
    } catch (error) {
      console.error('获取面试会话列表失败:', error)
      throw new Error('获取面试会话列表失败')
    }
  },

  // 获取完整的面试会话数据
  async getSessionData(sessionId: string): Promise<{
    session: any,
    resumeData: any,
    interviewData: any,
    reportData: any
  }> {
    try {
      // 获取会话基本信息
      const { data: sessionArray, error: sessionError } = await supabase
        .from('interview_sessions')
        .select('*')
        .eq('id', sessionId)
        .limit(1)

      if (sessionError) throw sessionError

      const session = sessionArray && sessionArray.length > 0 ? sessionArray[0] : null
      if (!session) throw new Error('会话不存在')

      // 获取简历数据
      const { data: resumeDataArray, error: resumeError } = await supabase
        .from('resume_data')
        .select('*')
        .eq('session_id', sessionId)
        .limit(1)

      const resumeData = resumeDataArray && resumeDataArray.length > 0 ? resumeDataArray[0] : null

      // 获取面试数据
      const { data: interviewDataArray, error: interviewError } = await supabase
        .from('interview_data')
        .select('*')
        .eq('session_id', sessionId)
        .limit(1)

      const interviewData = interviewDataArray && interviewDataArray.length > 0 ? interviewDataArray[0] : null

      // 获取报告数据
      const { data: reportDataArray, error: reportError } = await supabase
        .from('report_data')
        .select('*')
        .eq('session_id', sessionId)
        .limit(1)

      const reportData = reportDataArray && reportDataArray.length > 0 ? reportDataArray[0] : null

      return {
        session,
        resumeData: resumeError ? null : resumeData,
        interviewData: interviewError ? null : interviewData,
        reportData: reportError ? null : reportData
      }
    } catch (error) {
      console.error('获取面试会话数据失败:', error)
      throw new Error('获取面试会话数据失败')
    }
  },

  // 创建面试会话
  async createSession(candidateName: string): Promise<string> {
    try {
      const { data, error } = await supabase
        .from('interview_sessions')
        .insert([
          {
            candidate_name: candidateName,
            status: 'in_progress'
          }
        ])
        .select()

      if (error) throw error
      if (!data || data.length === 0) throw new Error('创建会话失败，未返回数据')
      return data[0].id
    } catch (error) {
      console.error('创建面试会话失败:', error)
      throw new Error('创建面试会话失败')
    }
  },

  // 保存简历数据（使用upsert操作）
  async saveResumeData(sessionId: string, data: ResumeDataUpdate): Promise<void> {
    try {
      const { error } = await supabase
        .from('resume_data')
        .upsert({
          session_id: sessionId,
          ...data
        }, {
          onConflict: 'session_id'
        })

      if (error) throw error
    } catch (error) {
      console.error('保存简历数据失败:', error)
      throw new Error('保存简历数据失败')
    }
  },

  // 更新简历数据
  async updateResumeData(sessionId: string, data: ResumeDataUpdate): Promise<void> {
    try {
      const { error } = await supabase
        .from('resume_data')
        .update(data)
        .eq('session_id', sessionId)

      if (error) throw error
    } catch (error) {
      console.error('更新简历数据失败:', error)
      throw new Error('更新简历数据失败')
    }
  },

  // 获取面试数据
  async getInterviewData(sessionId: string): Promise<{ data: any }> {
    try {
      const { data, error } = await supabase
        .from('interview_data')
        .select('*')
        .eq('session_id', sessionId)
        .limit(1)

      if (error) {
        console.error('获取面试数据失败:', error)
        throw new Error('获取面试数据失败')
      }

      return { data: data?.[0] || null }
    } catch (error) {
      console.error('获取面试数据失败:', error)
      throw new Error('获取面试数据失败')
    }
  },

  // 保存面试数据
  async saveInterviewData(sessionId: string, data: InterviewDataUpdate): Promise<void> {
    try {
      const { error } = await supabase
        .from('interview_data')
        .insert([
          {
            session_id: sessionId,
            ...data
          }
        ])

      if (error) throw error
    } catch (error) {
      console.error('保存面试数据失败:', error)
      throw new Error('保存面试数据失败')
    }
  },

  // 更新面试数据（使用upsert操作）
  async updateInterviewData(sessionId: string, data: InterviewDataUpdate): Promise<void> {
    try {
      // 使用upsert操作，如果记录不存在则创建，存在则更新
      const { error } = await supabase
        .from('interview_data')
        .upsert({
          session_id: sessionId,
          ...data
        }, {
          onConflict: 'session_id'
        })

      if (error) throw error
    } catch (error) {
      console.error('更新面试数据失败:', error)
      throw new Error('更新面试数据失败')
    }
  },

  // 保存报告数据
  async saveReportData(sessionId: string, data: ReportDataSave): Promise<void> {
    try {
      const { error } = await supabase
        .from('report_data')
        .insert([
          {
            session_id: sessionId,
            ...data
          }
        ])

      if (error) throw error
    } catch (error) {
      console.error('保存报告数据失败:', error)
      throw new Error('保存报告数据失败')
    }
  },

  // 简历分析
  async analyzeResume(request: ResumeAnalysisRequest, callbacks: SSERequest): Promise<void> {
    const prompt = `
请分析以下候选人的简历信息，生成详细的候选人画像：

候选人姓名：${request.candidateName}
应聘岗位：${request.jobPosition}

请从以下维度进行分析，并以Markdown格式输出：

## 基本信息
- 姓名、联系方式、教育背景等

## 工作经历分析
- 工作年限和职业发展轨迹
- 主要工作经验和职责
- 技能成长路径

## 技能能力评估
- 专业技能掌握程度
- 技术栈匹配度
- 学习能力和适应性

## 项目经验总结
- 主要项目经验
- 项目中的角色和贡献
- 解决问题的能力

## 综合评价
- 优势和亮点
- 可能的不足
- 与岗位的匹配度

请基于简历内容进行客观分析，突出关键信息。
    `

    return sendSSERequest({
      url: '/api/dify/chat',
      method: 'POST',
      data: {
        inputs: {
          prompt,
          candidate_name: request.candidateName,
          job_position: request.jobPosition
        },
        query: '请分析这份简历',
        response_mode: 'streaming',
        conversation_id: '',
        user: 'intelligent-interview',
        files: request.files
      },
      onMessage: callbacks.onMessage,
      onError: callbacks.onError,
      onFinish: callbacks.onFinish
    })
  },

  // 智能出题
  async generateQuestions(request: QuestionsGenerationRequest, callbacks: SSERequest): Promise<void> {
    console.log('开始生成面试题目')
    console.log('智能出题Token:', intelligentQuestionsToken)
    console.log('请求参数:', request)

    const prompt = `
基于以下候选人简历分析和岗位要求，生成个性化的面试题目：

## 候选人简历分析
${request.resumeAnalysis}

## 岗位要求
${request.jobRequirements}

请生成以下类型的面试题目，并以Markdown格式输出：

## 技术能力题目
针对候选人的技术背景和岗位要求，生成3-5个技术相关问题
每个问题包含：
- 问题描述
- 考察要点
- 参考答案要点
- 评分标准

## 项目经验题目
基于候选人的项目经历，生成2-3个项目相关问题
每个问题包含：
- 问题描述
- 考察要点
- 参考答案要点
- 评分标准

## 行为能力题目
评估候选人的软技能，生成2-3个行为面试问题
每个问题包含：
- 问题描述
- 考察要点
- 参考答案要点
- 评分标准

## 综合素质题目
评估学习能力、沟通能力等，生成1-2个综合问题

请确保题目具有针对性，能够有效评估候选人与岗位的匹配度。
    `

    return sendQuestionsSSERequest({
      url: '/api/dify/chat',
      method: 'POST',
      data: {
        inputs: {
          prompt,
          resume_analysis: request.resumeAnalysis,
          job_requirements: request.jobRequirements
        },
        query: '请生成个性化面试题目',
        response_mode: 'streaming',
        conversation_id: '',
        user: 'intelligent-interview'
      },
      onMessage: callbacks.onMessage,
      onError: callbacks.onError,
      onFinish: callbacks.onFinish
    })
  },

  // 创建会议
  async createMeeting(request: MeetingCreationRequest): Promise<{
    meetingUrl: string;
    webinarId?: string;
    hostUrl?: string;
    hostPassword?: string;
    attendeePassword?: string;
  }> {
    try {
      // 验证开始时间是否为未来时间
      const scheduledTime = new Date(request.scheduledTime)
      const currentTime = new Date()
      
      if (scheduledTime <= currentTime) {
        throw new Error('面试时间必须设置为未来时间')
      }

      // 构建保利威视研讨会创建请求
        const webinarRequest: CreateWebinarRequest = {
          title: `${request.title} - ${request.candidateName}面试`,
          startTime: request.scheduledTime, // 使用用户选择的面试时间
          duration: request.duration,
          description: `面试官：${request.interviewerName}，候选人：${request.candidateName}`,
          maxParticipants: 10, // 面试一般参与人数较少
          password: 'attend123', // 参会人密码
          settings: {
            allowChat: true,
            allowRecord: true,
            autoRecord: true,
            requireApproval: false
          }
        }

      // 调用保利威视API创建研讨会
      const webinarResponse = await PolyvWebinarAPI.createWebinar(webinarRequest)

      console.log('保利威视研讨会创建成功:', webinarResponse)

      return {
        meetingUrl: webinarResponse.joinUrl,
        webinarId: webinarResponse.webinarId,
        hostUrl: webinarResponse.hostUrl,
        hostPassword: webinarResponse.hostPassword,
        attendeePassword: webinarResponse.password
      }
    } catch (error) {
      console.error('创建会议失败:', error)

      // 如果保利威视API失败，抛出错误而不是返回模拟数据
      throw new Error(`创建会议失败: ${(error as Error).message}`)
    }
  },

  // 获取录制视频
  async getRecordFiles(webinarId: string, sessionId?: string): Promise<RecordFile[]> {
    try {
      const request: GetRecordFilesRequest = {
        channelId: webinarId
      }

      // 如果有sessionId，使用它来精确查询
      if (sessionId) {
        request.sessionId = sessionId
      } else {
        // 否则查询最近7天的录制文件
        const endDate = new Date()
        const startDate = new Date()
        startDate.setDate(endDate.getDate() - 7)

        request.startDate = startDate.toISOString().split('T')[0] // yyyy-MM-dd格式
        request.endDate = endDate.toISOString().split('T')[0]
      }

      const recordFiles = await PolyvWebinarAPI.getRecordFiles(request)
      console.log('获取到录制文件:', recordFiles)

      return recordFiles
    } catch (error) {
      console.error('获取录制文件失败:', error)
      throw new Error(`获取录制文件失败: ${(error as Error).message}`)
    }
  },

  // 说话人识别转录（使用FunASR接口）
  async generateSpeakerTranscript(videoUrl: string, callbacks: {
    onProgress?: (progress: number) => void
    onSegment?: (segment: any) => void
    onComplete?: (transcript: string) => void
    onError?: (error: string) => void
  }): Promise<void> {
    try {
      // 首先下载视频文件
      callbacks.onProgress?.(10)
      const response = await fetch(videoUrl)
      if (!response.ok) {
        throw new Error('无法下载视频文件')
      }

      const blob = await response.blob()
      callbacks.onProgress?.(20)

      // 创建FormData并上传到FunASR接口
      const formData = new FormData()
      formData.append('audio', blob, 'interview_video.mp4')

      callbacks.onProgress?.(30)

      // 调用说话人识别流式接口
      const speakerResponse = await fetch('https://copilot.sino-bridge.com/funasr/speaker_recall_stream', {
        method: 'POST',
        body: formData
      })

      if (!speakerResponse.ok) {
        throw new Error(`说话人识别失败: ${speakerResponse.status}`)
      }

      // 处理流式响应
      const reader = speakerResponse.body?.getReader()
      const decoder = new TextDecoder()

      if (!reader) {
        throw new Error('无法读取响应流')
      }

      let buffer = ''
      const segments: any[] = []
      let progress = 30

      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        buffer += decoder.decode(value, { stream: true })
        const lines = buffer.split('\n')
        buffer = lines.pop() || ''

        console.log('收到流数据行数:', lines.length)

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const jsonStr = line.slice(6).trim()
              if (!jsonStr) continue

              const data = JSON.parse(jsonStr)
              console.log('解析到数据:', data)

              switch (data.type) {
                case 'connected':
                  console.log('FunASR连接成功:', data.message)
                  break

                case 'status':
                  console.log('FunASR状态:', data.message)
                  if (data.progress !== undefined) {
                    progress = Math.max(progress, data.progress * 0.7 + 30) // 30-100%
                    callbacks.onProgress?.(progress)
                  }
                  break

                case 'segment_result': {
                  if (data.data) {
                    segments.push(data.data)
                    callbacks.onSegment?.(data.data)
                    console.log('收到转录片段:', data.data)
                  }
                  break;
                }

                case 'end': {
                  console.log('FunASR转录完成')
                  callbacks.onProgress?.(100)
                  // 生成最终的转录文本
                  const transcript = this.formatSpeakerTranscript(segments)
                  callbacks.onComplete?.(transcript)
                  return
                }
              }
            } catch (parseError) {
              console.warn('解析流数据失败:', parseError, '原始行:', line)
            }
          }
        }
      }

    } catch (error) {
      console.error('说话人识别转录失败:', error)
      callbacks.onError?.((error as Error).message)
    }
  },

  // 格式化说话人转录结果
  formatSpeakerTranscript(segments: any[]): string {
    if (!segments || segments.length === 0) {
      return '# 面试转录记录\n\n暂无转录内容'
    }

    // 按时间排序
    const sortedSegments = segments.sort((a, b) => a.start - b.start)

    // 生成转录文本
    let transcript = '# 面试转录记录\n\n## 识别结果:\n\n'

    for (const segment of sortedSegments) {
      const startTime = this.formatTime(segment.start)
      const endTime = this.formatTime(segment.end)
      const speakerName = segment.speaker_name || segment.speaker_id || '未知说话人'

      transcript += `${speakerName} (${startTime} - ${endTime}): ${segment.text}\n`
    }

    return transcript
  },

  // 格式化时间（秒转换为 mm:ss 格式）
  formatTime(seconds: number): string {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = Math.floor(seconds % 60)
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  },

  // 生成转录
  async generateTranscript(request: TranscriptGenerationRequest, callbacks: SSERequest): Promise<void> {
    const prompt = `
请将以下面试录像转换为详细的文字转录：

录像地址：${request.videoUrl}

请按照以下格式输出Markdown格式的转录内容：

# 面试转录记录

**面试时间**：[自动识别或手动填写]
**参与人员**：面试官、候选人

## 面试过程

### [时间] - [环节名称]
**面试官**：[面试官说话内容]
**候选人**：[候选人回答内容]

### [时间] - [环节名称]
**面试官**：[面试官说话内容]
**候选人**：[候选人回答内容]

请确保：
1. 准确识别说话人身份
2. 保持对话的完整性和连贯性
3. 标注重要的时间节点
4. 突出关键的技术讨论和回答
    `

    return sendSSERequest({
      url: '/api/speech/transcribe',
      method: 'POST',
      data: {
        inputs: {
          video_url: request.videoUrl,
          prompt
        },
        query: '请转录面试录像',
        response_mode: 'streaming',
        conversation_id: '',
        user: 'intelligent-interview'
      },
      onMessage: callbacks.onMessage,
      onError: callbacks.onError,
      onFinish: callbacks.onFinish
    })
  },

  // 生成评估报告
  async generateReport(request: ReportGenerationRequest, callbacks: SSERequest): Promise<void> {
    const prompt = `
基于以下面试数据，生成完整的面试评估报告：

## 简历分析
${request.resumeAnalysis}

## 面试题目
${request.interviewQuestions}

## 面试转录
${request.transcript}

请生成详细的评估报告，以Markdown格式输出：

# 面试评估报告

## 候选人信息
- 基本信息摘要
- 应聘岗位
- 面试时间

## 简历分析摘要
[从简历分析中提取关键信息]

## 面试表现评估

### 技术能力 (X/10)
- 具体技能评估
- 回答质量分析
- 技术深度评价

### 沟通能力 (X/10)
- 表达清晰度
- 逻辑思维能力
- 互动交流效果

### 综合素质 (X/10)
- 学习能力
- 问题解决能力
- 团队协作潜力

### 岗位匹配度 (X/10)
- 技能匹配程度
- 经验相关性
- 发展潜力

## 优势分析
[候选人的突出优势]

## 不足分析
[需要改进的方面]

## 录用建议
**[推荐录用/谨慎考虑/不推荐录用]**
[详细的录用建议和理由]

## 薪资建议
[基于表现的薪资建议范围]

## 后续建议
[如果录用，后续培养和发展建议]

请基于实际面试内容进行客观、专业的评估。
    `

    return sendEvaluationSSERequest({
      url: '/api/dify/chat',
      method: 'POST',
      data: {
        inputs: {
          prompt,
          session_id: request.sessionId,
          resume_analysis: request.resumeAnalysis,
          interview_questions: request.interviewQuestions,
          transcript: request.transcript
        },
        query: '请生成面试评估报告',
        response_mode: 'streaming',
        conversation_id: '',
        user: 'intelligent-interview'
      },
      onMessage: callbacks.onMessage,
      onError: callbacks.onError,
      onFinish: callbacks.onFinish
    })
  },

  // 更新面试会话状态
  async updateSessionStatus(sessionId: string, status: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('interview_sessions')
        .update({ status })
        .eq('id', sessionId)

      if (error) throw error
    } catch (error) {
      console.error('更新会话状态失败:', error)
      throw new Error('更新会话状态失败')
    }
  }
}
