import React, {
  useMemo,
} from "react";
import {
  Spin,
} from "antd";
import { useNavigate, useLocation } from "react-router-dom";
import { cacheSet } from "@/utils/cacheUtil";
import { LoadingOutlined } from "@ant-design/icons";
import { getUserInfo } from "@/api/loading";

const Loading: React.FC = () => {
  const navigate = useNavigate();
  const { search } = useLocation();
  const searchParams = useMemo(() => new URLSearchParams(search), [search]);
  const code = searchParams.get("code");
  getUserInfo({ code: code })
    .then((res: any) => {
      if (res.code == 200) {
        cacheSet("token", res.data.token);
        cacheSet("tenantId", res.data.tenantId);
        navigate("/chat?fromtype=app&hideheader=true");
      }
    })
    .catch((error: any) => {
      console.log(error);
    });

  return (
    <>
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "100vh",
        }}
      >
        <Spin indicator={<LoadingOutlined />} />
      </div>
    </>
  );
};

export default Loading;
