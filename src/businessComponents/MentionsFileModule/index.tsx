// 选择文件与知识库
import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useState,
} from "react";
import type { UploadProps } from "antd";
import "./index.less";
import {
  But<PERSON>,
  Divider,
  Flex,
  Mentions,
  message,
  Modal,
  Spin,
  theme,
  Upload,
} from "antd";
import { knowdgeSVGIcon } from "@/assets/config/menu/knowdge";
import { useGetState } from "ahooks";
import { getToken, getUserInfo } from "@/utils/auth";
import classNames from "classnames";
import { uploadChatFile,convertFileToPDF } from "@/api/public";
import SelectKnowledge from "@/component/SelectKnowledge";
import {
  CloseCircleFilled,
  DatabaseOutlined,
  FolderAddOutlined,
} from "@ant-design/icons";

const { useToken } = theme;

interface MentionsComponentProps {
  agentId: string;
  onQueryChange?: (query: string) => void;
}

export interface MentionsComponentRef {
  getMentionsData: () => object;
  setMentionsData: (data: any) => void;
}

const MentionsModule = forwardRef<MentionsComponentRef, MentionsComponentProps>(
  ({ agentId, onQueryChange }, ref) => {
    const { token: csstoken } = useToken();
    const [openDrawer, setOpenDrawer] = useState<boolean>(false); // 知识库弹窗
    const [selectKnIdsArr, setSelectKnIdsArr] = useState<any>([]); // 选中的知识信息
    const [localFile, setLocalFile] = useState<any>([]);
    const [webPageFile, setWebPageFile] = useState<any>([]);
    const [allFile, setAllFile] = useState<any>([]);
    const [localWebPageFile, setLocalWebPageFile] = useState<any>([]);
    const [selectKnowledgeArr, setSelectKnowledgeArr] = useState<any>([]);
    const [query, setQuery, getQuery] = useGetState<string>("");
    const [oneWebPageArr, setOneWebPageArr] = useState<any>([]);
    const [token, setToken] = useState<any>("");

    // 初始化数据
    useEffect(() => {
      const fetchData = async () => {
        const tokenInfo = await getToken();
        setToken(tokenInfo);
      };
      fetchData();
    }, []);

    const uploadFile: UploadProps = {
      name: "file",
      multiple: false,
      headers: {
        [import.meta.env.VITE_API_HEADER_KEY]: token,
      },
      showUploadList: false,
      beforeUpload(file) {
        // 只允许上传一个文件，如果已有文件则替换
        if (localFile.length > 0) {
          // 清空之前的文件
          setLocalFile([]);
          // message.info("已替换之前的文件");
        }
        
        const isLt2M = file.size / 1024 / 1024 < 15; // 限制文件大小为15MB
        if (!isLt2M) {
          message.error("不允许超过15MB!");
          return Promise.reject(new Error("false")); // 返回拒绝的 Promise 阻止上传
        }
        const arr = file.name.split(".");
        const fileName = arr[arr.length - 1] || "";
        const fileFormat = ["docx", "pptx", "xls", "xlsx", "csv", "txt", "pdf"];
        if (!fileFormat.includes(fileName)) {
          message.error("文件格式不正确!");
          return Promise.reject(new Error("false")); // 返回拒绝的 Promise 阻止上传
        }
      },
    };

    // 文件转base64
    function fileToBase64(file: any) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();

        // 成功读取文件时的回调
        reader.onload = () => {
          resolve(reader.result); // Base64 编码的字符串
        };

        // 读取文件失败时的回调
        reader.onerror = (error) => {
          reject(error);
        };

        // 读取文件并转为 Base64
        reader.readAsDataURL(file);
      });
    }

    const uploadQueue: any[] = []; // 存储待上传的文件
    let isUploading = false; // 标记是否正在上传

    const processQueue = async () => {
      if (uploadQueue.length === 0) {
        isUploading = false; // 队列为空时，停止上传状态
        return;
      }

      isUploading = true;
      const { file, type } = uploadQueue.shift()!;
      await uploadFileNew(file, type);

      // 由于只允许一个文件，处理完一个后就可以停止
      isUploading = false;
      
      // 清空队列，确保不会处理多余的文件
      uploadQueue.length = 0;
    };

    const handleCustomRequest = async (options: any) => {
      const { file } = options;
      
      // 清空队列，确保只处理当前文件
      uploadQueue.length = 0;
      
      // 添加新文件到队列
      uploadQueue.push({ file, type: 2 });

      if (!isUploading) {
        processQueue();
      }
    };

    // 上传文件
    let queue: Promise<void> = Promise.resolve();

    const uploadFileNew = (file: any, type: number) => {
      queue = queue.then(() => {
        return new Promise<void>((resolve, reject) => {
          let fileData: any = {
            fileName: "",
            fileStr: "",
          };
          if (!file) {
            resolve();
            return;
          }

          (async () => {
            const userInfo = await getUserInfo();
            try {
              fileData = {
                fileName: file.name,
                fileStr: await fileToBase64(file),
                loading: true,
                path: "/files/upload",
                agentId,
                user: userInfo?.id,
              };

              if (type == 2) {
                fileData.libName = file.name;
                const arr = file.name.split(".");
                fileData.libDesc = arr[arr.length - 1];
                fileData.flag = "file";
                // 只保留一个文件，直接替换
                setLocalFile([fileData]);
                // message.info("正在上传文件...");
              } else if (type == 3) {
                const selectIdFilterArr = webPageFile
                  .filter(
                    (itemArr: any) =>
                      file.name.substring(0, file.name.lastIndexOf(".")) ==
                      itemArr.title
                  )
                  .map((itemArr: any) => ({
                    flag: "webPageFile",
                    libName: itemArr.title,
                    libDesc: itemArr.url,
                    faviocn: itemArr.favIconUrl,
                    id: itemArr.id,
                    loading: true,
                  }));
                setLocalWebPageFile((prev: any) => [
                  ...prev,
                  ...selectIdFilterArr,
                ]);
              }
            } catch (error: any) {
              console.error("文件转 Base64 出错：", error);
              reject();
              return;
            }
            if (["docx", "doc"].includes(fileData.libDesc)) {
              convertFileToPDF(file).then(async (response) => {
                if (response["status"] && response["status"] !== 200) {
                  message.open({
                    key: "uploading",
                    type: "error",
                    content: "文件处理异常，请稍后重试",
                    duration: 1,
                  });
                } else if ("blob" in response) {
                  const blob = await response.blob();
                  const pdfFile = new File([blob], `${fileData.libDesc}.pdf`, {
                    type: "application/pdf",
                  });
                  uploadChatFile(fileData).then(async (response) => {
                    const res = response.data;
                    if (res?.name) {
                      if (type == 2) {
                        res.libName = res.name;
                        res.libDesc = res.extension;
                        res.flag = "file";
                        res.fileType = "document";
                        res.url = URL.createObjectURL(pdfFile)
                        // 只保留一个文件，直接替换
                        setLocalFile([{ ...res, loading: false }]);
                        message.success("文件上传成功");
                      } else if (type == 3) {
                        setLocalWebPageFile((prevArr: any) =>
                          prevArr.map((item: any) =>
                            item.libName ==
                            res.name.substring(0, res.name.lastIndexOf("."))
                              ? {
                                  ...item,
                                  loading: false,
                                  uid: res.id,
                                  size: res.size,
                                  mime_type: res.mime_type,
                                  extension: res.extension,
                                }
                              : item
                          )
                        );
                      }
                    } else {
                      message.open({
                        type: "error",
                        content: "上传失败",
                      });
                      // 上传失败时，清空文件列表
                      if (type == 2) {
                        setLocalFile([]);
                      }
                    }
                    resolve();
                  });
                }
              })
            } else {
              uploadChatFile(fileData).then(async (response) => {
                const res = response.data;
                if (res?.name) {
                  if (type == 2) {
                    res.libName = res.name;
                    res.libDesc = res.extension;
                    res.flag = "file";
                    res.fileType = "document";
                    res.url = URL.createObjectURL(file)
                    // 只保留一个文件，直接替换
                    setLocalFile([{ ...res, loading: false }]);
                    message.success("文件上传成功");
                  } else if (type == 3) {
                    setLocalWebPageFile((prevArr: any) =>
                      prevArr.map((item: any) =>
                        item.libName ==
                        res.name.substring(0, res.name.lastIndexOf("."))
                          ? {
                              ...item,
                              loading: false,
                              uid: res.id,
                              size: res.size,
                              mime_type: res.mime_type,
                              extension: res.extension,
                            }
                          : item
                      )
                    );
                  }
                } else {
                  message.open({
                    type: "error",
                    content: "上传失败",
                  });
                  // 上传失败时，清空文件列表
                  if (type == 2) {
                    setLocalFile([]);
                  }
                }
                resolve();
              });
            }
            
            
          })();
        });
      });
    };

    const handleDeleteKnowledge = (itemObj: any, index: number) => {
      console.log(allFile, "allFile");
      if (itemObj.flag === "know") {
        setSelectKnIdsArr(
          selectKnIdsArr.filter((item: any, i: number) => item.id !== itemObj.id)
        );
      }
      if (itemObj.flag === "knowledge") {
        setSelectKnowledgeArr(
          selectKnowledgeArr.filter((item: any, i: number) => item.id !== itemObj.id)
        );
      } else if (itemObj.flag === "file") {
        // 直接清空文件列表，因为只允许一个文件
        setLocalFile([]);
        message.info("已删除文件");
      } else if (itemObj.flag === "webPageFile") {
        setLocalWebPageFile(
          localWebPageFile.filter((item: any, i: number) => item.id !== itemObj.id)
        );
        for (let i = 0; i < webPageFile.length; i++) {
          if (webPageFile[i].id == itemObj.id) {
            webPageFile[i].checked = false;
          }
        }
        setWebPageFile(webPageFile);
      } else {
        const newOneWebPageArr = oneWebPageArr.filter(
          (item: any, i: number) => item.id !== itemObj.id
        );
        setOneWebPageArr(newOneWebPageArr);
      }
    };

    const fileExtensionHandler = (item: any) => {
      if (item.libDesc === "pdf") {
        return <span className="extend-icon">{knowdgeSVGIcon.pdf}</span>;
      } else if (item.libDesc === "docx") {
        return <span className="extend-icon">{knowdgeSVGIcon.word}</span>;
      } else if (
        item.libDesc === "xls" ||
        item.libDesc === "xlsx" ||
        item.libDesc === "csv"
      ) {
        return <span className="extend-icon">{knowdgeSVGIcon.excel}</span>;
      } else if (item.libDesc === "txt") {
        return <span className="extend-icon">{knowdgeSVGIcon.txt}</span>;
      } else if (item.libDesc === "pptx") {
        return <span className="extend-icon">{knowdgeSVGIcon.ppt}</span>;
      }
      return null;
    };

    useEffect(() => {
      setAllFile([
        ...localFile,
        ...selectKnowledgeArr,
        ...localWebPageFile,
        ...oneWebPageArr,
        ...selectKnIdsArr,
      ]);
    }, [
      localFile,
      selectKnowledgeArr,
      selectKnIdsArr,
      localWebPageFile,
      oneWebPageArr,
    ]);

    useEffect(() => {
      onQueryChange?.(query);
    }, [query]);

    useImperativeHandle(ref, () => ({
      getMentionsData: () => {
        return {
          // localWebPageFile,
          // selectKnowledgeArr,
          // oneWebPageArr,
          localFile,
          query: getQuery(),
        };
      },
      setMentionsData: (data: any) => {
        if (data.localFile !== undefined) setLocalFile(data.localFile);
        // if (data.localWebPageFile !== undefined)
        //   setLocalWebPageFile(data.localWebPageFile);
        // if (data.selectKnowledgeArr !== undefined)
        //   setSelectKnowledgeArr(data.selectKnowledgeArr);
        // if (data.oneWebPageArr !== undefined)
        //   setOneWebPageArr(data.oneWebPageArr);
        // if (data.selectKnIdsArr !== undefined)
        //   setSelectKnIdsArr(data.selectKnIdsArr);
        if (data.query !== undefined) setQuery(data.query);
      },
    }));

    const handleKnowledgeChange = (data: any) => {
      // 1 是知识库 2是知识
      const selectKnowledgeIdArr = data
        .filter((item: any) => item.isTypeKnow == "1")
        .map((item: any) => ({
          id: item.id,
          flag: "knowledge",
          libName: item.libName,
          libDesc: item.libDesc,
          isTypeKnow: 1,
        }));
      setSelectKnowledgeArr(selectKnowledgeIdArr);

      // 知识
      const filterArr = data
        .filter((item: any) => item.isTypeKnow === "2")
        .map((item: any) => ({
          id: item.id,
          flag: "know",
          libName: item.fileName,
          libDesc: item.fileType,
          isTypeKnow: 2,
        }));

      setSelectKnIdsArr(filterArr);
    };

    const onCloseDrawer = () => {
      setOpenDrawer(false);
    };

    const onGetKnowledge = (data: any) => {
      handleKnowledgeChange(data);
      setOpenDrawer(false);
    };

    const closeFun = () => {
      handleKnowledgeChange([]);
    };

    return (
      <Flex className="mentions-components">
        <Flex className="text-input" vertical>
          <Flex className="chat-textarea" vertical>
            <Mentions
              prefix="/"
              autoFocus
              className="text-input-mentions"
              placeholder="候选人姓名【】，候选人岗位【】，招聘要求"
              rows={4}
              value={query}
              maxLength={10000}
              options={[]}
              onInput={(e) => {
                let value = (e.target as HTMLInputElement).value;
                // 检查内容是否只包含空格或回车符
                if (/^\s*$/.test(value)) {
                  setQuery(""); // 如果只包含空格或回车符，清空输入框
                } else {
                  setQuery(value); // 否则更新输入内容
                }
              }}
            />
            <Flex vertical style={{ padding: "0px 12px 12px" }}>
              <Divider
                orientation="left"
                style={{
                  color: csstoken.colorTextTertiary,
                  fontSize: "10px",
                  margin: "0px",
                  fontWeight: "normal",
                }}
              >
                素材参考
              </Divider>
              <Flex
                className="top-toolbar"
                justify="space-between"
                align="center"
              >
                <Flex className="top-toolbar-left" align="center">
                  <Flex align="center" gap={csstoken.marginXXS}>
                    <Flex className="upload-file">
                      <Upload
                        {...uploadFile}
                        maxCount={1}
                        customRequest={handleCustomRequest}
                        className="sino-upload-file"
                      >
                        <Button
                          type="text"
                          id="sinoFolderAddOutlined"
                          icon={<FolderAddOutlined className="icon" />}
                          className="btn-icon"
                        />
                      </Upload>
                    </Flex>
                  </Flex>
                </Flex>
              </Flex>
              <Flex
                className={classNames({ "knowledge-base": allFile.length > 0 })}
              >
                <Flex className="knowledge-base-info">
                  {allFile && allFile.length > 0 && (
                    <Flex
                      key={0}
                      className="knowledge-content-base"
                      style={{ width: "100%" }}
                    >
                      {allFile[0].loading && (
                        <Flex className="knowledge-load" align="center">
                          <Spin spinning={allFile[0].loading}></Spin>
                        </Flex>
                      )}
                      <Flex
                        className="knowledge-content-base-flex"
                        align="center"
                        style={{
                          width: "100%",
                        }}
                      >
                        <Flex
                          className="knowledge-content-base-item"
                          align="center"
                        >
                          {allFile[0].flag === "knowledge" && (
                            <Flex className="sino-relation-icon">
                              {knowdgeSVGIcon.mainImage1}
                            </Flex>
                          )}
                          {(allFile[0].flag === "file" || allFile[0].flag === "know") &&
                            fileExtensionHandler(allFile[0])}
                          {(allFile[0].flag === "webPageFile" ||
                            allFile[0].flag === "oneWebPageFile") && (
                            <img
                              width="24"
                              height="24"
                              src={allFile[0].faviocn}
                              alt=""
                            />
                          )}
                          <Flex
                            className="knowledge-base-item first-title"
                            vertical
                            justify="center"
                          >
                            <div className="knowledge-base-title">
                              {allFile[0].libName}
                            </div>
                            <div className="two-title">{allFile[0].libDesc}</div>
                          </Flex>
                        </Flex>
                        {!allFile[0].loading && (
                          <CloseCircleFilled
                            className="close"
                            onClick={() =>
                              handleDeleteKnowledge(allFile[0], 0)
                            }
                          />
                        )}
                      </Flex>
                    </Flex>
                  )}
                </Flex>
              </Flex>
            </Flex>
          </Flex>
        </Flex>
        <Modal
          title="知识库"
          open={openDrawer}
          onCancel={onCloseDrawer}
          footer={null}
          className="modal-knowledge-drawer"
        >
          <SelectKnowledge
            openDrawer={openDrawer}
            onGetKnowledge={onGetKnowledge}
            drawerOpenTime={Date.now()}
            selectKnowledgeArr={selectKnowledgeArr}
            selectKnIdsArr={selectKnIdsArr}
            onCloseFun={closeFun}
          ></SelectKnowledge>
        </Modal>
      </Flex>
    );
  }
);
// 添加 displayName
MentionsModule.displayName = "MentionsModule";
export default MentionsModule;
