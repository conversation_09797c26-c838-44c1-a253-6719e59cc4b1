export interface IFetchArguments {
  api: string
  params: object
  file?: boolean
  callback: (res: any) => void
}

interface IFetchMessage {
  api: string
  type: string
}

export type IFetchRequestMessage = {
  params: object
  file?: file
} & IFetchMessage

export type IFetchResponseMessage = {
  response: any
} & IFetchMessage

export interface IFetchSSEArguments {
  /** SSE请求接口地址 */
  url: string
  /** 请求头 */
  headers: Record<string, string>
  /** 请求体 */
  body: Record<string, string | object>
  /** 请求参数 */
  query: Record<string, string | object>
  /** 回调函数 */
  callback?: (res: any) => void
  /** SSE指令 */
  instruct?: string
}

export type IFetchSSERequestMessage = {
  url: string
  headers: Record<string, string>
  body: Record<string, string | object>
  instruct: string
  query: Record<string, string | object>
} & IFetchMessage

export type IFetchSSEResponseMessage = {
  url: string
  process: string
  instruct: string
  responseEvent: any
} & IFetchMessage
