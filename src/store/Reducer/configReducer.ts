import axios from 'axios'
import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import { getLocalStorage, setLocalStorage } from '@/utils/storage'

interface ConfigState {
  data: Record<string, any>
  loading: boolean
  error: string | null
}

export const fetchConfig = createAsyncThunk('config/fetchConfig', async (configUrl: string) => {
  const response = await axios.get(configUrl)
  return response.data
})

const initialState: ConfigState = {
  data: getLocalStorage('configData', true) ?? {
    sso_domain: '',
    code_parser_domain: '',
    writing_assistant_agent_id: '',
    reply_assistant_agent_id: '',
    universal_assistant_agent_id: ''
  },
  loading: false,
  error: null
}

const registrantSlice = createSlice({
  name: 'counter',
  initialState,
  reducers: {},
  extraReducers: builder => {
    builder
      .addCase(fetchConfig.pending, state => {
        state.loading = true
        state.error = null
      })
      .addCase(fetchConfig.fulfilled, (state, action) => {
        state.loading = false
        setLocalStorage('configData', action.payload.data)
        state.data = action.payload.data
      })
      .addCase(fetchConfig.rejected, (state, action) => {
        state.loading = false
        state.error = action.error.message || 'Failed to fetch config'
      })
  }
})

const configReducer = registrantSlice.reducer

export { configReducer }
export type { ConfigState }
