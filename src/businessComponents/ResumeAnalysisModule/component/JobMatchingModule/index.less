.job-matching-module {
  .matching-card {
    .overall-matching {
      text-align: center;
      margin-bottom: 24px;
      
      h3 {
        margin-bottom: 20px;
        font-size: 24px;
        font-weight: 600;
        color: #262626;
        text-align: left;
      }
      
      .score-display {
        display: flex;
        align-items: center;
        justify-content: center;
        
        .score-circle {
          width: 120px;
          height: 120px;
          border-radius: 50%;
          background: linear-gradient(135deg, #1890ff 0%, #52c41a 100%);
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          color: white;
          box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
          
          .score-number {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 4px;
          }
          
          .score-label {
            font-size: 12px;
            opacity: 0.9;
          }
        }
      }
    }
    
    .radar-chart-section {
      margin-bottom: 24px;
      
      h4 {
        margin-bottom: 16px;
        font-size: 18px;
        font-weight: 600;
        color: #262626;
        text-align: center;
      }
      
      .chart-container {
        // background: #f8f9fa;
        border-radius: 8px;
        padding: 20px;
        border: 1px solid #e8e8e8;
      }
    }
    
    .rationale-section {
      h4 {
        margin-bottom: 16px;
        font-size: 18px;
        font-weight: 600;
        color: #262626;
        text-align: center;
      }
      
      .rationale-list {
        .rationale-item {
          display: flex;
          align-items: flex-start;
          margin-bottom: 16px;
          padding: 16px;
          // background: #f8f9fa;
          border-radius: 8px; 
          // border-left: 4px solid #1890ff;
          
          .rationale-tag {
            margin-right: 12px;
            margin-top: 2px;
            flex-shrink: 0;
          }
          
          .rationale-text {
            color: #262626;
            line-height: 1.6;
            font-size: 14px;
          }
          
          &:last-child {
            margin-bottom: 0;
          }
        }
        
        .no-data {
          text-align: center;
          padding: 40px 20px;
          color: #999;
          font-size: 16px;
          
          p {
            margin: 0;
          }
        }
      }
    }
  }
}
