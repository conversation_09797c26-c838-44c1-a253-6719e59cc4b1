// DraftPage.tsx
import React, { useState, useRef, createRef, RefObject } from "react";
import {
  Button,
  Flex,
  Form,
  message,
  Upload,
  Select,
  Spin,
  Typography,
} from "antd";
import useSSEChat from "@/hooks/useSSEChat";
import HeaderCom from "@/component/Header";
import uploadIcon from "@/assets/images/public/upload.png";
import { fileToBase64 } from "@/utils/common";
import { getUserInfo } from "@/utils/auth";
import { convertFileToPDF, uploadChatFile } from "@/api/public";
import "./index.less";

// 自动匹配业务组件
const modules = import.meta.glob("/src/businessComponents/**/index.tsx");

const pageInfo = {
  pageName: "智能合同审查助手",
  pageDesc: "法审、敏感词、批注",
  steps: [
    {
      title: "上传文档",
      content: "<DocumentSplittingModule />",
      isExport: false,
      agentId: "66c19948-5427-4c0d-b25a-5eb87ebfd989",
    },
    {
      title: "文档处理",
      content: "<SplitPreviewModule />",
      isExport: false,
      agentId: "66c19948-5427-4c0d-b25a-5eb87ebfd989",
    },
    {
      title: "规则与目标确认",
      content: "<TargetingModule />",
      isExport: false,
      agentId: "",
    },
    {
      title: "合同法审",
      content: "<ContractLawReview />",
      isExport: true,
      agentId: "",
    },
  ],
};

const DraftPage: React.FC = () => {
  const splitTypeOptions = [{ label: "按章节切分", value: "按章节切分" }];
  const [current, setCurrent] = useState(0);
  const [globalLoading, setGlobalLoading] = useState(false); // 全局加载状态
  const [uploadedFiles, setUploadedFiles] = useState([]); // 上传的文件信息
  const [originalFile, setOriginalFile] = useState<any>(null); // 原始上传的文件
  const [splitType, setSplitType] = useState(splitTypeOptions[0].value); // 切分类型
  const [isQuentially, setIsQuentially] = useState(false);
  const currentStep = pageInfo.steps[current];
  const [stepData, setStepData] = useState<Record<number, any>>({});

  // 所有组件的 ref 映射
  const refs = useRef<Record<number, RefObject<any>>>({});

  // 缓存懒加载组件
  const componentCache = useRef<Record<number, React.ComponentType<any>>>({});

  // 获取当前步骤对应的组件
  const getDynamicComponent = (stepIndex: number) => {
    // 如果缓存里有，直接用
    if (componentCache.current[stepIndex]) {
      return componentCache.current[stepIndex];
    }

    const match = pageInfo.steps[stepIndex].content.match(/<(\w+)\s*\/>/);
    const componentName = match?.[1];
    if (!componentName) return null;

    const modulePath = `/src/businessComponents/${componentName}/index.tsx`;
    const loader = modules[modulePath];

    if (!loader) {
      console.warn("模块未找到:", modulePath);
      return null;
    }

    // 创建 ref（如果不存在）
    if (!refs.current[stepIndex]) {
      refs.current[stepIndex] = createRef();
    }

    // 懒加载组件，只创建一次
    const LazyComponent = React.lazy(loader as any);
    const WrappedComponent = (props: any) => (
      <LazyComponent ref={refs.current[stepIndex]} {...props} />
    );

    componentCache.current[stepIndex] = WrappedComponent;
    return WrappedComponent;
  };

  const DynamicComponent = getDynamicComponent(current);

  // 提交操作
  const getSubmitInfo = async (item: any) => {
    const ref = refs.current[current];
    let childData = null;
    if (current == 0) {
      childData = await ref?.current?.triggerSplit?.();
      if (childData?.files?.length < 1) return;
    } else if (current == 1) {
      const data = await ref?.current?.triggerSplit?.();
      if (data?.isQuentially === false) {
        ref?.current?.getProcessedData?.();
        setIsQuentially(true);
        return;
      } else {
        ref?.current?.showModal?.();
        return;
      }
    } else {
      childData = await ref?.current?.getMentionsData?.();
    }

    setStepData((prev) => ({ ...prev, [current]: childData }));
    if (current < pageInfo.steps.length - 1) {
      setCurrent(current + 1);
    }
  };

  // 上传文件
  const beforeUpload = async (file: File) => {
    const originalFilename = file.name.substring(0, file.name.lastIndexOf("."));
    const originalFileExt = file.name
      .substring(file.name.lastIndexOf(".") + 1)
      ?.toLowerCase();
    setGlobalLoading?.(true);
    if (["docx", "doc"].includes(originalFileExt)) {
      convertFileToPDF(file).then(async (response) => {
        if (response["status"] && response["status"] !== 200) {
          setGlobalLoading?.(false);
          message.open({
            key: "uploading",
            type: "error",
            content: "文件处理异常，请稍后重试",
            duration: 1,
          });
        } else if ("blob" in response) {
          const userInfo = await getUserInfo();
          const blob = await response.blob();
          const pdfFile = new File([blob], `${originalFilename}.pdf`, {
            type: "application/pdf",
          });
          console.log(file, 123);
          const fileData = {
            fileName: file.name,
            fileStr: await fileToBase64(file),
            path: "/files/upload",
            agentId,
            user: userInfo?.id,
            libName: file.name,
            libDesc: "",
            flag: "file",
          };
          uploadChatFile(fileData).then(async (response: any) => {
            setGlobalLoading?.(false);
            if (response.code == 200) {
              setUploadedFiles([
                { url: URL.createObjectURL(pdfFile), ...response.data },
              ]);
              setOriginalFile(file);
              message.open({
                key: "uploading",
                type: "success",
                content: "文件上传成功",
                duration: 1,
              });
            } else {
              message.open({
                key: "uploading",
                type: "error",
                content: "文件上传失败",
                duration: 1,
              });
            }
          });
        }
      });
    }
  };
  return (
    <>
      <Spin tip="加载中" spinning={globalLoading} fullscreen size="large" />
      <Flex vertical className="legal-review-page" align="center">
        <Flex>
          <HeaderCom
            mainTitle={pageInfo.pageName}
            subTitle={pageInfo.pageDesc}
          />
          <Flex
            vertical
            gap="middle"
            style={{ flex: 1 }}
            className="splitting-form"
          >
            <Form layout="vertical">
              <Form.Item>
                <Flex className="upload">
                  <Flex vertical gap="4" style={{ flex: 1 }}>
                    <Typography.Title className="file-title" level={5}>
                      合同上传
                    </Typography.Title>
                    <Upload.Dragger
                      showUploadList={false}
                      multiple={false}
                      beforeUpload={beforeUpload}
                      accept=".docx"
                      fileList={uploadedFiles}
                    >
                      <img
                        src={uploadIcon}
                        style={{ width: 45, margin: "0px auto" }}
                      />
                      <p className="ant-upload-hint">
                        {uploadedFiles && uploadedFiles.length > 0 ? (
                          <span>{uploadedFiles[0].name}</span>
                        ) : (
                          <span>点击或将文件拖到此处上传</span>
                        )}
                        <span>支持word格式文档</span>
                      </p>
                    </Upload.Dragger>
                  </Flex>
                </Flex>
              </Form.Item>
              <Form.Item
                label={
                  <>
                    <span style={{ color: "#000", fontWeight: "bold" }}>
                      选择拆分方式
                    </span>
                  </>
                }
              >
                <Select
                  value={splitType}
                  options={splitTypeOptions}
                  onChange={setSplitType}
                  placeholder="请选择拆分方式"
                />
              </Form.Item>
            </Form>
            <Button
              size="large"
              type="primary"
              className="upload-btn"
              onClick={() => {
                if (uploadedFiles.length === 0) {
                  message.error("请先上传文件");
                }
              }}
            >
              下一步
            </Button>
          </Flex>
        </Flex>
        {/* <Flex vertical className="legal-page-con" justify="center">
          <Steps current={current} style={{ marginBottom: 24 }}>
            {pageInfo.steps.map((item) => (
              <Steps.Step key={item.title} title={item.title} />
            ))}
          </Steps>

          <div
            style={{
              marginTop: "20px",
              height: "calc(100vh - 280px)",
              overflowY: "auto",
            }}
          >
            <Suspense fallback={<div>加载中...</div>}>
              {DynamicComponent ? (
                <DynamicComponent
                  setGlobalLoading={setGlobalLoading}
                  agentId={currentStep.agentId}
                  splittingData={stepData[0]}
                  splitViewData={{
                    messages: "",
                    chunks: stepData[0]?.chunks,
                    fileList: stepData[0]?.files,
                    isQuentially: isQuentially,
                  }}
                  targetingData={{
                    fileList: stepData[0]?.files,
                  }}
                />
              ) : (
                <div>组件未找到</div>
              )}
            </Suspense>
          </div>

          <div style={{ textAlign: "center", marginTop: 20 }}>
            {current > 0 && (
              <Button
                style={{ minWidth: "100px", marginRight: 8 }}
                onClick={() => setCurrent(current - 1)}
              >
                上一步
              </Button>
            )}
            {current < pageInfo.steps.length - 1 && (
              <Button
                type="primary"
                onClick={() => getSubmitInfo(currentStep)}
                style={{ minWidth: "100px", marginRight: 8 }}
              >
                {currentStep.submitName || "下一步"}
              </Button>
            )}
            {current > 1 && <Button type="primary">重新生成</Button>}
            {currentStep.isExport && (
              <Button type="primary" onClick={() => console.log("导出操作")}>
                导出
              </Button>
            )}
          </div>
        </Flex> */}
      </Flex>
    </>
  );
};

export default DraftPage;
