* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  height: 100vh;
  width: 100vw;
  // overflow: hidden;
}

/* 2. 清除默认边距 */
body, h1, h2, h3, h4, h5, h6, p, ul, ol, li, figure, figcaption, blockquote, dl, dd {
  margin: 0;
  padding: 0;
}

/* 3. 全局字体优化 */
body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased; /* 优化 macOS 字体渲染 */
}

/* 4. 媒体元素优化（图片、视频、嵌入内容） */
img, picture, video, canvas, svg {
  max-width: 100%;
  height: auto;
  display: block;
}

/* 5. 表单元素优化 */
input, button, textarea, select {
  font: inherit; /* 继承父元素字体 */
  border: none;
  background: none;
  outline: none;
}

/* 6. 防止长单词溢出容器 */
html {
  word-break: break-word;
}

#root {
  height: 100%;
  overflow-x: hidden;
}

.custom-scrollbar {
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: rgba(255, 255, 255, 0.3);
  }

  &::-webkit-scrollbar-corner {
    background: transparent;
  }
}

/* 容器基本样式 */
.scroll-container {
  overflow: overlay;
  transition: all 0.3s ease;
}

/* 悬停时显示滚动条 */
.scroll-container:hover::-webkit-scrollbar,
.scroll-container:hover::-webkit-scrollbar-track,
.scroll-container:hover::-webkit-scrollbar-thumb {
  visibility: visible;
}

/* Webkit内核浏览器定制滚动条 */
.scroll-container::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  background: transparent;
  visibility: hidden;
}

.scroll-container::-webkit-scrollbar-track {
  background: rgba(200, 200, 200, 0.1);
  border-radius: 4px;
  margin: 4px 0;
  visibility: hidden;
}

.scroll-container::-webkit-scrollbar-thumb {
  background: rgba(150, 150, 150, 0.5);
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: background 0.3s, width 0.2s;
  visibility: hidden;
}

/* 悬停时滑块样式优化 */
.scroll-container:hover::-webkit-scrollbar-thumb {
  background: rgba(120, 120, 120, 0.8);
  background-clip: content-box;
  visibility: visible;
}

/* 横向滚动条优化 */
.scroll-container::-webkit-scrollbar-thumb:horizontal {
  min-width: 40px;
}

/* Firefox浏览器适配 */
@supports (scrollbar-color: red blue) {
  .scroll-container {
    scrollbar-width: thin;
    scrollbar-color: rgba(150, 150, 150, 0.5) transparent;
  }

  .scroll-container:hover {
    scrollbar-color: rgba(120, 120, 120, 0.8) transparent;
  }
}

/* 边缘浏览器适配 */
@supports (-ms-ime-align: auto) {
  .scroll-container {
    -ms-overflow-style: auto;
  }
}

/* 移动端禁用滚动条 */
@media (pointer: coarse) {
  .scroll-container::-webkit-scrollbar {
    display: none;
  }
}

.markdown-body table {
  border-collapse: collapse;
  width: 100%;
}

.markdown-body th,
.markdown-body td {
  border: 1px solid #ccc; /* 设置边框颜色 */
  padding: 8px; /* 设置单元格内边距 */
  text-align: left; /* 设置文本对齐方式 */
}

.markdown-body th {
  background-color: #f2f2f2; /* 设置表头背景色 */
}

.h-full {
  height: 100%;
}
