import React, { forwardRef, useImperativeHandle } from 'react';
import { Card, Tag, Divider } from 'antd';
import './index.less';
import { ExclamationCircleFilled, LikeFilled } from '@ant-design/icons';

export interface AnalysisModuleRef {
  getAnalysisData: () => any;
}

export interface AnalysisModuleProps {
  viewData?: any;
}

const AnalysisModule = forwardRef<AnalysisModuleRef, AnalysisModuleProps>(({viewData}, ref) => {
  console.log(viewData, '--------viewData简历分析组件')
  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    getAnalysisData: () => {
      return {...viewData}
    }
  }));

  // 检查是否有数据
  const hasData = viewData && Object.keys(viewData).length > 0;

  return (
    <div className="analysis-module">
      <Card className="resume-analysis-card">
        {!hasData ? (
          <div className="no-data">
            <p>暂无分析数据，请等待分析完成...</p>
          </div>
        ) : (
          <>
          <div className="candidate-info">
          <div style={{display: 'flex'}}>
            <h3>{viewData?.basic_info?.name || '未知姓名'}</h3>
            <div className="candidate-tags">
              {/* 根据风险情况动态生成标签 */}
              {/* {viewData?.risk_sentences?.map((risk: string, index: number) => {
                const isHighRisk = risk.includes('风险') || risk.includes('空档期') || risk.includes('不一致');
                return (
                  <Tag 
                    key={index} 
                    color={isHighRisk ? 'red' : 'green'}
                  >
                    {isHighRisk ? '需关注' : '正常'}
                  </Tag>
                );
              })} */}
            </div>
          </div>
          
          <p className="candidate-details">
            {viewData?.basic_info?.age || '未知'}岁 | {viewData?.basic_info?.gender || '未知'} | {viewData?.basic_info?.phone || '未知'} | {viewData?.basic_info?.email || '未知'} | {viewData?.basic_info?.total_experience_years || '未知'}年工作经验 | {viewData?.basic_info?.highest_education?.degree_level || '未知'}
          </p>
        </div>
        
        <Divider />
        
        <div className="analysis-section">
          <h4><LikeFilled style={{color: '#3173ff', marginRight: '8px'}} />简历亮点</h4>
          <ul className="highlights-list">
            {viewData?.highlights?.map((highlight: string, index: number) => (
              <li key={index}>{highlight}</li>
            )) || (
              <li>暂无亮点信息</li>
            )}
          </ul>
        </div>
        
        <div className="analysis-section">
          <h4><ExclamationCircleFilled style={{color: '#ffa727', marginRight: '8px'}} /> 风险提示</h4>
          <ul className="risks-list">
            {viewData?.risk_sentences?.map((risk: string, index: number) => (
              <li key={index} className="risk-item">{risk}</li>
            )) || (
              <li>暂无风险提示</li>
            )}
          </ul>
        </div>
          </>
        )}
      </Card>
    </div>
  );
});

AnalysisModule.displayName = 'AnalysisModule';

export default AnalysisModule;
