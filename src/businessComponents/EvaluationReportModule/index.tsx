import React, {
  useState,
  useRef,
  useImperative<PERSON>andle,
  forwardRef,
  useEffect,
} from 'react'
import { Tabs, Spin, Card } from 'antd'
import type { TabsProps } from 'antd'
import { getToken, getUserInfo } from '@/utils/auth'
import { cacheGet } from '@/utils/cacheUtil'
import useSSEChat from '@/hooks/useSSEChat'
import TabContainer, { TabItem } from '../TabContainer'
import './index.less'
import ReactMarkdown from "react-markdown";
import remarkGfm from 'remark-gfm'
import RemarkBreaks from 'remark-breaks'
import RemarkMath from 'remark-math'

import StreamTypewriter from '@/component/StreamTypewriter'


export interface EvaluationReportModuleRef {
  getMentionsData: () => any
}

interface EvaluationReportModuleProps {
  pageInfo: any
  reportData: any
  globalLoading?: boolean
  setGlobalLoading?: (loading: boolean) => void
  fileData: any[]
  agentId: string
  currentEchoData: any
  leftTabs: any[]
  stepIndex: number
  onCallParent: (type: string, data?: any) => void
}

const EvaluationReportModule = forwardRef<
  EvaluationReportModuleRef,
  EvaluationReportModuleProps
>(({ pageInfo, reportData, globalLoading, setGlobalLoading, fileData, agentId,currentEchoData,leftTabs,stepIndex,onCallParent}, ref) => {
  const sseChat = useSSEChat()

  // 全局加载状态管理
  // const [globalLoading, setGlobalLoading] = useState(false);
  const [reviewData, setReviewData] = useState<any>([]) // 输入评审弹框数据
  const [outputReviewData, setOutputReviewData] = useState<any>([]) // 输出评审数据
  const [reportContent, setReportContent] = useState<string>('') // 报告内容
  const scrollRef = useRef<HTMLDivElement>(null)

  // 自动开始逻辑：当组件挂载且有开始数据时，根据当前步骤自动调用相应的接口
  useEffect(() => {
    // console.log('pageInfo:', pageInfo)
    // console.log('reportData:', reportData)
    // console.log('currentEchoData:', currentEchoData)
    // console.log('leftTabs:', leftTabs)
    if(currentEchoData && Object.keys(currentEchoData).length > 0 && currentEchoData.reportContent) {
      setReportContent(currentEchoData.reportContent || '')
    }
    // else {
    //   console.log('没有回显内容')
    //   handleAutoStart()

    // }
  }, [])

  // 自动开始智能出题
  const handleAutoStart = async () => {
    console.log('开始进来了:', reportData)
    if (agentId && reportData.resumeAnalysisResult && reportData.transcript_md) {
      console.log('开始自动生成评估报告')
      const query = `${reportData.resumeAnalysisResult}, ${reportData.transcript_md}`
      await callUnifiedAPI(agentId, query)
    }
  }

  // 统一的API接口调用方法
  const callUnifiedAPI = async (agentId: string, query: string) => {
    try {
      const tokenInfo = await getToken()
      const userInfo = await getUserInfo()
      const tenantId = cacheGet('tenantId')
      setGlobalLoading?.(true)

      // 调用流式接口
      sseChat.start({
        url: '/dify/broker/agent/stream',
        headers: {
          'Content-Type': 'application/json',
          Token: tokenInfo || '',
        },
        body: {
          insId: '1',
          bizType: 'app:agent',
          bizId: agentId,
          agentId: agentId,
          path: '/chat-messages',
          // query: startData.query || '',
          query: '1',
          difyJson: {
            inputs: {
              Token: tokenInfo || '',
              tenantid: tenantId || '',
              pageInfo: JSON.stringify(pageInfo),
            },
            response_mode: 'streaming',
            user: userInfo?.id || 'anonymous',
            conversation_id: '',
            query: query,
          },
        },
        query: {},
        message: '1',
        onMessage: (message) => {
          console.log(`接口返回消息:`, message)
          // 去掉 <think> 标签内容
          let cleanStr = message.replace(/<think>[\s\S]*?<\/think>/g, '').trim()
          setReportContent(cleanStr)

        },
        onFinished: (res) => {
          let reportContent = res.replace(/<think>[\s\S]*?<\/think>/g, '').trim()
          console.log(`接口完成:`, res)
          setGlobalLoading?.(false)
          // 先注掉单元输入输出评审
          onCallParent('输出', reportContent)
        },
      })
    } catch (error) {
      console.error(`接口调用失败:`, error)
      setGlobalLoading?.(false)
    }
  }
  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    getMentionsData: () => {
      return {
        // outputReviewData: outputReviewData,
        // info: resumeAnalysisResult +  startData?.query,
        // jobMatchResult: jobMatchResult,
        // basicAnalysisResult: basicAnalysisResult,
        reportContent: reportContent,
      }
    },
    handleAutoStart: handleAutoStart,
  }))

  const items: TabsProps['items'] = [
    {
      key: '1',
      label: '简历预览',
      children: (
        <embed
          style={{
            width: '100%',
            height: '100%',
            minHeight: 'calc(100vh - 265px)',
          }}
          type="application/pdf"
          src={fileData[0].url + '#toolbar=0&navpanes=0&scrollbar=0'}
        />
      ),
    },
  ]

  return (
    <div className="evaluation-report-module">
      <div className="interview-container">
        {/* 左侧动态Tab内容 */}
        <div className="left-section">
          {/* {leftTabs && leftTabs.length > 0 ? (
              <TabContainer
                ref={tabContainerRef}
                tabs={leftTabs}
                currentStep={currentStep}
                className="left-tabs"
              />
            ) : (
              <div className="default-left-content">
                <h3>左侧内容区域</h3>
                <p>请配置leftTabs来显示具体内容</p>
              </div>
            )} */}

          {/* <Tabs
            items={items}
            className="dynamic-tabs"
            type="card"
            size="small"
          /> */}
          <TabContainer
            tabs={leftTabs}
            stepIndex={stepIndex || 0}
            className="left-tabs"
          />
        </div>

        {/* 右侧内容区域 */}
        <div className="right-section">
          <div className="report-content">
            <div className="component-wrapper">
              <div className="interview-report">
                <Card title="评估报告" className="report-card">
                  <div className="report-stream-content" ref={scrollRef}>
                    {globalLoading ? (
                      <StreamTypewriter
                        text={reportContent}
                        speed={30}
                        className="report-text"
                        onchange={() => {
                          scrollRef.current?.scrollTo({
                            top: scrollRef.current.scrollHeight,
                            behavior: "smooth",
                          })
                        }}
                      />
                    ) : (
                      <ReactMarkdown
                        remarkPlugins={[remarkGfm, RemarkBreaks, RemarkMath]}
                        className="markdown-content"
                      >
                        {reportContent}
                      </ReactMarkdown>
                    )}
                  </div>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
})

EvaluationReportModule.displayName = 'EvaluationReportModule'

export default EvaluationReportModule
