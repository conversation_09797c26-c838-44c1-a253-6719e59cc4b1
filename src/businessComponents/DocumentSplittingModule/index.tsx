/** 模板生成工具 */
import {
  But<PERSON>,
  Card,
  Flex,
  Form,
  Input,
  message,
  Upload,
  Select,
  Typography,
} from "antd";
import {
  useState,
  useImperativeHandle,
  forwardRef,
  useEffect,
  useRef,
} from "react";
import { DeleteOutlined, InboxOutlined } from "@ant-design/icons";
import { convertFileToPDF, uploadChatFile, splitFile } from "@/api/public";
import { fileToBase64 } from "@/utils/common";
import uploadIcon from "@/assets/images/public/upload.png";
import { getToken, getUserInfo } from "@/utils/auth";
import { cacheGet } from "@/utils/cacheUtil";
// import { contractSceneSet } from "@/api/contractSceneSet";

import "./index.less";
interface MentionsComponentProps {
  agentId: string;
  setGlobalLoading?: (loading: boolean) => void;
  splittingData: {
    files?: []; // 上传的文件信息// 需要回显时传进来
    originalFile?: any; // 原始文件信息
    splitType?: string; // 切分类型
  };
}
export interface MentionsComponentRef {
  triggerSplit: (data: any) => void;
}
const splitTypeOptions = [{ label: "按章节切分", value: "按章节切分" }];
const DocumentSplittingModule = forwardRef<
  MentionsComponentRef,
  MentionsComponentProps
>(
  (
    {
      setGlobalLoading,
      agentId,
      splittingData = { files: [], originalFile: "", splitType: "" },
    },
    ref
  ) => {
    const [uploadedFiles, setUploadedFiles] = useState([]); // 上传的文件信息
    const [originalFile, setOriginalFile] = useState<File>();
    const [splitType, setSplitType] = useState(splitTypeOptions[0].value);
    const [chunks, setChunks] = useState<any>([]);
    const isFirstLoad = useRef(true); // 每次回来组件会重新加载，所以要设置一个开关

    // 父组件回到第一步时回显
    useEffect(() => {
      if (isFirstLoad.current) {
        // 防止重复加载  回显
        if (splittingData.splitType) {
          setSplitType(splittingData.splitType);
        }
        if (splittingData.files && splittingData.files.length > 0) {
          setUploadedFiles(splittingData.files);
          setOriginalFile(splittingData.originalFile);
        }
      }
      isFirstLoad.current = false;
    }, [
      splittingData.files,
      splittingData.originalFile,
      splittingData.splitType,
    ]); // 文件切割

    const handleGenerationSplit = async () => {
      if (uploadedFiles.length === 0) {
        message.error("请先上传文件");
        return;
      } else {
        setChunks([]);
        setGlobalLoading?.(true);
        const chunks = await splitDataFiles(uploadedFiles, originalFile);
        setGlobalLoading?.(false);
        if (chunks && chunks.length > 0) {
          setChunks(chunks);
          return chunks;
        } else {
          return [];
        }
      }
    };

    // 添加文件切割方法
    const splitDataFiles = async (files: any, originalFile: any) => {
      if (!files[0].id || !originalFile) {
        message.error("没有可切割的模板文件");
        return [];
      }

      try {
        const chunks: any = [];

        // 切割模板文件
        console.log("模板文件信息:", files);

        try {
          // 使用subwayReport.ts中的splitFile方法
          const fileChunks = await splitFile(originalFile);
          // 过滤掉content为空的chunk
          const filteredChunks = fileChunks?.chunkInfo?.filter(
            (chunk: any) => chunk.text && chunk.text.trim() !== ""
          );
          if (filteredChunks.length > 0) {
            chunks.push(...filteredChunks);
          } else {
            // message.warning(`文件 ${uploadedFile.name} 切割未完成，使用原始文件`)
          }
        } catch (splitError: any) {
          console.error(`切割文件 ${files[0].name} 失败:`, splitError);
          // message.warning(`文件 ${uploadedFile.name} 切割失败: ${splitError.message || '未知错误'}，将使用原始文件`)
        }

        return chunks;
      } catch (error: any) {
        console.error("文件处理失败:", error);
        // message.error(`文件处理失败: ${error.message || '未知错误'}，将使用原始文件`)
        return [];
      }
    };
    const beforeUpload = async (file: File) => {
      const originalFilename = file.name.substring(
        0,
        file.name.lastIndexOf(".")
      );
      const originalFileExt = file.name
        .substring(file.name.lastIndexOf(".") + 1)
        ?.toLowerCase();
      setGlobalLoading?.(true);
      if (["docx", "doc"].includes(originalFileExt)) {
        console.log(21331);
        convertFileToPDF(file).then(async (response) => {
          if (response["status"] && response["status"] !== 200) {
            setGlobalLoading?.(false);
            message.open({
              key: "uploading",
              type: "error",
              content: "文件处理异常，请稍后重试",
              duration: 1,
            });
          } else if ("blob" in response) {
            const userInfo = await getUserInfo();
            const blob = await response.blob();
            const pdfFile = new File([blob], `${originalFilename}.pdf`, {
              type: "application/pdf",
            });
            console.log(file, 123);
            const fileData = {
              fileName: file.name,
              fileStr: await fileToBase64(file),
              path: "/files/upload",
              agentId,
              user: userInfo?.id,
              libName: file.name,
              libDesc: "",
              flag: "file",
            };
            uploadChatFile(fileData).then(async (response: any) => {
              setGlobalLoading?.(false);
              if (response.code == 200) {
                setUploadedFiles([
                  { url: URL.createObjectURL(pdfFile), ...response.data },
                ]);
                setOriginalFile(file);
                message.open({
                  key: "uploading",
                  type: "success",
                  content: "文件上传成功",
                  duration: 1,
                });
              } else {
                message.open({
                  key: "uploading",
                  type: "error",
                  content: "文件上传失败",
                  duration: 1,
                });
              }
            });
          }
        });
      }
    };
    // const handleDelete = (fileId: string) => {
    //   setUploadedFiles((prevFiles) =>
    //     prevFiles.filter((file) => file.id !== fileId)
    //   );
    // };
    useImperativeHandle(ref, () => ({
      triggerSplit: async () => {
        // 先切割
        const chunks = await handleGenerationSplit();
        // 返回给父组件
        return {
          files: uploadedFiles, // 上传的文件信息
          chunks: chunks, // 切割结果
          originalFile: originalFile, // 原始文件
        };
      }, // 暴露给父组件的方法
    }));
    return (
      <div className="document-splitting-module">
        <Flex
          vertical
          gap="middle"
          style={{ flex: 1 }}
          className="splitting-form"
        >
          <Form layout="vertical">
            {/* <Form.Item>
                <Upload.Dragger
                  showUploadList={false}
                  multiple={false}
                  beforeUpload={beforeUpload}
                  accept=".docx"
                  fileList={uploadedFiles}
                >
                  <span className="contract-scene-set-upload-icon">
                    <InboxOutlined />
                  </span>
                  <br />
                  <span className="contract-scene-set-upload-text">
                    点击或将文档拖拽到这里上传
                  </span>
                  <br />
                  <span className="contract-scene-set-upload-hint">
                    当前仅支持word格式
                  </span>
                </Upload.Dragger>

                {uploadedFiles.length > 0 && (
                  <div className="file-list-contract">
                    {uploadedFiles.map((file) => (
                      <div key={file.id} className="file-item">
                        <span>{file.name}</span>
                        <DeleteOutlined
                          onClick={() => handleDelete(file.id)}
                          style={{ cursor: "pointer", flex: "0 0 20px" }}
                        />
                      </div>
                    ))}
                  </div>
                )}
              </Form.Item> */}
            <Form.Item>
              <Flex className="upload">
                <Flex vertical gap="4" style={{ flex: 1 }}>
                  <Typography.Title className="file-title" level={5}>
                    合同上传
                  </Typography.Title>
                  <Upload.Dragger
                    showUploadList={false}
                    multiple={false}
                    beforeUpload={beforeUpload}
                    accept=".docx"
                    fileList={uploadedFiles}
                  >
                    <img
                      src={uploadIcon}
                      style={{ width: 45, margin: "0px auto" }}
                    />
                    <p className="ant-upload-hint">
                      {uploadedFiles && uploadedFiles.length > 0 ? (
                        <span>{uploadedFiles[0].name}</span>
                      ) : (
                        <span>点击或将文件拖到此处上传</span>
                      )}
                      <span>支持word格式文档</span>
                    </p>
                  </Upload.Dragger>
                </Flex>
              </Flex>
            </Form.Item>
            <Form.Item
              label={
                <>
                  <span style={{ color: "#000", fontWeight: "bold" }}>
                    选择拆分方式
                  </span>
                </>
              }
            >
              <Select
                value={splitType}
                options={splitTypeOptions}
                onChange={setSplitType}
                placeholder="请选择拆分方式"
              />
            </Form.Item>
          </Form>
          <Button
            size="large"
            type="primary"
            className="upload-btn"
            onClick={() => {
              if (uploadedFiles.length === 0) {
                message.error("请先上传文件");
              } else {
                handleGenerationSplit();
              }
            }}
          >
            下一步
          </Button>
        </Flex>
      </div>
    );
  }
);
// 添加 displayName
DocumentSplittingModule.displayName = "DocumentSplittingModule";
export default DocumentSplittingModule;
