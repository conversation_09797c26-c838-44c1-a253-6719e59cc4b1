/** SSE公共Hook */
import { fetchEventSource } from "@microsoft/fetch-event-source";
import type {
  AgentError,
  AgentThoughtItem,
  VisibleFileItem,
} from "@/types/chat";

declare interface SSEArguments {
  url: string;
  method: string;
  params?: Record<string, object>;
  body?: Record<string, string | object>;
  headers?: Record<string, string>;
  onStart?: OnSSEStart;
  onOpen?: object;
  onMessage?: OnSSEMessage;
  onAgentThought?: OnSSEAgentThought;
  onMessageFile?: OnSSEMessageFile;
  onClose?: OnSSEClose;
  onFinished?: OnSSEFinished;
  onError?: OnSSEError;
  onMessageEnd?: OnSSEMessageEnd;
}

declare type OnSSEStart = () => void;
declare type OnSSEMessage = (message: MessageItem) => void;
declare type OnSSEAgentThought = (thought: AgentThoughtItem) => void;
declare type OnSSEMessageFile = (file: VisibleFileItem) => void;
declare type OnSSEClose = () => void;
declare type OnSSEError = (err: AgentError) => void;
declare type OnSSEFinished = () => void;
declare type OnSSEMessageEnd = (data?: any) => void;
declare interface MessageItem {
  task_id: string;
  id: string;
  answer?: string;
  create_at: number;
  event: string;
  conversation_id?: string;
  message_id?: string;
  status?: number;
  code?: string;
}
function useEventSource({
  url,
  method,
  body,
  headers,
  onStart,
  onMessage,
  onAgentThought,
  onMessageFile,
  onMessageEnd,
  onClose,
  onFinished,
  onError,
}: SSEArguments) {
  let connected = false;
  const abortController = new AbortController();
  const start = (customBody?: Record<string, string | object>) => {
    connected = true;
    const signal = abortController.signal;
    fetchEventSource(url, {
      method,
      headers,
      body: JSON.stringify(Object.assign({}, body, customBody)),
      fetch,
      signal,
      openWhenHidden: true,
      onopen(response: Response): Promise<void> {
        if (!response.ok) {
          onError?.(response);
          abortController?.abort();
          throw new Error(`${response.status}: ${response.statusText}`);
        }
        onStart?.();
        return Promise.resolve();
      },
      onmessage(event): void {
        if (!event.data || event.data === "ping") {
          return;
        }
        const parsedData: Record<string, any> = JSON.parse(event.data);
        switch (parsedData.event) {
          case "message":
          case "agent_message":
            onMessage?.(parsedData as MessageItem);
            break;
          case "agent_thought":
            onAgentThought?.(JSON.parse(event.data) as AgentThoughtItem);
            break;
          case "message_file":
            onMessageFile?.(JSON.parse(event.data) as VisibleFileItem);
            break;
          case "message_end":
            connected = false;
            onMessageEnd?.(parsedData);
            break;
          case "error":
            connected = false;
            // eslint-disable-next-line no-case-declarations
            const err = JSON.parse(event.data) as AgentError;
            onError?.(err);
            throw new Error(`网络请求出现错误：${err.code}`);
        }
      },
      onerror(err): void {
        // setConnected(false);
        connected = false;
        onError?.(err);
        throw err;
      },
      onclose(): void {
        // setConnected(false);
        connected = false;
        onFinished?.();
      },
    });
  };

  const stop = () => {
    abortController?.abort();
    connected = false;
    onClose?.();
  };

  return { start, stop, connected };
}

export default useEventSource;
