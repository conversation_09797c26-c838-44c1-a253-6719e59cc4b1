@import '@/assets/styles/variables.less';
.document-splitting-module {
  width: 800px;
  display: flex;
  justify-content: center;
  flex-shrink: 0;
  padding: 20px 50px;
  margin: 0px auto;
  border-radius: var(--ant-border-radius-lg);
  border: 1px solid #EEEEEE;

  .contract-scene-set-upload-icon {
    margin-bottom: 16px;
    font-size: 48px;
    color: #1890ff;
    transition: transform 0.3s ease;

    &:hover {
      transform: scale(1.05);
    }
  }

  .markdown-body ol,
  .markdown-body ul {
    padding-left: 20px;
  }

  .file-list-contract {
    margin-top: 16px;
    max-height: 200px;
    overflow-y: auto;
    padding-right: 4px;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #d9d9d9;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background-color: #f5f5f5;
      border-radius: 3px;
    }

    .file-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 12px;
      background: #f8f9fa;
      border-radius: 6px;
      margin-bottom: 8px;
      transition: all 0.3s ease;

      &:hover {
        background: #f0f2f5;
      }

      span {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
  .splitting-form{
    .ant-select-selector{                                                       
      border-color:#EEEEEE;
    }
    .upload-btn{
      width: 120px;
      margin: 0px auto;
      background: @linear-gradient-1;
    }
  }
  .ant-upload-drag {
    border: 1px dashed #E0EAFF;
    border-radius: 12px;
    padding: 40px 0px;
    background: #FFFFFF;
  }
  .ant-card-body{
    box-shadow: 0px 0px 20px 0px rgba(89, 143, 200, 0.05);
    border-radius: 12px;
    padding: 30px;
  }
  .file-title{
    font-family: Alibaba PuHuiTi 3.0;
    font-size: 16px;
    font-weight: 500;
    line-height: 22px;
    color: #333333;
    margin-bottom: 20px;
  }
  .ant-upload-drag-icon {
    margin-bottom: 0;
    font-size: 36px;
    color: #1890ff;
    transition: transform 0.3s ease;
  }

  .ant-upload-text,
  .ant-upload-hint {
    font-size: 14px;
    span:nth-child(1){
      display: block;
      font-family: Alibaba PuHuiTi 3.0;
      font-size: 16px;
      font-weight: 500;
      line-height: 22px;
      color: #333333;
    }
    span:nth-child(2){
      display: block;
      opacity: 0.6;
      font-family: Alibaba PuHuiTi 3.0;
      font-size: 12px;
      font-weight: normal;
      margin-top: 5px;
    }
  }
  .file-list-contract {
    margin-top: 16px;
    max-height: 200px;
    overflow-y: auto;
    padding-right: 4px;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #d9d9d9;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background-color: #f5f5f5;
      border-radius: 3px;
    }

    .file-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 12px;
      background: #f8f9fa;
      border-radius: 6px;
      margin-bottom: 8px;
      transition: all 0.3s ease;

      &:hover {
        background: #f0f2f5;
      }

      span {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-right: 12px;
      }
    }
  }
}
.contract-scene-set-toolbar {
  background: linear-gradient(180deg, rgba(189, 225, 255, 0.4) 0%, rgba(224, 242, 255, 0) 100%);
  border-radius: 0.5rem 0.5rem 0 0;
  padding: 12px 24px;

  .title-text {
    color: transparent;
    background: linear-gradient(116deg, #1888ff 16%, #2f54eb 88%);
    background-clip: text;
    -webkit-background-clip: text;
    user-select: none;
    font-size: 30px;
    font-weight: bold;
  }
}
