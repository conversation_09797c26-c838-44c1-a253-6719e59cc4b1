.evaluation-report-module {
  width: 100%;
  height: 100%;
  // background: #f5f5f5;
  padding: 50px 20px 20px;
  
  .interview-container {
    gap: 30px;
    display: flex;
    height: 100%;
    // min-height: 600px;
    
    .left-section {
      // flex: 1;
      width: 40%;
      background: white;
      border-radius: 8px;
      // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      
      .left-tabs {
        height: 100%;
        
        .ant-tabs-content-holder {
          height: calc(100% - 46px);
          overflow-y: auto;
        }
        
        .ant-tabs-tabpane {
          height: 100%;
          // padding: 20px;
        }
      }
    }
    
    .right-section {
      flex: 1;
      background: white;
      border-radius: 8px;
      // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      display: flex;
      flex-direction: column;
      
      .right-tabs {
        flex: 1; // Changed from height: 100%
      }

      .report-content {
        // padding: 10px;
        height: 100%;
        
        .component-wrapper {
          height: 100%;
        }
      }
      
      // 报告内容样式
      .report-content {
        .interview-report {
          // max-width: 800px;
          margin: 0 auto;
          padding: 0 20px;
          height: 100%;
        }
        .report-card {
          border: none;
          height: 100%;
          

          .ant-card-head {
            border-bottom: none;
            padding: 0;
          }
          .ant-card-body {
            height: 100%;
            padding: 0;
          }
          .report-stream-content {
            height: calc(100% - 56px);
            overflow-y: auto;
          }
          .report-stream-content::-webkit-scrollbar {
            display: none;
          }
        }
        .report-text {
          line-height: 1.8;
          font-size: 14px;
          color: #262626;
          white-space: pre-wrap;
          word-break: break-word;
        }
        .markdown-content {
          line-height: 1.8;
          font-size: 14px;
          color: #262626;
    
          h1, h2, h3, h4, h5, h6 {
            color: #1890ff;
            margin-top: 24px;
            margin-bottom: 16px;
            font-weight: 600;
          }
    
          h1 {
            font-size: 24px;
            // border-bottom: 2px solid #f0f0f0;
            // padding-bottom: 8px;
          }
    
          h2 {
            font-size: 20px;
          }
    
          h3 {
            font-size: 18px;
          }
    
          p {
            margin-bottom: 16px;
            line-height: 1.8;
          }
    
          ul, ol {
            margin-bottom: 16px;
            padding-left: 24px;
    
            li {
              margin-bottom: 8px;
              line-height: 1.6;
            }
          }
    
          blockquote {
            border-left: 4px solid #1890ff;
            padding-left: 16px;
            margin: 16px 0;
            background-color: #f6ffed;
            padding: 16px;
            border-radius: 6px;
          }
    
          code {
            background-color: #f5f5f5;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
          }
    
          pre {
            background-color: #f5f5f5;
            padding: 16px;
            border-radius: 6px;
            overflow-x: auto;
            margin: 16px 0;
    
            code {
              background: none;
              padding: 0;
            }
          }
    
          table {
            width: 100%;
            border-collapse: collapse;
            margin: 16px 0;
    
            th, td {
              border: 1px solid #d9d9d9;
              padding: 8px 12px;
              text-align: left;
            }
    
            th {
              background-color: #fafafa;
              font-weight: 600;
            }
          }
        }
      }
    }
  }
}
