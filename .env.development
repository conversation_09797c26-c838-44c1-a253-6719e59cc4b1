
# # 服务前缀
# # 本地运行、桌面端构建时使用的配置, 其他端不用修改该配置
# VITE_BASE_API: http://192.168.113.18:6012
# # 本地运行、桌面端构建时使用的配置, 其他端不用修改该配置
# VITE_BASE_AI_API: http://192.168.113.90:88/v1 
# VITE_API_HEADER_KEY: OVERTOKEN
# VITE_API_BASE_PUB: /langwell-api/langwell-pub-server
# VITE_API_BASE_SYS: /langwell-api/langwell-sys-server
# VITE_API_BASE_NOTE: /langwell-api/langwell-notes-server
# VITE_API_BASE_INS: /langwell-api/langwell-ins-server
# VITE_API_BASE_AI: /langwell-api/langwell-ai-server
# VITE_API_BASE_DOC: /langwell-api/langwell-doc-server
# VITE_USERINFO_BASS: /lamp-api
# VITE_DIFY: /dify
# VITE_NODE_ENV: development
# VITE_API_BASE_SPLIT: /common-split-api
# BASE: ''
# # 语音识别
# VITE_API_BASE_VOICE=/voice-api

# # Supabase配置
# VITE_SUPABASE_URL=https://yciseftvlsnkbrivoowd.supabase.co
# VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InljaXNlZnR2bHNua2JyaXZvb3dkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI4MjkxNzQsImV4cCI6MjA2ODQwNTE3NH0.JtX7lcWR2bQ16bSpvrWtW5OoTYEqI4akrrlLISksaRs
# # 保利威配置
# VITE_POLYV_USER_ID=9548bf10a8
# VITE_POLYV_API_KEY=h9aojik3pd
# VITE_POLYV_SECRET=7c1158e28cdb49b8892d2839dd1c6e24


# 服务前缀
# 本地运行、桌面端构建时使用的配置, 其他端不用修改该配置
VITE_BASE_API: https://copilot.sino-bridge.com:85
# 本地运行、桌面端构建时使用的配置, 其他端不用修改该配置
VITE_BASE_AI_API: https://copilot.sino-bridge.com:90/v1,
VITE_API_HEADER_KEY: SA-TOKEN
VITE_API_BASE_PUB: /langwell-api/langwell-pub-server
VITE_API_BASE_SYS: /langwell-api/langwell-sys-server
VITE_API_BASE_NOTE: /langwell-api/langwell-notes-server
VITE_API_BASE_INS: /langwell-api/langwell-ins-server
VITE_API_BASE_AI: /langwell-api/langwell-ai-server
VITE_API_BASE_DOC: /langwell-api/langwell-doc-server
VITE_API_BASE_SPLIT: /common-split-api
VITE_SCENE_INPUT: 5aa01041-7e2b-4221-ab8d-a11d8db3ca8e
VITE_SCENE_OUTPUT: a40e5a31-da0a-4b70-91c3-1201baebfc9b
VITE_UNIT_INPUT: 1f72ff5f-d6b8-48e7-90ad-294cf58ab136
VITE_UNIT_OUTPUT: d00b1144-ad49-4c60-ac49-1cd16cf4bfeb
VITE_SCENE_PLANNING: 871bc0ed-4d86-4b87-aa3c-2bc187e5da25
VITE_ENABLE_LAB_FEATURES: false
VITE_MQTT_PROTOCOL: wss
VITE_MQTT_HOST: copilot.sino-bridge.com
VITE_MQTT_PORT: 85
VITE_MQTT_PATH: /mqttSocket/mqtt
VITE_USERINFO_BASS: https://copilot.sino-bridge.com:84/api
VITE_OFFICIAL_URL: https://copilot.sino-bridge.com:85
VITE_FILE_PREFIX: https://copilot.sino-bridge.com:90
VITE_DIFY: /dify
VITE_NODE_ENV: production
BASE: ''

# 语音识别
VITE_API_BASE_VOICE=/voice-api
# Supabase配置
VITE_SUPABASE_URL=https://yciseftvlsnkbrivoowd.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InljaXNlZnR2bHNua2JyaXZvb3dkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI4MjkxNzQsImV4cCI6MjA2ODQwNTE3NH0.JtX7lcWR2bQ16bSpvrWtW5OoTYEqI4akrrlLISksaRs
# 保利威视配置
VITE_POLYV_USER_ID=9548bf10a8
VITE_POLYV_API_KEY=h9aojik3pd
VITE_POLYV_SECRET=7c1158e28cdb49b8892d2839dd1c6e24
