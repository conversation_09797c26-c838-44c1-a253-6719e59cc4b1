.interview-module {
  width: 100%;
  height: 100%;
  // background: #f5f5f5;
  padding: 20px;
  
  .interview-container {
    gap: 30px;
    display: flex;
    height: 100%;
    // min-height: 600px;
    
    .left-section {
      // flex: 1;
      width: 40%;
      background: white;
      border-radius: 8px;
      // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      
      .left-tabs {
        height: 100%;
        
        .ant-tabs-content-holder {
          height: calc(100% - 46px);
          overflow-y: auto;
        }
        
        .ant-tabs-tabpane {
          height: 100%;
          // padding: 20px;
        }
      }
    }
    
    .right-section {
      flex: 1;
      background: white;
      border-radius: 8px;
      // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      display: flex;
      flex-direction: column;
      
      .right-tabs {
        flex: 1; // Changed from height: 100%
      }

      // Tab内容区域样式
      .analysis-content {
        padding: 10px;
        height: 100%;
        overflow-y: auto;
        
        .component-wrapper {
          height: 100%;
        }
      }

      .analysis-content::-webkit-scrollbar {
        display: none;
      }
      
      // 分析内容样式
      .analysis-content {
        .component-wrapper {
          // border-left: 4px solid #52c41a;
        }
        
        // 加载状态样式
                    .loading-state {
              display: flex;
              justify-content: center;
              align-items: center;
              min-height: 300px;
              text-align: center;
              
              .loading-text {
                h3 {
                  color: #1890ff;
                  margin-bottom: 16px;
                  font-size: 20px;
                }
                
                p {
                  color: #666;
                  margin-bottom: 24px;
                  font-size: 14px;
                }
              }
            }
        
        // 分析结果样式
        .analysis-result {
          padding: 20px;
          
          h3 {
            color: #52c41a;
            margin-bottom: 16px;
            font-size: 18px;
          }
          
          .result-content {
            background: #f8f9fa;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
            padding: 16px;
            
            pre {
              margin: 0;
              white-space: pre-wrap;
              word-wrap: break-word;
              font-size: 12px;
              color: #333;
            }
          }
        }
      }
      
      // 匹配内容样式
      .matching-content {
        .component-wrapper {
          // border-left: 4px solid #1890ff;
        }
      }
    }
  }
}
