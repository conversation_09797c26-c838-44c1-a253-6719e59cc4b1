.knowledge-card-chat {
  background: var(--ant-color-bg-base);
  padding: var(--ant-padding-xs);
  border: 1px solid var(--ant-color-border-secondary);
  border-radius: var(--ant-border-radius);
  flex: 1;
  cursor: pointer;
  .btn-knowledge-icon{
    width: 20px !important;
    height:20px !important;
    border: 1px solid #fff;
    box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.12)
  }
  .knowledge-checkbox{
    opacity: 0
  }
  .knowledge-card-info{
    display: flex;
  }
  .knowledge-name{
    font-weight: bold;
    font-size: var(--ant-font-size);
    line-height: 18px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    word-break: break-all;
    -webkit-box-orient: vertical;
  }
  .knowledge-card-opeate{
    justify-content: flex-start;
  }
  &:hover{
    border: 1px solid var(--ant-color-primary-border-hover);
    .knowledge-checkbox{
      opacity: 1 !important;
    }
  }
}