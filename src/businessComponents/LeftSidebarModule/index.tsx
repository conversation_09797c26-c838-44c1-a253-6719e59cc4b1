import React, { useState } from 'react';
import { Button } from 'antd';
import { MenuOutlined, CloseOutlined } from '@ant-design/icons';
import './index.less';
import SessionSelector from "../SessionSelectorMoudule";

interface LeftSidebarModuleProps {
  sessionId?: string;
  onSessionSelect?: (sessionId: string | null, sessionData?: any) => void;
  onSidebarToggle?: (visible: boolean) => void;
}

const LeftSidebarModule: React.FC<LeftSidebarModuleProps> = ({ 
  sessionId = '1', 
  onSessionSelect = () => {},
  onSidebarToggle = () => {}
}) => {
  const [sidebarVisible, setSidebarVisible] = useState(false);

  const handleSessionSelect = (newSessionId: string | null, sessionData?: any) => {
    onSessionSelect(newSessionId, sessionData);
    if (newSessionId) {
      setSidebarVisible(false);
      onSidebarToggle(false);
    }
  };

  const handleSidebarToggle = (visible: boolean) => {
    setSidebarVisible(visible);
    onSidebarToggle(visible);
  };

  return (
    <div className="left-sidebar-module">
      {/* 侧边栏切换按钮 */}
      {!sidebarVisible && (
        <Button
          className={`sidebar-toggle ${sidebarVisible ? "sidebar-open" : ""}`}
          icon={<MenuOutlined />}
          onClick={() => handleSidebarToggle(true)}
          title="会话管理"
        />
      )}

      {/* 会话管理侧边栏 */}
      <div className={`session-sidebar ${sidebarVisible ? "visible" : ""}`}>
        <div className="sidebar-header">
          <span className="sidebar-title">会话管理</span>
          <button
            className="close-btn"
            onClick={() => handleSidebarToggle(false)}
            title="关闭"
          >
            <CloseOutlined />
          </button>
        </div>
        <div className="sidebar-content">
          <SessionSelector
            onSessionSelect={handleSessionSelect}
            currentSessionId={sessionId}
          />
        </div>
      </div>
    </div>
  );
};

export default LeftSidebarModule;