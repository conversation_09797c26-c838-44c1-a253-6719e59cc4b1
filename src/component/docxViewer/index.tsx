// import { useEffect, useRef } from "react";
// import { renderAsync } from "docx-preview";

// interface DocxPreviewProps {
//   file: File | ArrayBuffer | Blob | null; // 支持 File / Blob / ArrayBuffer
//   className?: string; // 自定义 className
//   ignoreCss?: boolean; // 是否忽略 docx-preview 自带样式
//   style?: React.CSSProperties; // 容器样式
// }

// const DocxPreview: React.FC<DocxPreviewProps> = ({
//   file,
//   className = "my-docx-preview",
//   ignoreCss = false,
//   style = { width: "100%", height: "400px" },
// }) => {
//   const previewRef = useRef<HTMLDivElement | null>(null);

//   useEffect(() => {
//     if (!file || !previewRef.current) return;

//     // 清空上一次渲染内容，避免多个文件叠加
//     previewRef.current.innerHTML = "";

//     renderAsync(file, previewRef.current, null, {
//       className,
//       ignoreCss,
//     })
//       .then(() => console.log("DOCX 渲染成功"))
//       .catch((err) => console.error("DOCX 渲染失败:", err));
//   }, [file, className, ignoreCss]);

//   return <div ref={previewRef} style={style} />;
// };

// export default DocxPreview;

import { useEffect, useRef, forwardRef, useImperativeHandle } from "react";
import { renderAsync } from "docx-preview";

interface DocxPreviewProps {
  file: File | ArrayBuffer | Blob | null;
  className?: string;
  ignoreCss?: boolean;
  style?: React.CSSProperties;
  highlightWords?: string[];
  scrollToIndex?: number; // 新增：默认滚动到第几个高亮（0 开始）
  onHighlightsReady?: (ids: string[]) => void;
}

export interface DocxPreviewRef {
  scrollToText: (text: string) => void;
  highlightAndScroll: (text: string) => void;
  addHighlight: (text: string) => void;
  clearAllHighlights: () => void;
}

const DocxPreview = forwardRef<DocxPreviewRef, DocxPreviewProps>(
  (
    {
      file,
      className = "my-docx-preview",
      ignoreCss = false,
      style = { width: "100%", height: "400px", overflow: "auto" },
      highlightWords = [],
      scrollToIndex = 0,
      onHighlightsReady,
    },
    ref
  ) => {
    const previewRef = useRef<HTMLDivElement | null>(null);
    const highlightIdsRef = useRef<string[]>([]);

    // 暴露给父组件的方法
    useImperativeHandle(
      ref,
      () => ({
        scrollToText: (text: string) => {
          if (!previewRef.current) return;

          // 查找对应的高亮元素ID
          const targetId = highlightIdsRef.current.find((id) => {
            const element = document.getElementById(id);
            return (
              element &&
              element.textContent?.toLowerCase() === text.toLowerCase()
            );
          });

          if (targetId) {
            scrollToMark(previewRef.current, targetId);
          }
        },
        highlightAndScroll: (text: string) => {
          if (!previewRef.current) return;

          console.log("开始高亮文本:", text);
          // 先高亮文本
          const ids = highlightInDoc(previewRef.current, [text]);
          console.log("高亮结果IDs:", ids);

          if (ids.length > 0) {
            highlightIdsRef.current = [...highlightIdsRef.current, ...ids];
            // 滚动到新高亮的位置
            setTimeout(() => {
              console.log("开始滚动到:", ids[0]);
              scrollToMark(previewRef.current!, ids[0]);
            }, 200); // 增加延迟时间
          } else {
            console.log("没有找到匹配的文本:", text);
          }
        },
        addHighlight: (text: string) => {
          if (!previewRef.current) return;

          // 只高亮，不滚动
          const ids = highlightInDoc(previewRef.current, [text]);
          if (ids.length > 0) {
            highlightIdsRef.current = [...highlightIdsRef.current, ...ids];
          }
        },
        clearAllHighlights: () => {
          if (!previewRef.current) return;

          // 移除所有高亮标记
          highlightIdsRef.current.forEach((id) => {
            const element = document.getElementById(id);
            if (element && element.parentNode) {
              const textNode = document.createTextNode(
                element.textContent || ""
              );
              element.parentNode.replaceChild(textNode, element);
            }
          });
          highlightIdsRef.current = [];
        },
      }),
      []
    );

    useEffect(() => {
      if (!file || !previewRef.current) return;

      previewRef.current.innerHTML = "";
      highlightIdsRef.current = [];

      renderAsync(file, previewRef.current, null, { className, ignoreCss })
        .then(() => {
          console.log("DOCX 渲染成功");

          if (highlightWords.length > 0) {
            const ids = highlightInDoc(previewRef.current!, highlightWords);
            highlightIdsRef.current = ids;
            onHighlightsReady?.(ids);

            // 自动滚动到指定索引的高亮
            if (ids.length > 0) {
              const index = scrollToIndex ?? 0; // 默认 0
              const safeIndex = Math.min(Math.max(index, 0), ids.length - 1);
              setTimeout(() => {
                scrollToMark(previewRef.current!, ids[safeIndex]);
              }, 100); // 延迟一点确保DOM渲染完成
            }
          }
        })
        .catch((err) => console.error("DOCX 渲染失败:", err));
    }, [
      file,
      className,
      ignoreCss,
      highlightWords,
      scrollToIndex,
      onHighlightsReady,
    ]);

    return <div ref={previewRef} style={style} />;
  }
);

DocxPreview.displayName = "DocxPreview";

// --- 高亮工具函数 ---
function escapeRegExp(s: string) {
  return s.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
}

function highlightInDoc(container: HTMLElement, words: string[]): string[] {
  console.log("highlightInDoc 开始，查找词汇:", words);

  const walker = document.createTreeWalker(
    container,
    NodeFilter.SHOW_TEXT,
    null
  );

  // 为每个词创建更灵活的匹配模式
  const patterns = words.map((word) => {
    // 如果词汇很长（可能是文本片段），尝试匹配前几个词
    if (word.length > 20) {
      const firstWords = word.split(/\s+/).slice(0, 3).join("\\s+");
      return `(${escapeRegExp(firstWords)})`;
    } else {
      return `(${escapeRegExp(word)})`;
    }
  });

  const regex = new RegExp(patterns.join("|"), "gi");
  console.log("使用的正则表达式:", regex);

  let node: Node | null;
  const nodesToProcess: Text[] = [];
  const ids: string[] = [];
  let index = 0;

  // 先收集所有匹配的文本节点
  while ((node = walker.nextNode())) {
    if (node.nodeType === Node.TEXT_NODE) {
      const text = node.nodeValue || "";
      if (regex.test(text)) {
        console.log("找到匹配文本节点:", text.substring(0, 100));
        nodesToProcess.push(node as Text);
      }
    }
  }

  console.log("找到匹配节点数量:", nodesToProcess.length);

  nodesToProcess.forEach((textNode) => {
    const parent = textNode.parentNode;
    if (!parent) return;

    const originalText = textNode.nodeValue || "";
    console.log("处理文本节点:", originalText.substring(0, 50));

    const frag = document.createDocumentFragment();
    const parts = originalText.split(regex);
    console.log("分割后的部分:", parts);

    parts.forEach((part) => {
      if (!part) return frag.appendChild(document.createTextNode(""));

      // 更灵活的匹配逻辑
      const isMatch = words.some((w) => {
        if (w.length > 20) {
          // 对于长文本，检查是否包含前几个词
          const firstWords = w.split(/\s+/).slice(0, 3).join(" ");
          return (
            part.toLowerCase().includes(firstWords.toLowerCase()) ||
            firstWords.toLowerCase().includes(part.toLowerCase())
          );
        } else {
          return w.toLowerCase() === part.toLowerCase();
        }
      });

      console.log(`部分 "${part.substring(0, 30)}" 是否匹配:`, isMatch);

      if (isMatch) {
        const mark = document.createElement("mark");
        mark.textContent = part;
        mark.style.background = "yellow";
        mark.style.cursor = "pointer";

        const id = `highlight-${Date.now()}-${index++}`;
        mark.id = id;
        ids.push(id);

        console.log("创建高亮标记:", id, part.substring(0, 30));
        frag.appendChild(mark);
      } else {
        frag.appendChild(document.createTextNode(part));
      }
    });

    parent.replaceChild(frag, textNode);
  });

  console.log("高亮完成，返回IDs:", ids);
  return ids;
}

// --- 滚动 ---
function scrollToMark(container: HTMLElement, id: string) {
  const el = document.getElementById(id);
  if (!el) return;

  const rect = el.getBoundingClientRect();
  const containerRect = container.getBoundingClientRect();
  const offset = rect.top - containerRect.top;

  container.scrollTo({
    top: container.scrollTop + offset - container.clientHeight / 2,
    behavior: "smooth",
  });
}

export default DocxPreview;
