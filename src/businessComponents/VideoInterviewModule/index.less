.video-interview-module {
  width: 100%;
  height: 100%;
  // background: #f5f5f5;
  padding: 50px 20px 20px 20px;
  
  .interview-container {
    gap: 30px;
    display: flex;
    height: 100%;
    // min-height: 600px;
    
    .left-section {
      // flex: 1;
      width: 40%;
      background: white;
      border-radius: 8px;
      // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      
      .left-tabs {
        height: 100%;
        
        .ant-tabs-content-holder {
          height: calc(100% - 46px);
          overflow-y: auto;
        }
        
        .ant-tabs-tabpane {
          height: 100%;
          // padding: 20px;
        }
      }
    }
    
    .right-section {
      flex: 1;
      background: white;
      border-radius: 8px;
      // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      display: flex;
      flex-direction: column;
      
      .right-tabs {
        flex: 1; // Changed from height: 100%
      }

      .meeting-content {
        padding: 0 10px;
        height: 100%;
        overflow-y: auto;
        
        .component-wrapper {
          height: 100%;
          border-radius: 8px;
          .meeting-form{
            height: calc(100% - 90px);
            overflow: auto;
          }
          .host-info {
            margin-top: 10px;
          }
          
        }
        .iframe-container {
          height: 100%;
          border-radius: 8px;
          width: 100%;
          overflow: hidden;
          
          iframe {
            height: 115% !important;
            border-radius: 8px;
          }
        }
      }
      .meeting-content::-webkit-scrollbar {
        display: none;
      }

    }
  }
}

