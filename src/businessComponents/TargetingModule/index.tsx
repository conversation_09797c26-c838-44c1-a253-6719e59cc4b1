import {
  Button,
  Col,
  Row,
  Tabs,
  Card,
  Checkbox,
  Form,
  List,
  Space,
  Tag,
  Modal,
  theme,
  Flex,
  Result,
  Select,
  message,
  Radio,
} from "antd";
import {
  AppstoreOutlined,
  FileTextOutlined,
  FileWordOutlined,
  PlusOutlined,
  ReadOutlined,
} from "@ant-design/icons";
import { getToken, getUserInfo } from "@/utils/auth";
import { getItemDetail } from "@/api/knowledge";
import DocxPreview from "@/component/DocxViewer";
import StreamTypewriter from "@/component/StreamTypewriter";
import {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import "./index.less";
import useSSEChat from "@/hooks/useSSEChat";
interface FileItem {
  id: string;
  name: string;
  url: string;
}

interface Rule {
  id: number;
  title: string;
  riskLevel: string;
  source?: string; // 来源
}

interface MentionsComponentProps {
  agentId?: string;
  setGlobalLoading?: (loading: boolean) => void;
  onCallParent: (type: string, data?: any) => void; // 调用输入输出评审
  unitOutPutData?: any; // 父组件传过来的数据，用于回显跟判断有无输入输出通过
  unitInputData: {
    messages: string;
    chunks: any[]; // 切分后的数据
    originalFile: null; // 原文件
    fileList: FileItem[]; // 文件列表
    ruleFrom: any; // 规则数据
  };
}
export interface MentionsComponentRef {
  triggerSplit: (data: any) => void;
}

const riskLevelColors: Record<string, string> = {
  高风险: "red",
  中风险: "orange",
  低风险: "blue",
};
const { useToken } = theme;
const TargetingModule = forwardRef<
  MentionsComponentRef,
  MentionsComponentProps
>(
  (
    {
      unitInputData = {
        messages: "",
        fileList: [],
        chunks: [],
        ruleFrom: {},
        originalFile: null,
      },
      agentId,
      setGlobalLoading,
      onCallParent,
      unitOutPutData,
    },
    ref
  ) => {
    const sseChat = useSSEChat();
    const { token } = useToken();
    const [currentActiveKey, setCurrentActiveKey] = useState("2"); // 文件列表
    const [currentData, setCurrentData] = useState<any>(
      unitInputData?.fileList[0]
    ); // 当前选中的文件数据
    const [isCard, setIsCard] = useState(false); // 是否是卡片视图
    const [libSelectValue, setLibSelectValue] = useState<string[]>([]); // 选中的知识id
    const [ruleDbType, setRuleDbType] = useState<string>(""); // 选中的知识库类型
    const [libSelectName, setLibSelectName] = useState<string>(""); // 选中的知识库名称
    const [selectedRules, setSelectedRules] = useState<number[]>([]); // 选中的规则
    const [ruleList, setRuleList] = useState<any>([]); // 所有的规则数据
    const [isTargetModalOpen, setIsTargetModalOpen] = useState(false); // 新增目标的弹框
    const [knowledgeDataData, setKnowledgeDataData] = useState<any[]>([]); // 知识库数据
    const [targentList, setTargentList] = useState<any>([]); // 左侧目标数据
    // const [checkedTarget, setCheckedTarget] = useState<any>([]); // 选中的目标
    const [checkedItems, setCheckedItems] = useState<string[]>([]); // 选中的目标
    const [targentTitltList, setTargentTitltList] = useState<any>([]); // 目标所有块标题
    const titleList = useRef<any>([]); // 目标所有块标题
    const [previewModal, setPreviewModal] = useState<{
      // 目标详情弹框
      open: boolean;
      title: string;
      content: string;
    }>({ open: false, title: "", content: "" });
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [rules, setRules] = useState<Rule[]>([]);
    const [form] = Form.useForm();
    const [streamingText, setStreamingText] = useState<string>(""); // 流式输出的文本
    const [isStreaming, setIsStreaming] = useState<boolean>(false); // 是否正在流式输出
    const [showStreamContent, setShowStreamContent] = useState<boolean>(false); // 是否显示流式内容
    const scrollRef = useRef<HTMLDivElement>(null); // 滚动引用
    useEffect(() => {
      if (!unitOutPutData?.inputReview) {
        getRuleData("目标匹配");
        getRuleData("规则匹配");
        const arr: any = [];
        unitInputData?.chunks?.forEach((item: any) => {
          arr.push({ title: item.title });
        });
        titleList.current = arr;
        // 页面需要展示的规则
        if (unitInputData?.ruleFrom?.type_schema == "法审") {
          if (unitInputData?.ruleFrom?.ruleDbType == "自定义知识库") {
            const formattedLibName =
              unitInputData?.ruleFrom?.libSelectName?.replace(/,/g, "、");
            setRuleList([
              {
                title: "自定义知识库",
                content: formattedLibName,
                knowId: unitInputData?.ruleFrom?.libSelectValue,
              },
            ]);
          } else {
            setRuleList([
              {
                title: unitInputData?.ruleFrom?.ruleDbType,
              },
            ]);
          }
        } else if (unitInputData?.ruleFrom?.type_schema == "敏感词") {
          const formattedLibName =
            unitInputData?.ruleFrom?.libSelectName?.replace(/,/g, "、");
          setRuleList([
            {
              title: "敏感词",
              content: formattedLibName,
              knowId: unitInputData?.ruleFrom?.libSelectId,
            },
          ]);
        }
      } else {
        setRuleList(unitOutPutData?.ruleList || []);
        setTargentList(unitOutPutData?.targentData || []);
        setRules(unitOutPutData?.rulesData || []);
        setSelectedRules(unitOutPutData?.selectedRules || []);
        const targent: any = []; // 回显卡片头
        unitOutPutData?.targentData.forEach((item: any, index: number) => {
          targent.push({
            id: index + 1,
            title: item.title,
          });
        });
        console.log(unitInputData?.fileList[0]);
        setTargentTitltList(targent);
        setCurrentData(unitInputData?.fileList[0]);
      }
      getItemDetail({
        pageNum: 1,
        pageSize: 300000,
        entity: {
          title: "",
          libId:
            unitInputData?.ruleFrom?.stance == "甲方"
              ? "1948322515614875650"
              : "1962354481758691330",
          timeRange: "",
        },
      }).then((res) => {
        if (res.code === 200) {
          setKnowledgeDataData(res.data.records);
        }
      });
      if (unitInputData?.ruleFrom?.type_schema == "敏感词") {
        getItemDetail({
          pageNum: 1,
          pageSize: 300000,
          entity: {
            title: "",
            libId: "1962403075110932482",
            timeRange: "",
          },
        }).then((res) => {
          if (res.code === 200) {
            setKnowledgeDataData(res.data.records);
          }
        });
      }
    }, []);
    const getRuleData = async (
      type: string,
      ruleDbType?: string,
      RAG_Name?: string
    ) => {
      setGlobalLoading?.(true);
      const tokenInfo = await getToken();
      const userInfo = await getUserInfo();
      let params = {
        type_schema: unitInputData?.ruleFrom?.type_schema, // 模式分类
        contractType: unitInputData?.ruleFrom?.contractType, // 合同类型
        standpoint: unitInputData?.ruleFrom?.standpoint, // 立场视角
        ruleDbType:
          unitInputData?.ruleFrom?.type_schema == "法审"
            ? ruleDbType || unitInputData?.ruleFrom?.ruleDbType || ""
            : "自定义知识库", // 规则库类型
        key_points: unitInputData?.ruleFrom?.key_points, // 审查要点
        Scene_type: type, // 场景类型
        RAG_Name: RAG_Name || unitInputData?.ruleFrom?.libSelectName, // 知识库名称
        scence_description: "合同法审", // 场景描述
      };
      console.log(type, 9991, unitOutPutData?.inputReview);
      if (type == "规则匹配" && !unitOutPutData?.inputReview) {
        onCallParent("输入", JSON.stringify(params));
        return;
      }

      // 开始流式输出
      setIsStreaming(true);
      setShowStreamContent(true);
      setStreamingText("");

      sseChat.start({
        url: "/dify/broker/agent/stream",
        headers: {
          "Content-Type": "application/json",
          Token: tokenInfo || "",
        },
        body: {
          insId: "1",
          bizType: "app:agent",
          bizId: agentId || "",
          agentId: agentId || "",
          path: "/chat-messages",
          difyJson: {
            inputs: params,
            response_mode: "streaming",
            user: userInfo?.id || "anonymous",
            conversation_id: "",
            query: type == "规则匹配" ? "1" : titleList.current,
          },
        },
        query: {},
        onMessage: (text: string) => {
          // 流式更新文本
          if (type == "规则匹配") {
            setStreamingText(text);
          }
        },
        onFinished: (dataVal: any) => {
          console.log(dataVal, 2332);
          const resultData = dataVal
            .replace(/<think>[\s\S]*?<\/think>/g, "")
            .trim();

          // 检查是否为JSON格式，如果不是，延迟一段时间后再处理数据
          let isValidJSON = false;
          try {
            if (type == "规则匹配") {
              if (params.ruleDbType === "民法典") {
                JSON.parse(JSON.parse(resultData).result);
              } else {
                JSON.parse(resultData);
              }
            } else {
              JSON.parse(resultData);
            }
            isValidJSON = true;
          } catch (error) {
            isValidJSON = false;
          }

          // 如果不是JSON格式，延迟显示页面内容
          const processData = () => {
            if (type == "规则匹配") {
              console.log(888333111);
              const num = rules?.length || 0;
              const rulesData: any = [];
              try {
                if (params.ruleDbType === "民法典") {
                  console.log(JSON.parse(JSON.parse(resultData).result));
                  const result = JSON.parse(JSON.parse(resultData).result);
                  result.forEach((item: any, index: number) => {
                    rulesData.push({
                      id: num + index + 1,
                      title: item.rule_desc,
                      riskLevel: item.rule_level,
                      source: params.ruleDbType,
                    });
                  });
                } else {
                  JSON.parse(resultData).forEach((item: any, index: number) => {
                    rulesData.push({
                      id: num + index + 1,
                      title: item.rule_desc,
                      riskLevel: item.rule_level,
                      source: params.ruleDbType,
                    });
                  });
                }
              } catch (error) {
                console.log("json解析失败");
              }
              // 流式输出结束
              setIsStreaming(false);
              setShowStreamContent(false);
              setRules((prev) => [...prev, ...rulesData]);
              if (!unitOutPutData?.outputReview) {
                onCallParent("输出", JSON.stringify(rulesData));
              } else {
                setGlobalLoading?.(false);
              }
            } else {
              const targent: any = [];
              JSON.parse(resultData).forEach((item: any, index: number) => {
                targent.push({
                  id: index + 1,
                  title: item.title,
                });
              });
              setTargentTitltList(targent);
              // 拿到标题，将整个卡片的数据塞进去
              const selectList: any = [];
              const titles = targent.map((item) => item.title);
              unitInputData?.chunks?.forEach((item: any) => {
                if (titles.includes(item.title)) {
                  selectList.push(item);
                }
              });
              console.log(selectList);
              setTargentList(selectList);
              setGlobalLoading?.(false);
            }
          };

          if (isValidJSON) {
            // 如果是JSON格式，立即处理
            processData();
          } else {
            // 如果不是JSON格式，延迟2秒后处理，让用户看到代码块
            setTimeout(processData, 2000);
          }
        },
      });
    };
    // 新增规则
    const adjustRules = () => {
      setIsModalOpen(true);
      form.resetFields();
      setRuleDbType("");
      setLibSelectValue([]);
    };
    // 新增规则确定
    const handleOk = () => {
      console.log(unitInputData?.ruleFrom?.type_schema, 233131);
      if (unitInputData?.ruleFrom?.type_schema == "法审") {
        if (ruleDbType === "自定义知识库") {
          if (libSelectName) {
            setRuleList((prevData: any) =>
              prevData.map((item, index) => {
                if (index !== 0) return item;

                // 现有内容和 ID 数组
                const existingContent = item.content
                  ? item.content.split("、")
                  : [];
                const existingIds = item.knowId || [];

                // 新选择的值
                const newNames = libSelectName
                  .split(",")
                  .map((s) => s.trim())
                  .filter(Boolean);
                const newIds = libSelectValue; // 假设 libSelectValue 已是数组

                // 合并去重
                const mergedContent = Array.from(
                  new Set([...existingContent, ...newNames])
                );
                const mergedIds = Array.from(
                  new Set([...existingIds, ...newIds])
                );
                getRuleData("规则匹配", "自定义知识库", newNames.join(","));
                return {
                  ...item,
                  content: mergedContent.join("、"),
                  knowId: mergedIds,
                };
              })
            );
          }
        } else if (ruleDbType === "民法典") {
          if (!ruleList.some((item) => item.title === "民法典")) {
            setRuleList((prev) => [
              ...prev,
              {
                title: "民法典",
              },
            ]);
            getRuleData("规则匹配", "民法典", "");
          } else {
            message.error("民法典规则已存在");
          }
        } else if (ruleDbType === "AI生成") {
          if (!ruleList.some((item) => item.title === "AI生成")) {
            setRuleList((prev) => [
              ...prev,
              {
                title: "AI生成",
              },
            ]);
            getRuleData("规则匹配", "AI生成", "");
          } else {
            message.error("AI生成规则已存在");
          }
        }
        handleCancel();
      } else if (unitInputData?.ruleFrom?.type_schema == "敏感词") {
        if (libSelectName) {
          setRuleList((prevData: any) =>
            prevData.map((item, index) => {
              if (index !== 0) return item;

              // 现有内容和 ID 数组
              const existingContent = item.content
                ? item.content.split("、")
                : [];
              const existingIds = item.knowId || [];

              // 新选择的值
              const newNames = libSelectName
                .split(",")
                .map((s) => s.trim())
                .filter(Boolean);
              const newIds = libSelectValue; // 假设 libSelectValue 已是数组

              // 合并去重
              const mergedContent = Array.from(
                new Set([...existingContent, ...newNames])
              );
              const mergedIds = Array.from(
                new Set([...existingIds, ...newIds])
              );
              console.log(321);
              getRuleData("规则匹配", "", newNames.join(","));
              return {
                ...item,
                content: mergedContent.join("、"),
                knowId: mergedIds,
              };
            })
          );
        }
        handleCancel();
      }
    };

    // 新增规则取消
    const handleCancel = () => {
      setIsModalOpen(false);
    };

    // 规则库数据
    const handleSelectChange = (value: string[], options: any) => {
      setLibSelectValue(value);
      const labels = options.map((opt: any) => opt.label);
      const joinedLabels = labels.join(",");
      setLibSelectName(joinedLabels);
    };
    // 新增目标
    const addTargetModal = () => {
      // 将所有的目标的title统计出来
      const arr: any = [];
      targentList.forEach((item: any) => {
        arr.push(item.title);
      });
      console.log(arr, 123);
      setCheckedItems(arr);
      setIsTargetModalOpen(true);
    };
    // 目标复选框change
    const handleCheckboxChange = (checked: any, title: string) => {
      // 先记录
      setCheckedItems((prev) =>
        checked ? [...prev, title] : prev.filter((item) => item !== title)
      );
    };
    // 新增目标确定
    const handleTargetOk = () => {
      const arr: any = [];
      unitInputData?.chunks?.forEach((item: any) => {
        if (checkedItems.includes(item.title)) {
          arr.push(item);
        }
      });
      setTargentList(arr); // 赋值给左侧数据
    };
    // 新增目标取消
    const cancelTargetModal = () => {
      setIsTargetModalOpen(false);
    };
    // 吐出目标
    useImperativeHandle(ref, () => ({
      triggerSplit: async () => {
        return {
          targentData: targentList, // 目标的数据
          rulesData: rules, // 规则的数据
          ruleList: ruleList, // 所有的规则
          selectedRules: selectedRules, // 选中的规则
        };
      },
      getRuleData: () => {
        getRuleData("规则匹配");
      },
    }));
    return (
      <div style={{ height: "100%" }} className="targeting">
        <Row style={{ height: "100%" }}>
          <Col xs={24} md={12} className="targeting-file-info">
            <Tabs
              defaultActiveKey="0"
              activeKey={currentActiveKey}
              onChange={(e) => {
                setCurrentActiveKey(e);
                setCurrentData(unitInputData?.fileList[Number(e)]);
              }}
              className="targeting-file-list"
              items={unitInputData?.fileList.map((x, index) => ({
                key: index + "",
                label: x.name,
              }))}
            />
            <Flex vertical style={{ padding: "0px 40px 0px 50px" }}>
              <Flex
                gap={token.marginMD}
                className="targeting-file-type"
                align="center"
              >
                <Button
                  icon={<FileWordOutlined />}
                  style={{
                    borderColor: !isCard ? token.colorPrimary : "#F8FAFC",
                    background: !isCard ? token.colorPrimaryBg : "#F8FAFC",
                    color: !isCard ? token.colorPrimary : token.colorText,
                  }}
                  onClick={() => setIsCard(false)}
                >
                  Word视图
                </Button>
                <Button
                  icon={<AppstoreOutlined />}
                  style={{
                    borderColor: isCard ? token.colorPrimary : "#F8FAFC",
                    background: isCard ? token.colorPrimaryBg : "#F8FAFC",
                    color: isCard ? token.colorPrimary : token.colorText,
                  }}
                  onClick={() => setIsCard(true)}
                >
                  卡片视图
                </Button>
              </Flex>
              <Flex
                justify="space-between"
                style={{ marginTop: "15px", marginBottom: "13px" }}
              >
                <Flex gap={10} className="targeting-file-mb">
                  {targentTitltList?.map((item: any) => (
                    <Button>{item.title}</Button>
                  ))}
                </Flex>
                <Button icon={<PlusOutlined />} onClick={addTargetModal}>
                  新增目标
                </Button>
              </Flex>

              <div
                style={{
                  maxHeight: "calc(100vh - 270px)",
                  width: "100%",
                  overflow: "auto",
                }}
              >
                {isCard ? (
                  <Row
                    gutter={[16, 16]}
                    style={{
                      overflowY: "auto",
                    }}
                  >
                    {targentList?.length > 0 &&
                      targentList.map((x: any, index) => (
                        <Col span={24} key={index}>
                          <Card
                            title={x.title}
                            className="split-card"
                            extra={
                              <Button
                                type="link"
                                className="split-card-detail"
                                icon={<ReadOutlined />}
                                onClick={() =>
                                  setPreviewModal({
                                    open: true,
                                    title: x.title,
                                    content: x.text,
                                  })
                                }
                              >
                                查看详情
                              </Button>
                            }
                            style={{ width: "100%" }}
                          >
                            <div
                              style={{
                                display: "-webkit-box",
                                WebkitLineClamp: 5,
                                WebkitBoxOrient: "vertical",
                                overflow: "hidden",
                                textOverflow: "ellipsis",
                                whiteSpace: "normal",
                                minHeight: "110px",
                                lineHeight: token.lineHeight,
                              }}
                            >
                              {x.text}
                            </div>
                          </Card>
                        </Col>
                      ))}
                  </Row>
                ) : (
                  <DocxPreview
                    file={unitInputData?.originalFile}
                    className="my-split-docx-preview"
                  />
                )}
              </div>
            </Flex>
          </Col>
          <Col xs={24} md={12} className="targeting-right">
            <Flex
              className="targeting-right-header"
              gap={token.marginSM}
              vertical
            >
              <Flex justify="space-between" align="center">
                <Flex
                  style={{
                    fontSize: token.fontSizeXL,
                    fontWeight: "bold",
                    color: "#333",
                  }}
                  align="center"
                >
                  <Result status="success" />
                  规则与目标确认
                </Flex>
                <Flex
                  style={{
                    fontSize: token.fontSize,
                    color: "#333333",
                    lineHeight: token.lineHeight,
                  }}
                  gap={token.marginMD}
                >
                  <span>
                    当前立场：
                    <span style={{ color: token.colorPrimary }}>
                      {unitInputData?.ruleFrom?.stance || ""}
                    </span>
                  </span>
                  <span>
                    审查视角：
                    <span style={{ color: token.colorPrimary }}>
                      {unitInputData?.ruleFrom?.angleSelectValue || ""}
                    </span>
                  </span>
                  <span>
                    审核模式：
                    <span style={{ color: token.colorPrimary }}>
                      {unitInputData?.ruleFrom?.type_schema || ""}
                    </span>
                  </span>
                </Flex>
              </Flex>
              <Flex justify="space-between" align="center">
                <Flex
                  style={{
                    fontSize: token.fontSize,
                    color: "#333333",
                    lineHeight: token.lineHeight,
                    overflowX: "auto",
                    whiteSpace: "nowrap",
                    width: "calc(100% - 150px)",
                  }}
                  gap={token.marginXS}
                >
                  <FileTextOutlined />
                  审查清单：
                  <span>
                    {ruleList
                      ?.map((item) => {
                        if (item.content) {
                          const content = item.content.replace(/,/g, "、"); // 逗号替换成顿号
                          return `${item.title}-${content}`;
                        }
                        return item.title;
                      })
                      .join("、")}
                  </span>
                </Flex>
                <Button icon={<PlusOutlined />} onClick={adjustRules}>
                  新增规则
                </Button>
              </Flex>
            </Flex>
            <div className="scroll-container">
              <Flex vertical>
                <div
                  style={{
                    marginTop: token.marginMD,
                    marginBottom: token.marginXS,
                  }}
                >
                  <Space>
                    <Checkbox
                      indeterminate={
                        selectedRules.length > 0 &&
                        selectedRules.length < rules?.length
                      }
                      checked={
                        selectedRules.length === rules?.length &&
                        rules?.length > 0
                      }
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedRules(rules?.map((rule) => rule.id));
                        } else {
                          setSelectedRules([]);
                        }
                      }}
                    >
                      全部规则 ({rules?.length}条)
                    </Checkbox>

                    <Checkbox
                      indeterminate={
                        selectedRules.filter((id) =>
                          rules?.find(
                            (rule) =>
                              rule.id === id && rule.riskLevel === "高风险"
                          )
                        ).length > 0 &&
                        selectedRules.filter((id) =>
                          rules?.find(
                            (rule) =>
                              rule.id === id && rule.riskLevel === "高风险"
                          )
                        ).length <
                          rules?.filter((rule) => rule.riskLevel === "高风险")
                            .length
                      }
                      checked={
                        selectedRules.filter((id) =>
                          rules?.find(
                            (rule) =>
                              rule.id === id && rule.riskLevel === "高风险"
                          )
                        ).length ===
                          rules?.filter((rule) => rule.riskLevel === "高风险")
                            .length &&
                        selectedRules.filter((id) =>
                          rules?.find(
                            (rule) =>
                              rule.id === id && rule.riskLevel === "高风险"
                          )
                        ).length > 0
                      }
                      onChange={(e) => {
                        const highRiskRules = rules
                          ?.filter((rule) => rule.riskLevel === "高风险")
                          .map((rule) => rule.id);
                        if (e.target.checked) {
                          setSelectedRules([
                            ...new Set([...selectedRules, ...highRiskRules]),
                          ]);
                        } else {
                          setSelectedRules(
                            selectedRules.filter(
                              (id) => !highRiskRules.includes(id)
                            )
                          );
                        }
                      }}
                    >
                      高风险
                    </Checkbox>

                    {/* 中风险复选框 */}
                    <Checkbox
                      indeterminate={
                        selectedRules.filter((id) =>
                          rules?.find(
                            (rule) =>
                              rule.id === id && rule.riskLevel === "中风险"
                          )
                        ).length > 0 &&
                        selectedRules.filter((id) =>
                          rules?.find(
                            (rule) =>
                              rule.id === id && rule.riskLevel === "中风险"
                          )
                        ).length <
                          rules?.filter((rule) => rule.riskLevel === "中风险")
                            .length
                      }
                      checked={
                        selectedRules.filter((id) =>
                          rules?.find(
                            (rule) =>
                              rule.id === id && rule.riskLevel === "中风险"
                          )
                        ).length > 0 &&
                        selectedRules.filter((id) =>
                          rules?.find(
                            (rule) =>
                              rule.id === id && rule.riskLevel === "中风险"
                          )
                        ).length ===
                          rules?.filter((rule) => rule.riskLevel === "中风险")
                            .length
                      }
                      onChange={(e) => {
                        const midRiskRules = rules
                          ?.filter((rule) => rule.riskLevel === "中风险")
                          .map((rule) => rule.id);
                        if (e.target.checked) {
                          // 全选中风险
                          setSelectedRules([
                            ...new Set([...selectedRules, ...midRiskRules]),
                          ]);
                        } else {
                          // 取消全选中风险
                          setSelectedRules(
                            selectedRules.filter(
                              (id) => !midRiskRules.includes(id)
                            )
                          );
                        }
                      }}
                    >
                      中风险
                    </Checkbox>

                    {/* 低风险复选框 */}
                    <Checkbox
                      indeterminate={
                        selectedRules.filter((id) =>
                          rules?.find(
                            (rule) =>
                              rule.id === id && rule.riskLevel === "低风险"
                          )
                        ).length > 0 &&
                        selectedRules.filter((id) =>
                          rules?.find(
                            (rule) =>
                              rule.id === id && rule.riskLevel === "低风险"
                          )
                        ).length <
                          rules?.filter((rule) => rule.riskLevel === "低风险")
                            .length
                      }
                      checked={
                        selectedRules.filter((id) =>
                          rules?.find(
                            (rule) =>
                              rule.id === id && rule.riskLevel === "低风险"
                          )
                        ).length > 0 &&
                        selectedRules.filter((id) =>
                          rules?.find(
                            (rule) =>
                              rule.id === id && rule.riskLevel === "低风险"
                          )
                        ).length ===
                          rules?.filter((rule) => rule.riskLevel === "低风险")
                            .length
                      }
                      onChange={(e) => {
                        const lowRiskRules = rules
                          ?.filter((rule) => rule.riskLevel === "低风险")
                          .map((rule) => rule.id);
                        if (e.target.checked) {
                          // 全选低风险
                          setSelectedRules([
                            ...new Set([...selectedRules, ...lowRiskRules]),
                          ]);
                        } else {
                          // 取消全选低风险
                          setSelectedRules(
                            selectedRules.filter(
                              (id) => !lowRiskRules.includes(id)
                            )
                          );
                        }
                      }}
                    >
                      低风险
                    </Checkbox>
                    {/* 民法典复选框 */}
                    <Checkbox
                      indeterminate={
                        selectedRules.filter((id) =>
                          rules.find(
                            (rule) => rule.id === id && rule.source === "民法典"
                          )
                        ).length > 0 &&
                        selectedRules.filter((id) =>
                          rules.find(
                            (rule) => rule.id === id && rule.source === "民法典"
                          )
                        ).length <
                          rules.filter((rule) => rule.source === "民法典")
                            .length
                      }
                      checked={
                        selectedRules.filter((id) =>
                          rules.find(
                            (rule) => rule.id === id && rule.source === "民法典"
                          )
                        ).length > 0 &&
                        selectedRules.filter((id) =>
                          rules.find(
                            (rule) => rule.id === id && rule.source === "民法典"
                          )
                        ).length ===
                          rules.filter((rule) => rule.source === "民法典")
                            .length
                      }
                      onChange={(e) => {
                        const lowRiskRules = rules
                          .filter((rule) => rule.source === "民法典")
                          .map((rule) => rule.id);
                        if (e.target.checked) {
                          // 全选民法典
                          setSelectedRules([
                            ...new Set([...selectedRules, ...lowRiskRules]),
                          ]);
                        } else {
                          // 取消全选民法典
                          setSelectedRules(
                            selectedRules.filter(
                              (id) => !lowRiskRules.includes(id)
                            )
                          );
                        }
                      }}
                    >
                      民法典
                    </Checkbox>
                    {/* 用户自定义复选框 */}
                    <Checkbox
                      indeterminate={
                        selectedRules.filter((id) =>
                          rules.find(
                            (rule) =>
                              rule.id === id && rule.source === "自定义知识库"
                          )
                        ).length > 0 &&
                        selectedRules.filter((id) =>
                          rules.find(
                            (rule) =>
                              rule.id === id && rule.source === "自定义知识库"
                          )
                        ).length <
                          rules.filter((rule) => rule.source === "自定义知识库")
                            .length
                      }
                      checked={
                        selectedRules.filter((id) =>
                          rules.find(
                            (rule) =>
                              rule.id === id && rule.source === "自定义知识库"
                          )
                        ).length > 0 &&
                        selectedRules.filter((id) =>
                          rules.find(
                            (rule) =>
                              rule.id === id && rule.source === "自定义知识库"
                          )
                        ).length ===
                          rules.filter((rule) => rule.source === "自定义知识库")
                            .length
                      }
                      onChange={(e) => {
                        const lowRiskRules = rules
                          .filter((rule) => rule.source === "自定义知识库")
                          .map((rule) => rule.id);
                        if (e.target.checked) {
                          // 全选用户自定义
                          setSelectedRules([
                            ...new Set([...selectedRules, ...lowRiskRules]),
                          ]);
                        } else {
                          // 取消全选用户自定义
                          setSelectedRules(
                            selectedRules.filter(
                              (id) => !lowRiskRules.includes(id)
                            )
                          );
                        }
                      }}
                    >
                      用户自定义
                    </Checkbox>
                    {/* AI生成复选框 */}
                    <Checkbox
                      indeterminate={
                        selectedRules.filter((id) =>
                          rules.find(
                            (rule) =>
                              rule.id === id && rule.riskLevel === "AI生成"
                          )
                        ).length > 0 &&
                        selectedRules.filter((id) =>
                          rules.find(
                            (rule) =>
                              rule.id === id && rule.riskLevel === "AI生成"
                          )
                        ).length <
                          rules.filter((rule) => rule.riskLevel === "AI生成")
                            .length
                      }
                      checked={
                        selectedRules.filter((id) =>
                          rules.find(
                            (rule) =>
                              rule.id === id && rule.riskLevel === "AI生成"
                          )
                        ).length > 0 &&
                        selectedRules.filter((id) =>
                          rules.find(
                            (rule) =>
                              rule.id === id && rule.riskLevel === "AI生成"
                          )
                        ).length ===
                          rules.filter((rule) => rule.riskLevel === "AI生成")
                            .length
                      }
                      onChange={(e) => {
                        const lowRiskRules = rules
                          .filter((rule) => rule.riskLevel === "AI生成")
                          .map((rule) => rule.id);
                        if (e.target.checked) {
                          // 全选用户AI生成
                          setSelectedRules([
                            ...new Set([...selectedRules, ...lowRiskRules]),
                          ]);
                        } else {
                          // 取消全选AI生成
                          setSelectedRules(
                            selectedRules.filter(
                              (id) => !lowRiskRules.includes(id)
                            )
                          );
                        }
                      }}
                    >
                      AI生成
                    </Checkbox>
                  </Space>
                </div>

                {/* 流式输出显示 */}
                {showStreamContent && (
                  <div
                    ref={scrollRef}
                    style={{
                      marginBottom: token.marginLG,
                      maxHeight: "calc(100vh - 300px)",
                      overflow: "auto",
                    }}
                  >
                    {rules && rules.length > 0 && (
                      <List
                        itemLayout="horizontal"
                        dataSource={rules}
                        renderItem={(item) => (
                          <List.Item>
                            <List.Item.Meta
                              avatar={
                                <Flex vertical gap={token.marginXS}>
                                  <Flex align="center">
                                    <Checkbox
                                      checked={selectedRules.includes(item.id)}
                                      onChange={(e) => {
                                        if (e.target.checked) {
                                          setSelectedRules([
                                            ...selectedRules,
                                            item.id,
                                          ]);
                                        } else {
                                          setSelectedRules(
                                            selectedRules.filter(
                                              (id) => id !== item.id
                                            )
                                          );
                                        }
                                      }}
                                      style={{ marginRight: "8px" }}
                                    />
                                    <span
                                      style={{
                                        color: token.colorText,
                                        fontWeight: 600,
                                      }}
                                    >
                                      {item.id}. {item.title}
                                    </span>
                                  </Flex>
                                  <Flex style={{ marginLeft: "24px" }}>
                                    <Tag
                                      color={riskLevelColors[item.riskLevel]}
                                    >
                                      {item.riskLevel}
                                    </Tag>
                                    {item?.source && (
                                      <Tag
                                        style={{
                                          color: token.colorPrimary,
                                          borderColor:
                                            token.colorPrimaryBgHover,
                                        }}
                                      >
                                        {item?.source}
                                      </Tag>
                                    )}
                                  </Flex>
                                </Flex>
                              }
                            />
                          </List.Item>
                        )}
                      />
                    )}
                    <StreamTypewriter
                      text={streamingText}
                      end={!isStreaming}
                      charsPerUpdate={5}
                      onchange={() => {
                        // 自动滚动到底部
                        if (scrollRef.current) {
                          scrollRef.current.scrollTop =
                            scrollRef.current.scrollHeight;
                        }
                      }}
                    />
                  </div>
                )}

                {/* 规则列表 */}
                {!showStreamContent && (
                  <List
                    itemLayout="horizontal"
                    dataSource={rules}
                    className="targeting-list-scroll"
                    renderItem={(item) => (
                      <List.Item>
                        <List.Item.Meta
                          avatar={
                            <Flex vertical gap={token.marginXS}>
                              <Flex align="center">
                                <Checkbox
                                  checked={selectedRules.includes(item.id)}
                                  onChange={(e) => {
                                    if (e.target.checked) {
                                      setSelectedRules([
                                        ...selectedRules,
                                        item.id,
                                      ]);
                                    } else {
                                      setSelectedRules(
                                        selectedRules.filter(
                                          (id) => id !== item.id
                                        )
                                      );
                                    }
                                  }}
                                  style={{ marginRight: "8px" }}
                                />
                                <span
                                  style={{
                                    color: token.colorText,
                                    fontWeight: 600,
                                  }}
                                >
                                  {item.id}. {item.title}
                                </span>
                              </Flex>
                              <Flex style={{ marginLeft: "24px" }}>
                                <Tag color={riskLevelColors[item.riskLevel]}>
                                  {item.riskLevel}
                                </Tag>
                                {item?.source && (
                                  <Tag
                                    style={{
                                      color: token.colorPrimary,
                                      borderColor: token.colorPrimaryBgHover,
                                    }}
                                  >
                                    {item?.source}
                                  </Tag>
                                )}
                              </Flex>
                            </Flex>
                          }
                        />
                      </List.Item>
                    )}
                  />
                )}
              </Flex>
            </div>
          </Col>
        </Row>
        <Modal
          title="新增规则"
          maskClosable={false}
          open={isModalOpen}
          onOk={handleOk}
          onCancel={handleCancel}
        >
          <Form
            layout="vertical"
            form={form}
            initialValues={{ layout: "vertical" }}
            style={{ maxWidth: 600 }}
          >
            {unitInputData?.ruleFrom?.type_schema === "法审" ? (
              <>
                <Form.Item
                  name="ruleDbType"
                  rules={[{ message: "请选择规则库类型" }]}
                >
                  <Radio.Group
                    options={[
                      {
                        value: "自定义知识库",
                        label: "自定义知识库",
                        disabled: false, // 自定义知识库始终可选
                      },
                      {
                        value: "民法典",
                        label: "民法典",
                        disabled: ruleList.some(
                          (item) => item.title === "民法典"
                        ), // 如果已经有，禁用
                      },
                      {
                        value: "AI生成",
                        label: "AI生成",
                        disabled: ruleList.some(
                          (item) => item.title === "AI生成"
                        ), // 如果已经有，禁用
                      },
                    ]}
                    value={ruleDbType}
                    onChange={(e) => {
                      setRuleDbType(e.target.value);
                      setLibSelectValue([]);
                    }}
                  />
                </Form.Item>
                {ruleDbType === "自定义知识库" && (
                  <div>
                    <Select
                      mode="multiple"
                      style={{ width: "100%", marginTop: -20 }}
                      placeholder="请输入关键词筛选内部规则"
                      value={libSelectValue}
                      onChange={handleSelectChange}
                      showSearch
                      filterOption={(input, option) =>
                        option?.label
                          .toLowerCase()
                          .includes(input.toLowerCase())
                      }
                      options={knowledgeDataData.map((item) => ({
                        value: item.id,
                        label: item.title,
                        disabled: ruleList
                          .filter((rule) => rule.title === "自定义知识库") // 找出已有的知识库项
                          .some((rule) => rule.knowId?.includes(item.id)), // 如果 knowId 中包含这个 id，则禁用
                      }))}
                    />
                  </div>
                )}
              </>
            ) : (
              <Form.Item label="敏感词规则库">
                <Select
                  mode="multiple"
                  style={{ width: "100%", marginTop: -20 }}
                  placeholder="请选择内部规则"
                  value={libSelectValue}
                  onChange={handleSelectChange}
                  options={knowledgeDataData.map((item) => ({
                    value: item.id,
                    label: item.title,
                    disabled: ruleList
                      .filter((rule) => rule.title === "自定义知识库") // 找出已有的知识库项
                      .some((rule) => rule.knowId?.includes(item.id)), // 如果 knowId 中包含这个 id，则禁用
                  }))}
                />
              </Form.Item>
            )}
          </Form>
        </Modal>
        <Modal
          title="新增目标"
          maskClosable={false}
          open={isTargetModalOpen}
          onOk={handleTargetOk}
          width={1000}
          className="targent-split-file-modal"
          onCancel={cancelTargetModal}
        >
          <div>
            <Row
              gutter={[16, 16]}
              style={{
                maxHeight: "65vh",
                overflowY: "auto",
                paddingTop: "8px",
              }}
            >
              {unitInputData?.chunks?.length > 0 &&
                unitInputData?.chunks?.map((x: any, index) => (
                  <Col span={12} key={index}>
                    <Card
                      title={
                        <div
                          style={{
                            display: "flex",
                            alignItems: "center",
                            gap: token.marginXS,
                          }}
                        >
                          <Checkbox
                            disabled={checkedItems.includes(x.title)}
                            checked={checkedItems.includes(x.title)}
                            onChange={(e) =>
                              handleCheckboxChange(e.target.checked, x.title)
                            }
                          />
                          <span>{x.title}</span>
                        </div>
                      }
                      className={`split-card ${
                        checkedItems.includes(x.title) ? "active-card" : ""
                      }`}
                      extra={
                        <Button
                          type="link"
                          className="split-card-detail"
                          icon={<ReadOutlined />}
                          onClick={() =>
                            setPreviewModal({
                              open: true,
                              title: x.title,
                              content: x.text,
                            })
                          }
                        >
                          查看详情
                        </Button>
                      }
                      style={{ width: "100%" }}
                    >
                      <div
                        style={{
                          display: "-webkit-box",
                          WebkitLineClamp: 5,
                          WebkitBoxOrient: "vertical",
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          whiteSpace: "normal",
                          minHeight: "110px",
                          lineHeight: token.lineHeight,
                        }}
                      >
                        {x.text}
                      </div>
                    </Card>
                  </Col>
                ))}
            </Row>
          </div>
        </Modal>
        <Modal
          open={previewModal.open}
          title={previewModal.title}
          footer={null}
          className="targenting-split-file-detail"
          onCancel={() =>
            setPreviewModal({ open: false, title: "", content: "" })
          }
          style={{ top: "15vh", width: "60vw" }}
          width="70vw"
        >
          <div style={{ whiteSpace: "pre-wrap" }}>
            {(previewModal.content || "").replace(/\n{3,}/g, "\n\n")}
          </div>
        </Modal>
      </div>
    );
  }
);
// 添加 displayName
TargetingModule.displayName = "SplitPreviewModule";
export default TargetingModule;
