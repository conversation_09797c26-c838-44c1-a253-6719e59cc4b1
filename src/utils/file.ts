
export const getFileType = (filename: string) => {
  const mimeType = filename.split('.').pop()
  switch (mimeType) {
    case 'docx':
      return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    case 'txt':
      return 'text/plain'
    case 'csv':
      return 'text/csv'
    case 'xls':
      return 'application/vnd.ms-excel'
    case 'pptx':
      return 'application/vnd.openxmlformats-officedocument.presentationml.presentation'
    case 'pdf':
      return 'application/pdf'
    case 'doc':
      return 'application/msword'
    case 'ppt':
      return 'application/vnd.ms-powerpoint'
    case 'xlsm':
      return 'application/vnd.ms-excel.sheet.macroEnabled.12'
    case 'xlsx':
      return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    case 'xlsb':
      return 'application/vnd.ms-excel.sheet.binary.macroEnabled.12'

    default:
      return 'text/plain'
  }
}