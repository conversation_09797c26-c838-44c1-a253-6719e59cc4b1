import { message } from "antd";
import { addKnowledge, addNoteKnowledge } from "@/api/knowledge"; // 假设你的 API

// 封装成公共方法 存入知识库
export const saveToKnowledgeBase = async (
  pageName: string,
  pageDesc: string,
  title: string,
  content: any
): Promise<boolean> => {
  try {
    // 先创建知识库
    const libRes = await addKnowledge({
      libName: pageName,
      libType: "TEXT_LIBRARY",
      libDesc: pageDesc,
    });

    if (libRes.code !== 200) {
      message.error("创建知识库失败");
      return false;
    }

    // 添加笔记
    const noteRes = await addNoteKnowledge({
      libId: libRes.data,
      title: title || "",
      content: content || "",
    });

    if (noteRes.code === 200) {
      message.success("存入知识库成功");
      return true;
    } else {
      message.error("存入知识库失败");
      return false;
    }
  } catch (error) {
    console.error(error);
    message.error("请求出错");
    return false;
  }
};
