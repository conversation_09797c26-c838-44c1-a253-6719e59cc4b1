import React, { createContext, useContext, useMemo, useState } from 'react'
import type { ReactNode } from 'react'

// 定义全局参数的类型
interface GlobalContextType {
  mainWinType: string
  setMainWinType: (value: string) => void
  splitterLeftSize: number
  setsplitterLeftSize: (value: number) => void
  collapse: boolean
  setCollapse: (value: boolean) => void
}

// 创建 Context
const GlobalContext = createContext<GlobalContextType | undefined>(undefined)

// 创建 Provider 组件
export const GlobalProvider: React.FC<{ children: ReactNode; defaultMainWinType?: string }> = ({
  children,
  defaultMainWinType = '1'
}) => {
  const [mainWinType, setMainWinType] = useState<string>(defaultMainWinType)
  const [splitterLeftSize, setsplitterLeftSize] = useState(160)
  const [collapse, setCollapse] = useState(false) // 是否折叠
  const contextValue = useMemo(
    () => ({
      mainWinType,
      setMainWinType,
      splitterLeftSize,
      setsplitterLeftSize,
      collapse,
      setCollapse
    }),
    [mainWinType, setMainWinType, splitterLeftSize, setsplitterLeftSize, collapse, setCollapse]
  )
  return <GlobalContext.Provider value={contextValue}>{children}</GlobalContext.Provider>
}

// 创建一个自定义 Hook 来使用
export function useGlobalContext(): GlobalContextType {
  const context = useContext(GlobalContext)
  if (!context) {
    throw new Error('useGlobalContext must be used within a GlobalProvider')
  }
  return context
}
